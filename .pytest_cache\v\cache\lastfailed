{"tests/test_integration.py::TestLearningHubIntegration": true, "tests/test_integration.py::TestConstellationIntegration": true, "tests/test_integration.py::TestEndToEndLearningSession": true, "tests/test_integration.py::TestConfigurationIntegration": true, "tests/test_integration.py::TestCLIIntegration": true, "tests/test_agents.py::TestBaseAgent::test_base_agent_initialization": true, "tests/test_agents.py::TestBaseAgent::test_base_agent_invoke": true, "tests/test_agents.py::TestBaseAgent::test_confidence_score_calculation": true, "tests/test_agents.py::TestSpecializedAgents::test_instructor_agent_confidence": true, "tests/test_agents.py::TestSpecializedAgents::test_troubleshooter_confidence": true, "tests/test_agents.py::TestAgentHandoffs::test_code_assistant_to_troubleshooter_handoff": true, "tests/test_agents.py::TestAgentFactory::test_create_all_agent_types": true, "tests/test_agents.py::TestAgentFactory::test_invalid_agent_type": true, "tests/test_core_systems.py::TestFrameworkDatabase::test_singleton_pattern": true, "tests/test_core_systems.py::TestFrameworkDatabase::test_get_module": true, "tests/test_core_systems.py::TestFrameworkDatabase::test_cache_functionality": true, "tests/test_core_systems.py::TestFrameworkDatabase::test_database_statistics": true, "tests/test_integration.py::TestLearningHubIntegration::test_user_profile_management": true, "tests/test_integration.py::TestLearningHubIntegration::test_session_creation_and_management": true, "tests/test_integration.py::TestLearningHubIntegration::test_message_processing": true, "tests/test_integration.py::TestLearningHubIntegration::test_learning_progress_tracking": true, "tests/test_integration.py::TestLearningHubIntegration::test_session_ending": true, "tests/test_integration.py::TestConstellationIntegration::test_constellation_creation": true, "tests/test_integration.py::TestConstellationIntegration::test_agent_selection_and_handoff": true, "tests/test_integration.py::TestConstellationIntegration::test_constellation_adaptation": true, "tests/test_integration.py::TestEndToEndLearningSession::test_complete_learning_session": true, "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_with_tools_initialization": true, "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_with_memory_initialization": true, "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_reset_memory": true, "tests/test_vinagent_agent.py::TestAgentInvocation::test_invoke_with_user_id": true, "tests/test_vinagent_agent.py::TestAgentInvocation::test_async_invoke_with_user_id": true, "tests/test_vinagent_agent.py::TestAgentInvocation::test_invoke_with_memory_save": true, "tests/test_vinagent_agent.py::TestAgentToolIntegration::test_tool_execution_sync": true, "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_agent_with_invalid_tools_path": true, "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_agent_with_invalid_memory_path": true, "tests/test_vinagent_agent.py::TestAgentMemoryIntegration::test_memory_save_and_load": true, "tests/test_vinagent_agent.py::TestAgentMemoryIntegration::test_memory_persistence": true, "tests/test_end_to_end_scenarios.py::TestBasicAgentWorkflow::test_agent_with_memory_persistence": true, "tests/test_integration_components.py::TestCrossModuleInteractions::test_agent_memory_tool_integration": true, "tests/test_error_handling_edge_cases.py::TestMemoryErrorHandling::test_memory_with_corrupted_file": true, "tests/test_end_to_end_scenarios.py::TestBasicAgentWorkflow": true, "tests/test_end_to_end_scenarios.py::TestToolIntegrationWorkflow": true, "tests/test_end_to_end_scenarios.py::TestErrorHandlingScenarios": true, "tests/test_end_to_end_scenarios.py::TestGAAPFIntegration": true, "tests/test_end_to_end_scenarios.py::TestPerformanceScenarios": true, "tests/test_integration_components.py::TestCrossModuleInteractions::test_memory_persistence_across_agents": true, "tests/test_integration_components.py::TestCrossModuleInteractions::test_error_propagation_across_modules": true, "tests/test_end_to_end_scenarios.py::TestErrorHandlingScenarios::test_agent_with_corrupted_memory_file": true}