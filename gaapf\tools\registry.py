"""
Tool registry for GAAPF.

This module provides a registry for all available tools in the GAAPF system.
"""

from typing import Dict, List, Optional, Any, Type
from langchain_core.tools import BaseTool

from gaapf.tools.web_search import WebSearchTool


class ToolRegistry:
    """
    Registry for GAAPF tools.
    
    Provides access to all available tools in the system.
    """
    
    def __init__(self):
        """Initialize the tool registry."""
        self._tools = {}
        self._register_default_tools()
        
    def _register_default_tools(self):
        """Register default tools."""
        self.register_tool("web_search", WebSearchTool)
        
    def register_tool(self, name: str, tool_cls: Type[BaseTool]):
        """
        Register a tool.
        
        Args:
            name: Tool name
            tool_cls: Tool class
        """
        self._tools[name] = tool_cls
        
    def get_tool(self, name: str) -> Optional[Type[BaseTool]]:
        """
        Get a tool by name.
        
        Args:
            name: Tool name
            
        Returns:
            Tool class if found, None otherwise
        """
        return self._tools.get(name)
        
    def create_tool(self, name: str, **kwargs) -> Optional[BaseTool]:
        """
        Create a tool instance.
        
        Args:
            name: Tool name
            **kwargs: Tool initialization arguments
            
        Returns:
            Tool instance if found, None otherwise
        """
        tool_cls = self.get_tool(name)
        if tool_cls:
            return tool_cls(**kwargs)
        return None
        
    def list_tools(self) -> List[str]:
        """
        List all registered tools.
        
        Returns:
            List of tool names
        """
        return list(self._tools.keys())
        
    def create_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """
        Create tools for a specific agent type.
        
        Args:
            agent_type: Agent type
            
        Returns:
            List of tool instances
        """
        # Define tool configurations for each agent type
        agent_tools = {
            "instructor": ["web_search", "documentation_lookup"],
            "code_assistant": ["web_search", "code_generation", "code_explanation"],
            "documentation_expert": ["documentation_lookup", "web_search", "code_explanation"],
            "mentor": ["progress_tracking", "learning_path_recommendation"],
            "practice_facilitator": ["exercise_generation", "code_evaluation"],
            "knowledge_synthesizer": ["web_search", "documentation_lookup", "concept_mapping"],
            "research_assistant": ["web_search", "documentation_lookup", "information_extraction"],
            "troubleshooter": ["code_debugging", "error_analysis", "web_search"],
            "project_guide": ["project_planning", "architecture_recommendation", "web_search"]
        }
        
        # Get tool names for this agent type
        tool_names = agent_tools.get(agent_type, ["web_search"])
        
        # Create tool instances
        tools = []
        for name in tool_names:
            tool = self.create_tool(name)
            if tool:
                tools.append(tool)
                
        return tools


# Create a global instance
registry = ToolRegistry() 