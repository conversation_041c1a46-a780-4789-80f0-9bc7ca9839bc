# Testing dependencies for GAAPF
# Install with: pip install -r requirements-test.txt

# Core testing framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0  # For parallel test execution

# Test utilities
factory-boy>=3.3.0  # For creating test data
faker>=19.0.0  # For generating fake data
freezegun>=1.2.0  # For mocking datetime
responses>=0.23.0  # For mocking HTTP requests

# Performance testing
pytest-benchmark>=4.0.0
memory-profiler>=0.61.0

# Code quality and linting (for CI/CD)
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0

# Documentation testing
pytest-doctestplus>=1.0.0

# Test reporting
pytest-html>=3.2.0
pytest-json-report>=1.5.0

# Coverage reporting
coverage[toml]>=7.3.0

# Mock and testing utilities
unittest-mock>=1.0.1
testfixtures>=7.2.0

# For testing CLI applications
click-testing>=0.1.0

# For testing async code
aioresponses>=0.7.4
pytest-timeout>=2.1.0

# Database testing utilities (if needed)
pytest-postgresql>=5.0.0
pytest-sqlite>=0.1.0

# For testing with temporary files and directories
pytest-tmp-path>=0.1.0

# Environment and configuration testing
python-dotenv>=1.0.0  # For testing .env file loading

# For testing rich console output
pytest-console-scripts>=1.4.0

# Memory and resource testing
psutil>=5.9.0

# For testing network-related functionality
pytest-httpserver>=1.0.8

# JSON schema validation for testing
jsonschema>=4.19.0

# For testing with different Python versions (if using tox)
tox>=4.11.0

# For testing documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# For testing notebooks (if any)
nbval>=0.10.0

# For property-based testing
hypothesis>=6.82.0

# For testing with fixtures and data
pytest-datadir>=1.4.1
pytest-datafiles>=3.0.0

# For testing logging
pytest-logging>=2016.11.4

# For testing with different environments
pytest-env>=0.8.2

# For testing with markers and metadata
pytest-metadata>=3.0.0

# For testing with custom plugins
pluggy>=1.3.0

# For testing subprocess calls
pytest-subprocess>=1.5.0

# For testing with temporary servers
pytest-localserver>=0.7.1

# For testing with fixtures that need cleanup
pytest-fixture-config>=1.7.0

# For testing with parameterized tests
pytest-cases>=3.6.14

# For testing with retry logic
pytest-rerunfailures>=12.0

# For testing with warnings
pytest-warnings>=0.3.1

# For testing with custom assertions
pytest-check>=2.2.2

# For testing with step-by-step debugging
pytest-steps>=1.8.0

# For testing with custom ordering
pytest-order>=1.1.0

# For testing with dependency injection
pytest-dependency>=0.5.1

# For testing with custom fixtures
pytest-lazy-fixture>=0.6.3

# For testing with custom markers
pytest-custom-exit-code>=0.3.0
