# GAAPF API Reference

This document provides comprehensive API reference for all GAAPF components and their public interfaces.

## 🏗️ Core Components

### LearningHubCore

The central orchestration component for GAAPF learning sessions.

```python
from gaapf.core.learning_hub import LearningHubCore
```

#### Constructor

```python
LearningHubCore(data_path: Path = Path("data"))
```

**Parameters:**
- `data_path` (Path): Base directory for storing GAAPF data

#### Methods

##### `async initialize()`

Initialize the learning hub and all subsystems.

**Returns:** None

**Raises:** 
- `InitializationError`: If initialization fails

##### `async create_session(user_id: str, framework: str, module_id: str, llm: Any = None) -> str`

Create a new learning session.

**Parameters:**
- `user_id` (str): User identifier
- `framework` (str): Target framework (e.g., "langchain", "langgraph")
- `module_id` (str): Module identifier
- `llm` (Any, optional): Language model instance

**Returns:** str - Session ID

**Example:**
```python
session_id = await hub.create_session(
    user_id="learner_123",
    framework="langchain",
    module_id="lc_basics"
)
```

##### `async process_message(user_id: str, message: str, session_id: str) -> Dict[str, Any]`

Process a user message through the agent constellation.

**Parameters:**
- `user_id` (str): User identifier
- `message` (str): User's message/question
- `session_id` (str): Active session ID

**Returns:** Dict containing:
- `response` (str): Agent's response
- `agent_path` (List[str]): Agents involved in processing
- `confidence_scores` (Dict[str, float]): Confidence scores by agent
- `handoff_reasons` (List[str]): Reasons for agent handoffs
- `metadata` (Dict): Additional processing metadata

**Example:**
```python
result = await hub.process_message(
    user_id="learner_123",
    message="How do I create a LangChain chain?",
    session_id=session_id
)
print(result["response"])
```

##### `async save_user_profile(profile: UserProfile) -> bool`

Save a user profile.

**Parameters:**
- `profile` (UserProfile): User profile object

**Returns:** bool - Success status

##### `async get_user_profile(user_id: str) -> Optional[UserProfile]`

Retrieve a user profile.

**Parameters:**
- `user_id` (str): User identifier

**Returns:** UserProfile or None if not found

##### `async get_learning_progress(user_id: str) -> Dict[str, Any]`

Get comprehensive learning progress for a user.

**Parameters:**
- `user_id` (str): User identifier

**Returns:** Dict containing progress metrics

##### `async end_session(session_id: str) -> bool`

End a learning session and perform cleanup.

**Parameters:**
- `session_id` (str): Session to end

**Returns:** bool - Success status

### ConstellationManager

Manages agent constellations and orchestration.

```python
from gaapf.core.constellation import ConstellationManager
```

#### Constructor

```python
ConstellationManager(data_path: Path = Path("data"))
```

#### Methods

##### `async create_constellation(user_profile: Dict, framework: str, module_id: str, session_id: str, llm: Any) -> Constellation`

Create an optimized agent constellation.

**Parameters:**
- `user_profile` (Dict): User profile data
- `framework` (str): Target framework
- `module_id` (str): Module identifier
- `session_id` (str): Session identifier
- `llm` (Any): Language model instance

**Returns:** Constellation object

##### `async select_best_agent(constellation: Constellation, message: str, context: Dict) -> BaseGAAPFAgent`

Select the best agent for a given message.

**Parameters:**
- `constellation` (Constellation): Active constellation
- `message` (str): User message
- `context` (Dict): Conversation context

**Returns:** Selected agent instance

## 🧠 Memory Systems

### IntegratedMemoryManager

Unified interface for all memory systems.

```python
from gaapf.core.memory_systems import IntegratedMemoryManager
```

#### Constructor

```python
IntegratedMemoryManager(data_path: Path = Path("data/memory"))
```

#### Methods

##### `get_conversation_memory(session_id: str) -> ConversationMemory`

Get conversation memory for a session.

**Parameters:**
- `session_id` (str): Session identifier

**Returns:** ConversationMemory instance

##### `get_user_memory(user_id: str) -> UserMemory`

Get user memory for a user.

**Parameters:**
- `user_id` (str): User identifier

**Returns:** UserMemory instance

##### `get_knowledge_memory() -> KnowledgeMemory`

Get the knowledge memory instance.

**Returns:** KnowledgeMemory instance

##### `search_all_memories(query: str, user_id: str = None, session_id: str = None) -> Dict[str, List[Any]]`

Search across all memory tiers.

**Parameters:**
- `query` (str): Search query
- `user_id` (str, optional): User filter
- `session_id` (str, optional): Session filter

**Returns:** Dict with results from each memory tier

### ConversationMemory

Manages session-specific conversation context.

```python
from gaapf.core.memory_systems import ConversationMemory
```

#### Constructor

```python
ConversationMemory(session_id: str, max_entries: int = 100)
```

#### Methods

##### `add_message(role: str, content: str, agent_type: str = None, metadata: Dict = None)`

Add a message to conversation memory.

**Parameters:**
- `role` (str): Message role ("user", "assistant", "system")
- `content` (str): Message content
- `agent_type` (str, optional): Agent that generated the message
- `metadata` (Dict, optional): Additional metadata

##### `get_recent_messages(count: int = 10) -> List[Dict[str, Any]]`

Get recent messages from conversation.

**Parameters:**
- `count` (int): Number of messages to retrieve

**Returns:** List of message dictionaries

##### `search_messages(query: str, limit: int = 5) -> List[MemoryEntry]`

Search messages by content.

**Parameters:**
- `query` (str): Search query
- `limit` (int): Maximum results

**Returns:** List of matching memory entries

### UserMemory

Manages long-term user data and learning history.

```python
from gaapf.core.memory_systems import UserMemory
```

#### Constructor

```python
UserMemory(user_id: str, data_path: Path = Path("data/user_memory"))
```

#### Methods

##### `update_profile(profile_data: Dict[str, Any])`

Update user profile information.

**Parameters:**
- `profile_data` (Dict): Profile data to update

##### `add_learning_session(session_data: Dict[str, Any])`

Add a learning session to history.

**Parameters:**
- `session_data` (Dict): Session information

##### `add_goal(goal: str, target_date: str = None, framework: str = None)`

Add a learning goal.

**Parameters:**
- `goal` (str): Goal description
- `target_date` (str, optional): Target completion date
- `framework` (str, optional): Related framework

##### `get_learning_summary(days: int = 30) -> Dict[str, Any]`

Get learning summary for specified period.

**Parameters:**
- `days` (int): Number of days to analyze

**Returns:** Summary statistics

## 📊 Analytics System

### RealTimeAnalyticsEngine

Real-time learning analytics and metrics collection.

```python
from gaapf.core.analytics_system import RealTimeAnalyticsEngine
```

#### Constructor

```python
RealTimeAnalyticsEngine(data_path: Path = Path("data/analytics"))
```

#### Methods

##### `start_real_time_processing()`

Start real-time analytics processing.

##### `record_engagement_metrics(user_id: str, session_id: str, framework: str, module_id: str, metrics: EngagementMetrics)`

Record engagement metrics.

**Parameters:**
- `user_id` (str): User identifier
- `session_id` (str): Session identifier
- `framework` (str): Framework context
- `module_id` (str): Module context
- `metrics` (EngagementMetrics): Engagement metrics object

##### `get_real_time_dashboard(user_id: str = None) -> Dict[str, Any]`

Get real-time dashboard data.

**Parameters:**
- `user_id` (str, optional): User filter

**Returns:** Dashboard data dictionary

##### `analyze_learning_effectiveness(user_id: str, time_window_hours: int = 24) -> Dict[str, Any]`

Analyze learning effectiveness for a user.

**Parameters:**
- `user_id` (str): User identifier
- `time_window_hours` (int): Analysis time window

**Returns:** Effectiveness analysis results

### Metrics Classes

#### EngagementMetrics

```python
from gaapf.core.analytics_system import EngagementMetrics

EngagementMetrics(
    session_duration: float,
    interaction_count: int,
    question_count: int,
    response_time_avg: float,
    attention_score: float,
    completion_rate: float
)
```

#### LearningEffectivenessMetrics

```python
from gaapf.core.analytics_system import LearningEffectivenessMetrics

LearningEffectivenessMetrics(
    comprehension_score: float,
    retention_score: float,
    application_score: float,
    progress_velocity: float,
    concept_mastery_rate: float,
    error_recovery_rate: float
)
```

#### AdaptiveMetrics

```python
from gaapf.core.analytics_system import AdaptiveMetrics

AdaptiveMetrics(
    difficulty_appropriateness: float,
    learning_style_alignment: float,
    constellation_effectiveness: float,
    tool_usage_efficiency: float,
    handoff_success_rate: float,
    personalization_score: float
)
```

## 🎯 Knowledge Graph

### KnowledgeGraphManager

Manages concept relationships and learning paths.

```python
from gaapf.core.knowledge_graph import KnowledgeGraphManager
```

#### Constructor

```python
KnowledgeGraphManager(data_path: Path = Path("data/knowledge_graph"))
```

#### Methods

##### `add_concept(concept: ConceptNode) -> bool`

Add a concept to the knowledge graph.

**Parameters:**
- `concept` (ConceptNode): Concept to add

**Returns:** bool - Success status

##### `add_relationship(from_concept: str, to_concept: str, relationship_type: RelationshipType, weight: float = 1.0) -> bool`

Add a relationship between concepts.

**Parameters:**
- `from_concept` (str): Source concept ID
- `to_concept` (str): Target concept ID
- `relationship_type` (RelationshipType): Type of relationship
- `weight` (float): Relationship weight

**Returns:** bool - Success status

##### `find_learning_path(start_concepts: List[str], target_concept: str, user_id: str = None) -> List[str]`

Find optimal learning path to target concept.

**Parameters:**
- `start_concepts` (List[str]): Known concepts
- `target_concept` (str): Target concept ID
- `user_id` (str, optional): User context

**Returns:** List of concept IDs in learning order

##### `identify_knowledge_gaps(user_id: str, target_concepts: List[str]) -> Dict[str, Any]`

Identify knowledge gaps for target concepts.

**Parameters:**
- `user_id` (str): User identifier
- `target_concepts` (List[str]): Target concept IDs

**Returns:** Gap analysis results

### ConceptNode

Represents a learning concept in the knowledge graph.

```python
from gaapf.core.knowledge_graph import ConceptNode

ConceptNode(
    concept_id: str,
    name: str,
    framework: str,
    module_id: str,
    description: str = "",
    difficulty_level: str = "intermediate",
    prerequisites: List[str] = None,
    learning_objectives: List[str] = None,
    estimated_time_minutes: int = 60,
    tags: List[str] = None
)
```

## 🗄️ Framework Database

### FrameworkDatabaseManager

Manages framework and module data with caching.

```python
from gaapf.core.framework_database import FrameworkDatabaseManager
```

#### Constructor

```python
FrameworkDatabaseManager(data_path: Path = None)
```

Note: Uses singleton pattern - only one instance per application.

#### Methods

##### `get_module(module_id: str, use_cache: bool = True) -> Optional[FrameworkModule]`

Get a module by ID.

**Parameters:**
- `module_id` (str): Module identifier
- `use_cache` (bool): Whether to use cache

**Returns:** FrameworkModule or None

##### `get_framework_modules(framework: str, use_cache: bool = True) -> List[FrameworkModule]`

Get all modules for a framework.

**Parameters:**
- `framework` (str): Framework identifier
- `use_cache` (bool): Whether to use cache

**Returns:** List of FrameworkModule objects

##### `search_modules(query: str = None, framework: str = None, difficulty: ModuleDifficulty = None, module_type: ModuleType = None, topics: List[str] = None, use_cache: bool = True) -> List[FrameworkModule]`

Search modules with filters.

**Parameters:**
- `query` (str, optional): Text search query
- `framework` (str, optional): Framework filter
- `difficulty` (ModuleDifficulty, optional): Difficulty filter
- `module_type` (ModuleType, optional): Module type filter
- `topics` (List[str], optional): Topics filter
- `use_cache` (bool): Whether to use cache

**Returns:** List of matching modules

##### `get_cache_statistics() -> Dict[str, Any]`

Get cache performance statistics.

**Returns:** Cache statistics dictionary

## 🤖 Agent System

### BaseGAAPFAgent

Base class for all GAAPF agents.

```python
from gaapf.agents.base_agent import BaseGAAPFAgent
```

#### Constructor

```python
BaseGAAPFAgent(
    agent_type: str,
    llm: Any,
    user_profile: Dict[str, Any],
    framework: str,
    module_id: str,
    session_id: str,
    data_path: Path = Path("data")
)
```

#### Methods

##### `async ainvoke(query: str, user_id: str, **kwargs) -> str`

Process a query asynchronously.

**Parameters:**
- `query` (str): User query
- `user_id` (str): User identifier
- `**kwargs`: Additional parameters

**Returns:** Agent response string

##### `get_confidence_score(message: str) -> float`

Calculate confidence score for handling a message.

**Parameters:**
- `message` (str): User message

**Returns:** Confidence score (0.0 to 1.0)

##### `analyze_handoff_need(content: str, user_message: str) -> Dict[str, Any]`

Analyze if handoff to another agent is needed.

**Parameters:**
- `content` (str): Current response content
- `user_message` (str): User's message

**Returns:** Handoff analysis dictionary

### Agent Factory

```python
from gaapf.agents.base_agent import create_agent

create_agent(
    agent_type: str,
    llm: Any,
    user_profile: Dict[str, Any],
    framework: str,
    module_id: str,
    session_id: str,
    data_path: Path = Path("data")
) -> BaseGAAPFAgent
```

**Available Agent Types:**
- `instructor` - Concept explanation and theory
- `code_assistant` - Code examples and implementation
- `documentation_expert` - Official docs and references
- `practice_facilitator` - Hands-on exercises
- `mentor` - Learning guidance and strategy
- `assessment` - Knowledge evaluation
- `research_assistant` - Latest information and trends
- `project_guide` - End-to-end project guidance
- `troubleshooter` - Error resolution and debugging
- `motivational_coach` - Encouragement and motivation
- `knowledge_synthesizer` - Connecting concepts
- `progress_tracker` - Learning progress analysis

## 🔧 Configuration

### UserProfile

User profile configuration.

```python
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningStyle, LearningPace

UserProfile(
    user_id: str,
    programming_experience_years: int,
    python_skill_level: SkillLevel,
    learning_pace: LearningPace,
    preferred_learning_style: LearningStyle,
    ai_framework_experience: Dict[str, str] = None,
    learning_goals: List[str] = None,
    time_availability: int = 10,
    preferred_difficulty_progression: str = "gradual"
)
```

### Environment Configuration

```python
from gaapf.config.env_config import GAAPFConfig, get_config

# Get configuration instance
config = get_config()

# Create LLM instance
llm = config.create_llm()

# Get available providers
providers = config.get_available_llm_providers()
```

## 🚨 Error Handling

### Common Exceptions

```python
from gaapf.core.exceptions import (
    GAAPFError,
    InitializationError,
    SessionError,
    AgentError,
    MemoryError,
    AnalyticsError
)
```

### Error Handling Example

```python
try:
    session_id = await hub.create_session(
        user_id="test_user",
        framework="langchain",
        module_id="lc_basics"
    )
except InitializationError as e:
    print(f"Initialization failed: {e}")
except SessionError as e:
    print(f"Session creation failed: {e}")
except GAAPFError as e:
    print(f"General GAAPF error: {e}")
```

This API reference provides comprehensive documentation for all public interfaces in the GAAPF system.
