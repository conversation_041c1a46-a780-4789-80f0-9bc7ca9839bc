#!/usr/bin/env python3
"""
Test script for smart LLM provider selection.

This script tests the intelligent provider fallback logic when the default provider is not available.
"""

import sys
import os
from pathlib import Path
from unittest.mock import patch

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_smart_provider_selection():
    """Test smart provider selection logic."""
    print("🧪 Testing Smart LLM Provider Selection")
    print("=" * 50)
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Test 1: Default provider available
        print("\n📋 Test 1: Default provider available")
        with patch.dict(os.environ, {
            'DEFAULT_LLM_PROVIDER': 'together',
            'TOGETHER_API_KEY': 'test_key'
        }, clear=True):
            config = EnvironmentConfig()
            
            # Mock ChatTogether to avoid actual API calls
            with patch('langchain_together.ChatTogether') as mock_chat:
                mock_chat.return_value = "mock_llm"
                llm = config.create_llm()
                
                print("✅ Used default provider successfully")
                mock_chat.assert_called_once()
        
        # Test 2: Default provider not available, fallback to available one
        print("\n📋 Test 2: Default provider not available, fallback")
        with patch.dict(os.environ, {
            'DEFAULT_LLM_PROVIDER': 'google',  # Not available
            'TOGETHER_API_KEY': 'test_key'     # Available
        }, clear=True):
            config = EnvironmentConfig()
            
            # Mock ChatTogether to avoid actual API calls
            with patch('langchain_together.ChatTogether') as mock_chat:
                mock_chat.return_value = "mock_llm"
                
                # Capture print output
                import io
                from contextlib import redirect_stdout
                
                captured_output = io.StringIO()
                with redirect_stdout(captured_output):
                    llm = config.create_llm()
                
                output = captured_output.getvalue()
                assert "Default provider 'google' not available. Using 'together' instead." in output
                print("✅ Correctly fell back to available provider")
                mock_chat.assert_called_once()
        
        # Test 3: No providers available
        print("\n📋 Test 3: No providers available")
        with patch.dict(os.environ, {}, clear=True):
            # Mock the load_environment method to prevent loading .env file
            with patch.object(EnvironmentConfig, 'load_environment'):
                config = EnvironmentConfig()

                try:
                    llm = config.create_llm()
                    print("❌ Should have raised ValueError")
                    return False
                except ValueError as e:
                    assert "No LLM providers are available" in str(e)
                    print("✅ Correctly raised error when no providers available")
        
        # Test 4: Specific provider requested and available
        print("\n📋 Test 4: Specific provider requested and available")
        with patch.dict(os.environ, {
            'DEFAULT_LLM_PROVIDER': 'google',
            'TOGETHER_API_KEY': 'test_key'
        }, clear=True):
            config = EnvironmentConfig()
            
            with patch('langchain_together.ChatTogether') as mock_chat:
                mock_chat.return_value = "mock_llm"
                llm = config.create_llm(provider='together')
                
                print("✅ Used specifically requested provider")
                mock_chat.assert_called_once()
        
        # Test 5: Specific provider requested but not available
        print("\n📋 Test 5: Specific provider requested but not available")
        with patch.dict(os.environ, {}, clear=True):
            # Mock the load_environment method to prevent loading .env file
            with patch.object(EnvironmentConfig, 'load_environment'):
                config = EnvironmentConfig()

                try:
                    llm = config.create_llm(provider='together')
                    print("❌ Should have raised ValueError")
                    return False
                except ValueError as e:
                    assert "API key not configured for provider: together" in str(e)
                    print("✅ Correctly raised error for unavailable specific provider")
        
        return True
        
    except Exception as e:
        print(f"❌ Smart provider selection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging_configuration():
    """Test logging configuration with environment variables."""
    print("\n🧪 Testing Logging Configuration")
    print("=" * 30)
    
    try:
        import logging
        
        # Test default log level
        with patch.dict(os.environ, {}, clear=True):
            # Reload the main module to test logging config
            import importlib
            import gaapf.main
            importlib.reload(gaapf.main)
            
            # Check that logger is configured
            logger = logging.getLogger('gaapf.main')
            print("✅ Default logging configuration works")
        
        # Test custom log level
        with patch.dict(os.environ, {'LOG_LEVEL': 'DEBUG'}):
            importlib.reload(gaapf.main)
            print("✅ Custom log level configuration works")
        
        return True
        
    except Exception as e:
        print(f"❌ Logging configuration test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Smart Provider Selection & Logging Test")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Smart Provider Selection", test_smart_provider_selection),
        ("Logging Configuration", test_logging_configuration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All smart provider selection tests passed!")
        print("\n💡 Key Features Verified:")
        print("   ✅ Automatic fallback to available providers")
        print("   ✅ Clear warning messages for provider changes")
        print("   ✅ Proper error handling for unavailable providers")
        print("   ✅ Environment-based logging configuration")
        print("   ✅ Together AI integration working correctly")
        return 0
    else:
        print("\n❌ Some tests failed.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
