#!/usr/bin/env python3
"""
Integration test for GAAPF system.

This script tests the complete GAAPF system with mock LLM to verify
all components work together correctly.
"""

import sys
import os
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

class MockLLM:
    """Mock LLM for testing purposes."""
    
    def __init__(self, model_name="mock-llm"):
        self.model_name = model_name
        self.temperature = 0.2
        
    async def ainvoke(self, messages, **kwargs):
        """Mock async invoke method."""
        # Return a mock response based on the input
        if isinstance(messages, str):
            content = messages
        elif isinstance(messages, list) and messages:
            content = str(messages[-1])
        else:
            content = "test message"
            
        # Create a simple response based on content
        if "langchain" in content.lower():
            response_text = "LangChain is a framework for developing applications powered by language models. Here's how to get started..."
        elif "code" in content.lower():
            response_text = "Here's a simple code example:\n```python\nfrom langchain import <PERSON><PERSON>hain\nprint('Hello, Lang<PERSON>hain!')\n```"
        else:
            response_text = f"I understand you're asking about: {content[:50]}... Let me help you learn more about this topic."
            
        # Mock response object
        mock_response = Mock()
        mock_response.content = response_text
        return mock_response
    
    def invoke(self, messages, **kwargs):
        """Mock sync invoke method."""
        return asyncio.run(self.ainvoke(messages, **kwargs))

async def test_user_profile_creation():
    """Test user profile creation and management."""
    print("👤 Testing User Profile Creation")
    
    try:
        from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
        
        # Create a test user profile
        profile = UserProfile(
            user_id="test_user",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE.value,
            learning_pace=LearningPace.MODERATE.value,
            preferred_learning_style=LearningStyle.MIXED.value
        )
        
        print(f"✅ User profile created: {profile.user_id}")
        print(f"   Skill level: {profile.python_skill_level}")
        print(f"   Learning style: {profile.preferred_learning_style}")
        
        # Test serialization
        profile_dict = profile.to_dict()
        profile_restored = UserProfile.from_dict(profile_dict)
        
        assert profile_restored.user_id == profile.user_id
        print("✅ Profile serialization/deserialization works")
        
        return True
        
    except Exception as e:
        print(f"❌ User profile test failed: {e}")
        return False

async def test_learning_hub_integration():
    """Test the complete learning hub integration."""
    print("\n🏗️ Testing Learning Hub Integration")
    
    try:
        from gaapf.core.learning_hub import LearningHubCore
        from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
        
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Initialize learning hub with mock LLM
            mock_llm = MockLLM()
            hub = LearningHubCore(llm=mock_llm, data_path=temp_path)
            await hub.initialize()
            
            print("✅ Learning hub initialized")
            
            # Create and save a test user profile
            profile = UserProfile(
                user_id="integration_test_user",
                programming_experience_years=2,
                python_skill_level=SkillLevel.BEGINNER.value,
                learning_pace=LearningPace.SLOW.value,
                preferred_learning_style=LearningStyle.HANDS_ON.value
            )
            
            await hub.save_user_profile(profile)
            print("✅ User profile saved")
            
            # Retrieve the profile
            retrieved_profile = await hub.get_user_profile("integration_test_user")
            assert retrieved_profile is not None
            assert retrieved_profile.user_id == "integration_test_user"
            print("✅ User profile retrieved")
            
            # Create a learning session
            session_id = await hub.create_session(
                user_id="integration_test_user",
                framework="langchain",
                module_id="basics",
                llm=mock_llm
            )
            
            print(f"✅ Learning session created: {session_id}")
            
            # Process a message
            result = await hub.process_message(
                user_id="integration_test_user",
                message="What is LangChain and how do I get started?",
                session_id=session_id
            )
            
            assert "response" in result
            assert "agent_path" in result
            assert result["session_id"] == session_id
            print("✅ Message processed successfully")
            print(f"   Response preview: {result['response'][:100]}...")
            print(f"   Agent path: {result['agent_path']}")
            
            # Process another message to test agent handoff
            result2 = await hub.process_message(
                user_id="integration_test_user",
                message="Show me a simple code example",
                session_id=session_id
            )
            
            print("✅ Second message processed")
            print(f"   Agent path: {result2['agent_path']}")
            
            # End the session
            summary = await hub.end_session(session_id)
            assert "session_id" in summary
            assert summary["user_id"] == "integration_test_user"
            print("✅ Session ended successfully")
            
            # Get learning progress
            progress = await hub.get_learning_progress("integration_test_user")
            assert progress["user_id"] == "integration_test_user"
            assert progress["total_sessions"] >= 1
            print("✅ Learning progress retrieved")
            
            return True
            
    except Exception as e:
        print(f"❌ Learning hub integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_agent_creation():
    """Test agent creation and basic functionality."""
    print("\n🤖 Testing Agent Creation")
    
    try:
        from gaapf.agents.base_agent import create_agent
        from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
        
        # Create test user profile
        user_profile = {
            "user_id": "test_agent_user",
            "skill_level": SkillLevel.INTERMEDIATE.value,
            "learning_style": LearningStyle.VISUAL.value
        }
        
        mock_llm = MockLLM()
        
        # Test creating different agent types
        agent_types = ["instructor", "code_assistant", "documentation_expert", "practice_facilitator", "mentor"]
        
        for agent_type in agent_types:
            try:
                agent = await create_agent(
                    agent_type=agent_type,
                    user_profile=user_profile,
                    framework="langchain",
                    module_id="test_module",
                    session_id="test_session",
                    llm=mock_llm
                )
                
                assert agent.agent_type == agent_type
                print(f"✅ {agent_type} agent created successfully")
                
                # Test confidence scoring
                confidence = agent.get_confidence_score("test message")
                assert 0.0 <= confidence <= 1.0
                print(f"   Confidence scoring works: {confidence}")
                
            except Exception as e:
                print(f"❌ Failed to create {agent_type} agent: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Agent creation test failed: {e}")
        return False

async def test_constellation_manager():
    """Test constellation manager functionality."""
    print("\n🌟 Testing Constellation Manager")
    
    try:
        from gaapf.core.constellation import ConstellationManager, ConstellationType
        from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
        
        # Create constellation manager
        manager = ConstellationManager()
        print("✅ Constellation manager created")
        
        # Test user profile
        user_profile = {
            "user_id": "constellation_test_user",
            "skill_level": SkillLevel.BEGINNER.value,
            "learning_style": LearningStyle.HANDS_ON.value
        }
        
        mock_llm = MockLLM()
        
        # Test creating a constellation
        agents = await manager.create_constellation(
            constellation_type=ConstellationType.BASIC_LEARNING,
            user_profile=user_profile,
            framework="langchain",
            module_id="basics",
            session_id="constellation_test_session",
            llm=mock_llm
        )
        
        assert len(agents) > 0
        print(f"✅ Constellation created with {len(agents)} agents")
        
        # Verify agent types
        expected_primary = ["instructor", "code_assistant"]
        for agent_type in expected_primary:
            assert agent_type in agents
            print(f"   ✅ {agent_type} agent present")
        
        return True
        
    except Exception as e:
        print(f"❌ Constellation manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("🧪 GAAPF Integration Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("User Profile Creation", test_user_profile_creation),
        ("Agent Creation", test_agent_creation),
        ("Constellation Manager", test_constellation_manager),
        ("Learning Hub Integration", test_learning_hub_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Integration Test Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All integration tests passed!")
        print("\n✅ GAAPF system is working correctly!")
        print("\n📝 Ready for production use:")
        print("1. Configure your API keys in .env file")
        print("2. Run: python -m gaapf")
        return 0
    else:
        print("\n❌ Some integration tests failed.")
        print("Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
