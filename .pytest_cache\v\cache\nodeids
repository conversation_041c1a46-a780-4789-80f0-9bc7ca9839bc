["tests/test_agents.py::TestAgentFactory::test_create_all_agent_types", "tests/test_agents.py::TestAgentFactory::test_invalid_agent_type", "tests/test_agents.py::TestAgentHandoffs::test_code_assistant_to_troubleshooter_handoff", "tests/test_agents.py::TestAgentHandoffs::test_instructor_to_code_assistant_handoff", "tests/test_agents.py::TestBaseAgent::test_base_agent_initialization", "tests/test_agents.py::TestBaseAgent::test_base_agent_invoke", "tests/test_agents.py::TestBaseAgent::test_confidence_score_calculation", "tests/test_agents.py::TestSpecializedAgents::test_code_assistant_confidence", "tests/test_agents.py::TestSpecializedAgents::test_instructor_agent_confidence", "tests/test_agents.py::TestSpecializedAgents::test_motivational_coach_confidence", "tests/test_agents.py::TestSpecializedAgents::test_troubleshooter_confidence", "tests/test_core_systems.py::TestAnalyticsSystem::test_learning_effectiveness_analysis", "tests/test_core_systems.py::TestAnalyticsSystem::test_real_time_dashboard", "tests/test_core_systems.py::TestAnalyticsSystem::test_record_effectiveness_metrics", "tests/test_core_systems.py::TestAnalyticsSystem::test_record_engagement_metrics", "tests/test_core_systems.py::TestFrameworkDatabase::test_cache_functionality", "tests/test_core_systems.py::TestFrameworkDatabase::test_database_statistics", "tests/test_core_systems.py::TestFrameworkDatabase::test_get_module", "tests/test_core_systems.py::TestFrameworkDatabase::test_singleton_pattern", "tests/test_core_systems.py::TestKnowledgeGraph::test_add_concept", "tests/test_core_systems.py::TestKnowledgeGraph::test_add_relationship", "tests/test_core_systems.py::TestKnowledgeGraph::test_find_learning_path", "tests/test_core_systems.py::TestKnowledgeGraph::test_identify_knowledge_gaps", "tests/test_core_systems.py::TestMemorySystems::test_conversation_memory", "tests/test_core_systems.py::TestMemorySystems::test_integrated_memory_manager", "tests/test_core_systems.py::TestMemorySystems::test_knowledge_memory", "tests/test_core_systems.py::TestMemorySystems::test_user_memory", "tests/test_end_to_end_scenarios.py::TestBasicAgentWorkflow::test_agent_creation_and_basic_conversation", "tests/test_end_to_end_scenarios.py::TestBasicAgentWorkflow::test_agent_with_memory_persistence", "tests/test_end_to_end_scenarios.py::TestBasicAgentWorkflow::test_async_agent_workflow", "tests/test_end_to_end_scenarios.py::TestErrorHandlingScenarios::test_agent_resilience_to_llm_failures", "tests/test_end_to_end_scenarios.py::TestErrorHandlingScenarios::test_agent_with_corrupted_memory_file", "tests/test_end_to_end_scenarios.py::TestErrorHandlingScenarios::test_agent_with_missing_tools_file", "tests/test_end_to_end_scenarios.py::TestGAAPFIntegration::test_environment_config_integration", "tests/test_end_to_end_scenarios.py::TestGAAPFIntegration::test_knowledge_graph_basic_operations", "tests/test_end_to_end_scenarios.py::TestGAAPFIntegration::test_memory_system_integration", "tests/test_end_to_end_scenarios.py::TestPerformanceScenarios::test_large_memory_handling", "tests/test_end_to_end_scenarios.py::TestPerformanceScenarios::test_multiple_concurrent_conversations", "tests/test_end_to_end_scenarios.py::TestToolIntegrationWorkflow::test_tool_discovery_and_loading", "tests/test_end_to_end_scenarios.py::TestToolIntegrationWorkflow::test_tool_related_conversation", "tests/test_error_handling_edge_cases.py::TestMemoryErrorHandling::test_memory_with_corrupted_file", "tests/test_integration.py::TestCLIIntegration::test_cli_initialization", "tests/test_integration.py::TestConfigurationIntegration::test_config_validation", "tests/test_integration.py::TestConfigurationIntegration::test_llm_provider_selection", "tests/test_integration.py::TestConstellationIntegration::test_agent_selection_and_handoff", "tests/test_integration.py::TestConstellationIntegration::test_constellation_adaptation", "tests/test_integration.py::TestConstellationIntegration::test_constellation_creation", "tests/test_integration.py::TestEndToEndLearningSession::test_complete_learning_session", "tests/test_integration.py::TestLearningHubIntegration::test_learning_progress_tracking", "tests/test_integration.py::TestLearningHubIntegration::test_message_processing", "tests/test_integration.py::TestLearningHubIntegration::test_session_creation_and_management", "tests/test_integration.py::TestLearningHubIntegration::test_session_ending", "tests/test_integration.py::TestLearningHubIntegration::test_user_profile_management", "tests/test_integration_components.py::TestCrossModuleInteractions::test_agent_memory_tool_integration", "tests/test_integration_components.py::TestCrossModuleInteractions::test_error_propagation_across_modules", "tests/test_integration_components.py::TestCrossModuleInteractions::test_memory_persistence_across_agents", "tests/test_integration_components.py::TestCrossModuleInteractions::test_tool_manager_isolation", "tests/test_integration_components.py::TestDataFlowIntegration::test_component_lifecycle_integration", "tests/test_integration_components.py::TestDataFlowIntegration::test_configuration_data_flow", "tests/test_integration_components.py::TestDataFlowIntegration::test_memory_data_consistency", "tests/test_integration_components.py::TestVinagentGAAPFIntegration::test_agent_with_gaapf_config", "tests/test_integration_components.py::TestVinagentGAAPFIntegration::test_configuration_compatibility", "tests/test_integration_components.py::TestVinagentGAAPFIntegration::test_gaapf_memory_systems_integration", "tests/test_integration_components.py::TestVinagentGAAPFIntegration::test_knowledge_graph_integration", "tests/test_integration_components.py::TestVinagentGAAPFIntegration::test_memory_system_integration", "tests/test_memory_path_validation.py::TestEdgeCasesAndBoundaryConditions::test_memory_with_special_characters_in_data", "tests/test_memory_path_validation.py::TestEdgeCasesAndBoundaryConditions::test_memory_with_unicode_paths", "tests/test_memory_path_validation.py::TestEdgeCasesAndBoundaryConditions::test_memory_with_very_long_user_id", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_init_with_invalid_path_fallback", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_init_with_valid_path", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_load_with_corrupted_file", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_load_with_invalid_user_id", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_save_with_invalid_data_type", "tests/test_memory_path_validation.py::TestMemoryWithImprovedValidation::test_memory_save_with_invalid_user_id", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_absolute_path", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_empty_string", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_invalid_characters", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_invalid_extension", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_none", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_path_object", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_relative_path", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_reserved_names", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_too_long", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_valid_json", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_valid_jsonl", "tests/test_memory_path_validation.py::TestPathValidation::test_validate_memory_path_whitespace_only", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_load_corrupted_file", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_load_empty_file", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_load_missing_file", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_load_valid_file", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_save_atomic_write", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_save_creates_directories", "tests/test_memory_path_validation.py::TestSafeJsonOperations::test_safe_json_save_valid_data", "tests/test_performance_load.py::TestAgentPerformance::test_agent_memory_usage", "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_agent_with_invalid_memory_path", "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_agent_with_invalid_tools_path", "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_agent_with_malformed_tools_json", "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_async_invoke_with_llm_error", "tests/test_vinagent_agent.py::TestAgentErrorHandling::test_invoke_with_llm_error", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_basic_initialization", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_reset_memory", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_reset_tools", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_with_mcp_client", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_with_memory_initialization", "tests/test_vinagent_agent.py::TestAgentInitialization::test_agent_with_tools_initialization", "tests/test_vinagent_agent.py::TestAgentInvocation::test_async_invoke_with_user_id", "tests/test_vinagent_agent.py::TestAgentInvocation::test_async_invoke_without_tools", "tests/test_vinagent_agent.py::TestAgentInvocation::test_invoke_with_memory_save", "tests/test_vinagent_agent.py::TestAgentInvocation::test_invoke_with_user_id", "tests/test_vinagent_agent.py::TestAgentInvocation::test_sync_invoke_without_tools", "tests/test_vinagent_agent.py::TestAgentMemoryIntegration::test_memory_persistence", "tests/test_vinagent_agent.py::TestAgentMemoryIntegration::test_memory_save_and_load", "tests/test_vinagent_agent.py::TestAgentStreamingAndAsync::test_agent_streaming", "tests/test_vinagent_agent.py::TestAgentToolIntegration::test_tool_execution_sync"]