"""
User profile configurations for GAAPF.

This module defines the user profile structure and related enums
for the GAAPF system.
"""

from enum import Enum
from typing import Dict, List, Optional, Any


class SkillLevel(str, Enum):
    """Skill level enum for user profiles."""
    NONE = "none"
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningPace(str, Enum):
    """Learning pace enum for user profiles."""
    SLOW = "slow"
    MODERATE = "moderate"
    FAST = "fast"
    INTENSIVE = "intensive"


class LearningStyle(str, Enum):
    """Learning style enum for user profiles."""
    VISUAL = "visual"
    HANDS_ON = "hands_on"
    THEORETICAL = "theoretical"
    MIXED = "mixed"


class DifficultyProgression(str, Enum):
    """Difficulty progression enum for user profiles."""
    GRADUAL = "gradual"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


class UserProfile:
    """
    User profile class for GAAPF.
    
    Stores user information, preferences, and learning history
    to enable personalized learning experiences.
    """
    
    def __init__(
        self,
        user_id: str,
        programming_experience_years: int = 0,
        python_skill_level: SkillLevel = SkillLevel.BEGINNER,
        learning_pace: LearningPace = LearningPace.MODERATE,
        preferred_learning_style: LearningStyle = LearningStyle.MIXED,
        difficulty_progression: DifficultyProgression = DifficultyProgression.MODERATE,
        learning_goals: List[str] = None,
        completed_modules: Dict[str, Dict[str, Any]] = None,
        preferences: Dict[str, Any] = None
    ):
        """
        Initialize a user profile.
        
        Args:
            user_id: Unique user identifier
            programming_experience_years: Years of programming experience
            python_skill_level: Python skill level
            learning_pace: Preferred learning pace
            preferred_learning_style: Preferred learning style
            difficulty_progression: Preferred difficulty progression
            learning_goals: List of learning goals
            completed_modules: Dictionary of completed modules with completion data
            preferences: Additional user preferences
        """
        self.user_id = user_id
        self.programming_experience_years = programming_experience_years
        
        # Ensure enum values are used
        if isinstance(python_skill_level, str):
            self.python_skill_level = SkillLevel(python_skill_level)
        else:
            self.python_skill_level = python_skill_level
            
        if isinstance(learning_pace, str):
            self.learning_pace = LearningPace(learning_pace)
        else:
            self.learning_pace = learning_pace
            
        if isinstance(preferred_learning_style, str):
            self.preferred_learning_style = LearningStyle(preferred_learning_style)
        else:
            self.preferred_learning_style = preferred_learning_style
            
        if isinstance(difficulty_progression, str):
            self.difficulty_progression = DifficultyProgression(difficulty_progression)
        else:
            self.difficulty_progression = difficulty_progression
            
        self.learning_goals = learning_goals or []
        self.completed_modules = completed_modules or {}
        self.preferences = preferences or {}
        
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the profile to a dictionary.
        
        Returns:
            Dictionary representation of the profile
        """
        return {
            "user_id": self.user_id,
            "programming_experience_years": self.programming_experience_years,
            "python_skill_level": self.python_skill_level.value,
            "learning_pace": self.learning_pace.value,
            "preferred_learning_style": self.preferred_learning_style.value,
            "difficulty_progression": self.difficulty_progression.value,
            "learning_goals": self.learning_goals,
            "completed_modules": self.completed_modules,
            "preferences": self.preferences
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserProfile':
        """
        Create a profile from a dictionary.
        
        Args:
            data: Dictionary representation of a profile
            
        Returns:
            UserProfile instance
        """
        return cls(
            user_id=data["user_id"],
            programming_experience_years=data.get("programming_experience_years", 0),
            python_skill_level=data.get("python_skill_level", SkillLevel.BEGINNER.value),
            learning_pace=data.get("learning_pace", LearningPace.MODERATE.value),
            preferred_learning_style=data.get("preferred_learning_style", LearningStyle.MIXED.value),
            difficulty_progression=data.get("difficulty_progression", DifficultyProgression.MODERATE.value),
            learning_goals=data.get("learning_goals", []),
            completed_modules=data.get("completed_modules", {}),
            preferences=data.get("preferences", {})
        )
        
    def update_skill_level(self, framework: str, module_id: str, skill_level: SkillLevel):
        """
        Update skill level for a specific framework and module.
        
        Args:
            framework: Framework name
            module_id: Module identifier
            skill_level: New skill level
        """
        if framework not in self.completed_modules:
            self.completed_modules[framework] = {}
            
        if module_id not in self.completed_modules[framework]:
            self.completed_modules[framework][module_id] = {}
            
        self.completed_modules[framework][module_id]["skill_level"] = skill_level.value
        
    def add_completed_module(
        self,
        framework: str,
        module_id: str,
        completion_date: str,
        score: float = 0.0,
        time_spent_seconds: int = 0
    ):
        """
        Add a completed module to the profile.
        
        Args:
            framework: Framework name
            module_id: Module identifier
            completion_date: Date of completion (ISO format)
            score: Completion score (0.0 to 1.0)
            time_spent_seconds: Time spent on the module
        """
        if framework not in self.completed_modules:
            self.completed_modules[framework] = {}
            
        self.completed_modules[framework][module_id] = {
            "completion_date": completion_date,
            "score": score,
            "time_spent_seconds": time_spent_seconds
        }
        
    def get_framework_progress(self, framework: str) -> Dict[str, Any]:
        """
        Get progress information for a specific framework.
        
        Args:
            framework: Framework name
            
        Returns:
            Progress information
        """
        if framework not in self.completed_modules:
            return {
                "modules_completed": 0,
                "average_score": 0.0,
                "total_time_spent_seconds": 0
            }
            
        modules = self.completed_modules[framework]
        total_score = sum(m.get("score", 0.0) for m in modules.values())
        total_time = sum(m.get("time_spent_seconds", 0) for m in modules.values())
        
        return {
            "modules_completed": len(modules),
            "average_score": total_score / len(modules) if modules else 0.0,
            "total_time_spent_seconds": total_time
        } 