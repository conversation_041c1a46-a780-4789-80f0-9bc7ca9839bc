"""
Comprehensive tests for vinagent Agent functionality.

This module tests:
- Agent initialization and configuration
- Tool management and execution
- Memory integration
- MCP client integration
- Graph compilation and execution
- Error handling and edge cases
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import BaseTool

# Import vinagent components
from vinagent.agent.agent import Agent, AgentMeta
from vinagent.register.tool import ToolManager
from vinagent.memory.memory import Memory
from vinagent.mcp.client import DistributedMCPClient


class MockLLM:
    """Mock LLM for testing."""
    
    def __init__(self, response_content="Mock response"):
        self.response_content = response_content
        self.model_name = "mock-llm"
        
    async def ainvoke(self, messages):
        """Mock async invoke method."""
        class MockResponse:
            def __init__(self, content):
                self.content = content
                
        return MockResponse(self.response_content)
    
    def invoke(self, messages):
        """Mock sync invoke method."""
        class MockResponse:
            def __init__(self, content):
                self.content = content
                
        return MockResponse(self.response_content)


class MockTool(BaseTool):
    """Mock tool for testing."""
    
    name: str = "mock_tool"
    description: str = "A mock tool for testing"
    
    def _run(self, query: str) -> str:
        return f"Mock tool executed with query: {query}"
    
    async def _arun(self, query: str) -> str:
        return f"Mock tool executed async with query: {query}"


class TestAgentInitialization:
    """Test Agent initialization and configuration."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        return MockLLM()
    
    @pytest.fixture
    def tools_path(self, temp_data_path):
        """Create a temporary tools.json file."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "mock_tool": {
                "name": "mock_tool",
                "description": "A mock tool",
                "module_path": "tests.test_vinagent_agent",
                "class_name": "MockTool"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return tools_file
    
    def test_agent_basic_initialization(self, mock_llm, tools_path):
        """Test basic agent initialization."""
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_path,
            description="Test agent",
            skills=["Testing"]
        )
        
        assert agent.llm == mock_llm
        assert agent.description == "Test agent"
        assert agent.skills == ["Testing"]
        assert agent.tools_path == tools_path
        assert isinstance(agent.tools_manager, ToolManager)
    
    def test_agent_with_tools_initialization(self, mock_llm, tools_path):
        """Test agent initialization with tools."""
        mock_tool = MockTool()
        
        agent = Agent(
            llm=mock_llm,
            tools=[mock_tool],
            tools_path=tools_path,
            description="Test agent with tools",
            skills=["Testing", "Tool usage"]
        )
        
        assert len(agent.tools) >= 1
        assert agent.description == "Test agent with tools"
        assert "Tool usage" in agent.skills
    
    def test_agent_with_memory_initialization(self, mock_llm, tools_path, temp_data_path):
        """Test agent initialization with memory."""
        memory_path = temp_data_path / "memory.jsonl"
        
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_path,
            memory_path=memory_path,
            description="Test agent with memory"
        )
        
        assert agent.memory is not None
        assert isinstance(agent.memory, Memory)
        assert agent.memory.memory_path == memory_path
    
    def test_agent_with_mcp_client(self, mock_llm, tools_path):
        """Test agent initialization with MCP client."""
        mock_mcp_client = Mock(spec=DistributedMCPClient)
        
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_path,
            mcp_client=mock_mcp_client,
            mcp_server_name="test_server",
            description="Test agent with MCP"
        )
        
        assert agent.mcp_client == mock_mcp_client
        assert agent.mcp_server_name == "test_server"
    
    def test_agent_reset_tools(self, mock_llm, tools_path):
        """Test agent with reset_tools flag."""
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_path,
            is_reset_tools=True,
            description="Test agent with reset tools"
        )
        
        # Should initialize successfully even with reset flag
        assert agent.tools_manager is not None
    
    def test_agent_reset_memory(self, mock_llm, tools_path, temp_data_path):
        """Test agent with reset_memory flag."""
        memory_path = temp_data_path / "memory.jsonl"
        
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_path,
            memory_path=memory_path,
            is_reset_memory=True,
            description="Test agent with reset memory"
        )
        
        assert agent.memory is not None
        assert agent.memory.is_reset_memory == True


class TestAgentInvocation:
    """Test Agent invocation methods."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        return MockLLM("This is a test response without tools.")
    
    @pytest.fixture
    def agent(self, mock_llm, temp_data_path):
        """Create a test agent."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        return Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Test agent for invocation"
        )
    
    def test_sync_invoke_without_tools(self, agent):
        """Test synchronous invocation without tool calls."""
        result = agent.invoke("Hello, how are you?")
        
        assert result is not None
        assert hasattr(result, 'content')
        assert "test response" in result.content.lower()
    
    @pytest.mark.asyncio
    async def test_async_invoke_without_tools(self, agent):
        """Test asynchronous invocation without tool calls."""
        result = await agent.ainvoke("Hello, how are you?")
        
        assert result is not None
        assert hasattr(result, 'content')
        assert "test response" in result.content.lower()
    
    def test_invoke_with_user_id(self, agent):
        """Test invocation with user ID."""
        result = agent.invoke("Hello", user_id="test_user_123")
        
        assert result is not None
        assert agent._user_id == "test_user_123"
    
    @pytest.mark.asyncio
    async def test_async_invoke_with_user_id(self, agent):
        """Test async invocation with user ID."""
        result = await agent.ainvoke("Hello", user_id="test_user_456")
        
        assert result is not None
        assert agent._user_id == "test_user_456"
    
    def test_invoke_with_memory_save(self, agent, temp_data_path):
        """Test invocation with memory saving."""
        # Add memory to agent
        memory_path = temp_data_path / "test_memory.jsonl"
        agent.memory = Memory(memory_path=memory_path)
        
        result = agent.invoke("Test message", is_save_memory=True, user_id="memory_test_user")
        
        assert result is not None
        # Memory should be saved (file should exist)
        assert memory_path.exists()


class TestAgentToolIntegration:
    """Test Agent tool integration and execution."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm_with_tool_call(self):
        """Mock LLM that returns a tool call."""
        tool_response = '''Here's the information you requested.

        {"tool_name": "mock_tool", "tool_type": "function", "arguments": {"query": "test"}, "module_path": "test_module"}
        '''
        return MockLLM(tool_response)
    
    @pytest.fixture
    def agent_with_tools(self, mock_llm_with_tool_call, temp_data_path):
        """Create agent with mock tools."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "mock_tool": {
                "name": "mock_tool",
                "description": "A mock tool",
                "module_path": "tests.test_vinagent_agent",
                "class_name": "MockTool"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        
        return Agent(
            llm=mock_llm_with_tool_call,
            tools=[MockTool()],
            tools_path=tools_file,
            description="Test agent with tools"
        )
    
    @patch('vinagent.register.tool.ToolManager._execute_tool')
    def test_tool_execution_sync(self, mock_execute_tool, agent_with_tools):
        """Test synchronous tool execution."""
        mock_execute_tool.return_value = "Tool executed successfully"

        # This test would need the actual tool calling mechanism to work
        # For now, we test that the agent can be invoked
        result = agent_with_tools.invoke("Execute a tool")
        assert result is not None


class TestAgentErrorHandling:
    """Test Agent error handling and edge cases."""

    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def mock_llm_error(self):
        """Mock LLM that raises errors."""
        llm = Mock()
        llm.invoke.side_effect = Exception("LLM error")
        llm.ainvoke = AsyncMock(side_effect=Exception("Async LLM error"))
        return llm

    def test_agent_with_invalid_tools_path(self, temp_data_path):
        """Test agent initialization with invalid tools path."""
        mock_llm = MockLLM()
        invalid_path = temp_data_path / "nonexistent" / "tools.json"

        # Should handle missing tools file gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=invalid_path,
            description="Test agent with invalid tools path"
        )

        assert agent is not None
        assert agent.tools_path == invalid_path

    def test_agent_with_invalid_memory_path(self, temp_data_path):
        """Test agent initialization with invalid memory path."""
        mock_llm = MockLLM()
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))

        invalid_memory_path = temp_data_path / "nonexistent" / "memory.jsonl"

        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=invalid_memory_path,
            description="Test agent with invalid memory path"
        )

        assert agent is not None
        assert agent.memory is not None

    def test_invoke_with_llm_error(self, mock_llm_error, temp_data_path):
        """Test invocation when LLM raises an error."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))

        agent = Agent(
            llm=mock_llm_error,
            tools=[],
            tools_path=tools_file,
            description="Test agent with error LLM"
        )

        # Should handle LLM errors gracefully
        with pytest.raises(Exception):
            agent.invoke("This should cause an error")

    @pytest.mark.asyncio
    async def test_async_invoke_with_llm_error(self, mock_llm_error, temp_data_path):
        """Test async invocation when LLM raises an error."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))

        agent = Agent(
            llm=mock_llm_error,
            tools=[],
            tools_path=tools_file,
            description="Test agent with error LLM"
        )

        # Should handle async LLM errors gracefully
        with pytest.raises(Exception):
            await agent.ainvoke("This should cause an async error")

    def test_agent_with_malformed_tools_json(self, temp_data_path):
        """Test agent with malformed tools.json file."""
        mock_llm = MockLLM()
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text("invalid json content")

        # Should handle malformed JSON gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Test agent with malformed tools JSON"
        )

        assert agent is not None


class TestAgentStreamingAndAsync:
    """Test Agent streaming and async functionality."""

    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def mock_streaming_llm(self):
        """Mock LLM with streaming capability."""
        llm = Mock()

        # Mock streaming response
        async def mock_astream(messages):
            chunks = ["Hello", " ", "world", "!"]
            for chunk in chunks:
                mock_chunk = Mock()
                mock_chunk.content = chunk
                yield mock_chunk

        llm.astream = mock_astream
        return llm

    @pytest.mark.asyncio
    async def test_agent_streaming(self, mock_streaming_llm, temp_data_path):
        """Test agent streaming functionality."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))

        agent = Agent(
            llm=mock_streaming_llm,
            tools=[],
            tools_path=tools_file,
            description="Test streaming agent"
        )

        # Test streaming method if it exists
        if hasattr(agent, 'astream'):
            chunks = []
            async for chunk in agent.astream("Hello"):
                if chunk is not None:
                    chunks.append(chunk)

            # Should receive some chunks
            assert len(chunks) >= 0  # May be 0 if streaming not implemented


class TestAgentMemoryIntegration:
    """Test Agent memory integration."""

    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def agent_with_memory(self, temp_data_path):
        """Create agent with memory."""
        mock_llm = MockLLM()
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        memory_path = temp_data_path / "memory.jsonl"

        return Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=memory_path,
            description="Test agent with memory"
        )

    def test_memory_save_and_load(self, agent_with_memory):
        """Test memory save and load functionality."""
        user_id = "test_user"

        # Save a message to memory
        if agent_with_memory.memory:
            agent_with_memory.save_memory("Test message", user_id=user_id)

            # Load memory
            loaded_memory = agent_with_memory.memory.load_memory(user_id=user_id)
            assert loaded_memory is not None

    def test_memory_persistence(self, agent_with_memory, temp_data_path):
        """Test memory persistence across agent instances."""
        user_id = "persistent_user"
        test_message = "This should persist"

        # Save message with first agent
        if agent_with_memory.memory:
            agent_with_memory.save_memory(test_message, user_id=user_id)

            # Create new agent with same memory path
            mock_llm = MockLLM()
            tools_file = temp_data_path / "tools.json"
            memory_path = temp_data_path / "memory.jsonl"

            new_agent = Agent(
                llm=mock_llm,
                tools=[],
                tools_path=tools_file,
                memory_path=memory_path,
                description="New agent with existing memory"
            )

            # Should be able to load previous memory
            if new_agent.memory:
                loaded_memory = new_agent.memory.load_memory(user_id=user_id)
                assert loaded_memory is not None
