"""
Enhanced Framework Database Manager for GAAPF.

This module implements robust database lookup systems for framework data access
with proper caching mechanisms and singleton pattern to prevent duplicate initialization.
"""

import sqlite3
import json
import threading
import time
from typing import Dict, List, Optional, Any, Set, Tuple
from pathlib import Path
from collections import defaultdict
import hashlib

from gaapf.config.framework_configs import (
    FrameworkModule, FrameworkConfig, SupportedFrameworks,
    ModuleDifficulty, ModuleType, LearningObjective, ModuleMetadata
)


class FrameworkDatabaseManager:
    """
    Enhanced database manager for framework data with caching and optimization.
    
    This class provides:
    - Robust database operations with connection pooling
    - Multi-level caching for performance optimization
    - Thread-safe operations
    - Singleton pattern to prevent duplicate initialization
    - Advanced search and filtering capabilities
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, data_path: Path = None):
        """Singleton pattern implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, data_path: Path = None):
        """Initialize the database manager (only once due to singleton)."""
        if self._initialized:
            return
            
        self.data_path = data_path or Path("data/framework_db")
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # Database connection
        self.db_path = self.data_path / "frameworks.db"
        self.connection_pool = []
        self.pool_lock = threading.Lock()
        
        # Multi-level caching
        self.module_cache = {}  # module_id -> FrameworkModule
        self.framework_cache = {}  # framework -> FrameworkConfig
        self.search_cache = {}  # search_hash -> results
        self.metadata_cache = {}  # Various metadata caches
        
        # Cache statistics
        self.cache_stats = {
            "hits": 0,
            "misses": 0,
            "invalidations": 0
        }
        
        # Cache configuration
        self.cache_ttl = 3600  # 1 hour TTL
        self.max_cache_size = 1000
        self.cache_timestamps = {}
        
        # Thread safety
        self.cache_lock = threading.RLock()
        
        # Initialize database
        self._initialize_database()
        self._load_initial_data()
        
        self._initialized = True
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get a database connection from the pool."""
        with self.pool_lock:
            if self.connection_pool:
                return self.connection_pool.pop()
            else:
                conn = sqlite3.connect(str(self.db_path), check_same_thread=False)
                conn.row_factory = sqlite3.Row
                return conn
    
    def _return_connection(self, conn: sqlite3.Connection):
        """Return a connection to the pool."""
        with self.pool_lock:
            if len(self.connection_pool) < 10:  # Max pool size
                self.connection_pool.append(conn)
            else:
                conn.close()
    
    def _initialize_database(self):
        """Initialize database schema."""
        conn = self._get_connection()
        try:
            # Create tables
            conn.executescript("""
                CREATE TABLE IF NOT EXISTS frameworks (
                    framework_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    version TEXT,
                    documentation_url TEXT,
                    repository_url TEXT,
                    installation_guide TEXT,
                    config_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE TABLE IF NOT EXISTS modules (
                    module_id TEXT PRIMARY KEY,
                    framework_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    difficulty TEXT,
                    module_type TEXT,
                    estimated_minutes INTEGER,
                    content_path TEXT,
                    module_data TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (framework_id) REFERENCES frameworks (framework_id)
                );
                
                CREATE TABLE IF NOT EXISTS module_prerequisites (
                    module_id TEXT,
                    prerequisite_id TEXT,
                    PRIMARY KEY (module_id, prerequisite_id),
                    FOREIGN KEY (module_id) REFERENCES modules (module_id)
                );
                
                CREATE TABLE IF NOT EXISTS module_topics (
                    module_id TEXT,
                    topic TEXT,
                    PRIMARY KEY (module_id, topic),
                    FOREIGN KEY (module_id) REFERENCES modules (module_id)
                );
                
                CREATE TABLE IF NOT EXISTS learning_objectives (
                    objective_id TEXT PRIMARY KEY,
                    module_id TEXT NOT NULL,
                    description TEXT,
                    skill_level TEXT,
                    assessment_criteria TEXT,
                    FOREIGN KEY (module_id) REFERENCES modules (module_id)
                );
                
                CREATE TABLE IF NOT EXISTS module_metadata (
                    module_id TEXT PRIMARY KEY,
                    tags TEXT,
                    learning_style TEXT,
                    complexity_score REAL,
                    practical_ratio REAL,
                    interaction_level TEXT,
                    tools_required TEXT,
                    external_resources TEXT,
                    last_updated TEXT,
                    version TEXT,
                    FOREIGN KEY (module_id) REFERENCES modules (module_id)
                );
                
                -- Indexes for performance
                CREATE INDEX IF NOT EXISTS idx_modules_framework ON modules (framework_id);
                CREATE INDEX IF NOT EXISTS idx_modules_difficulty ON modules (difficulty);
                CREATE INDEX IF NOT EXISTS idx_modules_type ON modules (module_type);
                CREATE INDEX IF NOT EXISTS idx_prerequisites ON module_prerequisites (prerequisite_id);
                CREATE INDEX IF NOT EXISTS idx_topics ON module_topics (topic);
            """)
            
            conn.commit()
        finally:
            self._return_connection(conn)
    
    def _load_initial_data(self):
        """Load initial framework data if database is empty."""
        conn = self._get_connection()
        try:
            # Check if we have any frameworks
            cursor = conn.execute("SELECT COUNT(*) FROM frameworks")
            count = cursor.fetchone()[0]
            
            if count == 0:
                # Load default framework configurations
                from gaapf.config.framework_configs import DEFAULT_FRAMEWORK_CONFIGS
                
                for framework_name, config in DEFAULT_FRAMEWORK_CONFIGS.items():
                    self._insert_framework_config(conn, framework_name, config)
                
                conn.commit()
        finally:
            self._return_connection(conn)
    
    def _insert_framework_config(self, conn: sqlite3.Connection, framework_name: str, config: FrameworkConfig):
        """Insert framework configuration into database."""
        # Insert framework
        conn.execute("""
            INSERT OR REPLACE INTO frameworks 
            (framework_id, name, description, version, documentation_url, repository_url, installation_guide, config_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            framework_name,
            config.name,
            config.description,
            config.version,
            config.documentation_url,
            config.repository_url,
            config.installation_guide,
            json.dumps(config.to_dict())
        ))
        
        # Insert modules
        for module in config.modules:
            self._insert_module(conn, module)
    
    def _insert_module(self, conn: sqlite3.Connection, module: FrameworkModule):
        """Insert module into database."""
        # Insert main module data
        conn.execute("""
            INSERT OR REPLACE INTO modules 
            (module_id, framework_id, title, description, difficulty, module_type, estimated_minutes, content_path, module_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            module.module_id,
            module.framework.value,
            module.title,
            module.description,
            module.difficulty.value,
            module.module_type.value,
            module.estimated_minutes,
            module.content_path,
            json.dumps(module.to_dict())
        ))
        
        # Insert prerequisites
        conn.execute("DELETE FROM module_prerequisites WHERE module_id = ?", (module.module_id,))
        for prereq in module.prerequisites:
            conn.execute("""
                INSERT INTO module_prerequisites (module_id, prerequisite_id) VALUES (?, ?)
            """, (module.module_id, prereq))
        
        # Insert topics
        conn.execute("DELETE FROM module_topics WHERE module_id = ?", (module.module_id,))
        for topic in module.topics:
            conn.execute("""
                INSERT INTO module_topics (module_id, topic) VALUES (?, ?)
            """, (module.module_id, topic))
        
        # Insert learning objectives
        conn.execute("DELETE FROM learning_objectives WHERE module_id = ?", (module.module_id,))
        for obj in module.learning_objectives:
            conn.execute("""
                INSERT INTO learning_objectives (objective_id, module_id, description, skill_level, assessment_criteria)
                VALUES (?, ?, ?, ?, ?)
            """, (
                obj.objective_id,
                module.module_id,
                obj.description,
                obj.skill_level,
                json.dumps(obj.assessment_criteria)
            ))
        
        # Insert metadata
        if module.metadata:
            conn.execute("""
                INSERT OR REPLACE INTO module_metadata 
                (module_id, tags, learning_style, complexity_score, practical_ratio, interaction_level, 
                 tools_required, external_resources, last_updated, version)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                module.module_id,
                json.dumps(module.metadata.tags),
                json.dumps(module.metadata.learning_style),
                module.metadata.complexity_score,
                module.metadata.practical_ratio,
                module.metadata.interaction_level,
                json.dumps(module.metadata.tools_required),
                json.dumps(module.metadata.external_resources),
                module.metadata.last_updated,
                module.metadata.version
            ))
    
    def get_module(self, module_id: str, use_cache: bool = True) -> Optional[FrameworkModule]:
        """
        Get a module by ID with caching.
        
        Args:
            module_id: Module identifier
            use_cache: Whether to use cache
            
        Returns:
            FrameworkModule or None if not found
        """
        # Check cache first
        if use_cache:
            with self.cache_lock:
                if module_id in self.module_cache:
                    # Check TTL
                    if self._is_cache_valid(f"module_{module_id}"):
                        self.cache_stats["hits"] += 1
                        return self.module_cache[module_id]
                    else:
                        # Cache expired
                        del self.module_cache[module_id]
                        self.cache_stats["invalidations"] += 1
        
        # Cache miss - fetch from database
        self.cache_stats["misses"] += 1
        
        conn = self._get_connection()
        try:
            cursor = conn.execute("""
                SELECT m.*, f.name as framework_name
                FROM modules m
                JOIN frameworks f ON m.framework_id = f.framework_id
                WHERE m.module_id = ?
            """, (module_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            # Reconstruct module from database
            module_data = json.loads(row["module_data"])
            module = FrameworkModule.from_dict(module_data)
            
            # Cache the result
            if use_cache:
                with self.cache_lock:
                    self._add_to_cache(f"module_{module_id}", module, self.module_cache)
            
            return module
            
        finally:
            self._return_connection(conn)

    def get_framework_modules(self, framework: str, use_cache: bool = True) -> List[FrameworkModule]:
        """
        Get all modules for a framework with caching.

        Args:
            framework: Framework identifier
            use_cache: Whether to use cache

        Returns:
            List of FrameworkModule objects
        """
        cache_key = f"framework_modules_{framework}"

        # Check cache first
        if use_cache:
            with self.cache_lock:
                if cache_key in self.metadata_cache:
                    if self._is_cache_valid(cache_key):
                        self.cache_stats["hits"] += 1
                        return self.metadata_cache[cache_key]
                    else:
                        del self.metadata_cache[cache_key]
                        self.cache_stats["invalidations"] += 1

        # Cache miss - fetch from database
        self.cache_stats["misses"] += 1

        conn = self._get_connection()
        try:
            cursor = conn.execute("""
                SELECT module_data FROM modules WHERE framework_id = ?
                ORDER BY difficulty, title
            """, (framework,))

            modules = []
            for row in cursor:
                module_data = json.loads(row["module_data"])
                module = FrameworkModule.from_dict(module_data)
                modules.append(module)

            # Cache the result
            if use_cache:
                with self.cache_lock:
                    self._add_to_cache(cache_key, modules, self.metadata_cache)

            return modules

        finally:
            self._return_connection(conn)

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self.cache_timestamps:
            return False

        return time.time() - self.cache_timestamps[cache_key] < self.cache_ttl

    def _add_to_cache(self, cache_key: str, value: Any, cache_dict: Dict):
        """Add entry to cache with size management."""
        # Remove oldest entries if cache is full
        if len(cache_dict) >= self.max_cache_size:
            # Remove 10% of oldest entries
            oldest_keys = sorted(
                self.cache_timestamps.keys(),
                key=lambda k: self.cache_timestamps[k]
            )[:self.max_cache_size // 10]

            for old_key in oldest_keys:
                cache_dict.pop(old_key, None)
                self.cache_timestamps.pop(old_key, None)

        cache_dict[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        with self.cache_lock:
            total_requests = self.cache_stats["hits"] + self.cache_stats["misses"]
            hit_rate = (self.cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0

            return {
                "cache_stats": self.cache_stats.copy(),
                "hit_rate_percent": hit_rate,
                "cache_sizes": {
                    "modules": len(self.module_cache),
                    "frameworks": len(self.framework_cache),
                    "searches": len(self.search_cache),
                    "metadata": len(self.metadata_cache)
                },
                "total_cached_items": (
                    len(self.module_cache) + len(self.framework_cache) +
                    len(self.search_cache) + len(self.metadata_cache)
                )
            }


# Global instance
_db_manager_instance = None

def get_framework_database() -> FrameworkDatabaseManager:
    """Get the global framework database manager instance."""
    global _db_manager_instance
    if _db_manager_instance is None:
        _db_manager_instance = FrameworkDatabaseManager()
    return _db_manager_instance
