# GAAPF - Guidance AI Agent for Python Framework

🤖 **An Adaptive Multi-Agent Learning System for AI Framework Education**

GAAPF is a cutting-edge educational platform that uses the novel "Adaptive Learning Constellation" architecture to provide personalized, interactive learning experiences for Python AI frameworks. Built with Lang<PERSON>hain, LangGraph, and advanced temporal optimization algorithms, integrated with the powerful vinagent library.

## 🌟 Key Features

### 🔗 Adaptive Learning Constellations
- **Dynamic Agent Networks**: Multi-agent systems that adapt in real-time based on user learning patterns
- **12 Specialized Agents**: Instructor, Code Assistant, Documentation Expert, Practice Facilitator, Assessment, Mentor, Research Assistant, Project Guide, Troubleshooter, Motivational Coach, Knowledge Synthesizer, Progress Tracker
- **Context-Aware Handoffs**: Intelligent agent coordination for seamless learning experiences
- **5 Constellation Types**: Knowledge Intensive, Hands-On Focused, Theory-Practice Balanced, Basic Learning, Guided Learning

### 📊 Temporal Learning Optimization
- **Effectiveness Tracking**: Continuous monitoring of learning outcomes and engagement
- **Pattern Recognition**: AI-powered analysis of optimal learning configurations
- **Personalized Recommendations**: Constellation selection based on individual learning patterns
- **Adaptive Engine**: Real-time learning path optimization

### 🎯 Comprehensive Framework Support
- **LangChain**: Complete learning path from basics to advanced agent systems
- **LangGraph**: Stateful multi-agent application development
- **Extensible Architecture**: Ready framework for adding CrewAI, AutoGen, LlamaIndex

### 🚀 Modern Technology Stack
- **VinAgent Integration**: Leverages the powerful vinagent library for agent management, tools, and memory
- **LangChain 0.3.x**: Latest LLM orchestration framework
- **LangGraph 0.4.x**: Advanced graph-based agent workflows
- **CLI Interface**: Real LLM integration with actual AI responses
- **Streamlit Demo**: Visual interface for system demonstration
- **Tavily Integration**: AI-powered search and documentation discovery
- **Pydantic 2.x**: Type-safe configuration and data validation
- **Modern Python**: Built for Python 3.10+

## 🚀 Quick Start

### Prerequisites
- Python 3.10 or higher
- At least one LLM API key:
  - **Google Gemini** (Recommended - free tier available)
  - **OpenAI GPT** (Pay per use)
  - **Anthropic Claude** (Pay per use)
  - **Together AI** (Cost-effective, open-source models)
- **Tavily API Key** (Optional - for enhanced search capabilities)

### Installation

1. **Navigate to the GAAPF directory**
```bash
cd gaapf/
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment variables**
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env file with your API keys
# At least one LLM API key is required:
# - OPENAI_API_KEY (for GPT models)
# - GOOGLE_API_KEY (for Gemini - recommended, has free tier)
# - ANTHROPIC_API_KEY (for Claude)
# - TOGETHER_API_KEY (for Together AI - cost-effective open-source models)
# - TAVILY_API_KEY (optional - for enhanced search)
```

4. **Run the CLI interface**
```bash
python -m gaapf
```

### 🧪 Testing the Installation

Run the basic test suite to ensure everything is working:

```bash
python test_basic.py
```

## 🎯 CLI Interface (Real LLM Integration)

The CLI provides the full experience with **actual AI responses**:

- ✅ **Real LLM API calls** to Google Gemini, OpenAI GPT, Anthropic Claude, or Together AI
- ✅ **Intelligent agent selection** based on your questions
- ✅ **Personalized learning paths** adapted to your skill level
- ✅ **Natural conversation** with specialized AI agents
- ✅ **Progress tracking** and temporal optimization
- ✅ **Advanced search** with Tavily integration for real-time framework discovery
- ✅ **Environment-based configuration** using .env files

**Quick CLI Demo:**
```bash
# Start the CLI
python -m gaapf

# Follow the interactive setup:
# 1. Profile creation (experience, skills, goals)
# 2. Framework selection (LangChain, LangGraph, etc.)
# 3. Start learning with real AI assistance!

# Example conversation:
You: What is LangChain and how do I get started?
🤖 Instructor: LangChain is a powerful framework for building applications with Large Language Models...

You: Show me a simple code example
🤖 Code Assistant: Here's a basic LangChain example to get you started:
```python
from langchain.llms import OpenAI
...
```
```

## 🏗️ System Architecture

GAAPF integrates with the vinagent library to provide a comprehensive learning platform:

### Core Components
- **Learning Hub Core**: Central coordination and session management
- **Constellation Manager**: Dynamic agent team formation and orchestration
- **Temporal State Manager**: Learning effectiveness tracking and optimization
- **VinAgent Integration**: Leverages vinagent's Agent, StateGraph, tools, and memory systems

### 12 Specialized Agents
1. **Instructor**: Theoretical explanations and conceptual understanding
2. **Code Assistant**: Practical code examples and implementation guidance
3. **Documentation Expert**: Official documentation and API references
4. **Practice Facilitator**: Hands-on exercises and guided tutorials
5. **Mentor**: Personalized guidance and learning strategy
6. **Assessment Agent**: Progress evaluation and skill assessment
7. **Research Assistant**: Information discovery and research support
8. **Project Guide**: End-to-end project development assistance
9. **Troubleshooter**: Problem diagnosis and debugging help
10. **Motivational Coach**: Encouragement and motivation support
11. **Knowledge Synthesizer**: Concept integration and knowledge mapping
12. **Progress Tracker**: Learning analytics and progress monitoring

## 🔧 Configuration

### User Profile Customization
```python
# Skill levels: none, beginner, intermediate, advanced, expert
# Learning paces: slow, moderate, fast, intensive
# Learning styles: visual, hands_on, theoretical, mixed
# Difficulty progression: gradual, moderate, aggressive
```

### Framework Support
- **LangChain**: Complete curriculum with agent orchestration
- **LangGraph**: Stateful multi-agent workflows and patterns
- **Extensible**: Ready for CrewAI, AutoGen, LlamaIndex integration

## 🧪 Development

### Prerequisites
- Python 3.10+
- LLM API keys (OpenAI, Google, Anthropic, or Together AI)
- Git

### Setting up Development Environment
```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run tests
python test_basic.py
```

### Project Structure
```
gaapf/
├── agents/              # 12 specialized agent implementations
├── core/               # Core system components
│   ├── constellation.py    # Constellation management
│   ├── temporal_state.py   # Learning optimization
│   └── learning_hub.py     # Central coordination
├── config/             # Configuration and user profiles
├── interfaces/         # CLI and web interfaces
├── memory/             # Memory management systems
├── tools/              # Tool integrations
└── requirements.txt    # Dependencies
```

## 📊 Performance & Metrics

### Learning Effectiveness Metrics
- **Comprehension Score**: Understanding level measurement via LLM analysis
- **Engagement Score**: User interaction and interest tracking
- **Completion Rate**: Task and module completion tracking
- **Time Efficiency**: Learning speed optimization
- **Retention Estimate**: Knowledge retention prediction
- **Agent Handoff Efficiency**: Smooth transitions between specialists

## 🤝 Integration with VinAgent

GAAPF leverages the vinagent library for:
- **Agent Management**: Core agent functionality and lifecycle
- **Tool Integration**: Comprehensive tool registration and execution
- **Memory Systems**: Persistent conversation and learning memory
- **State Management**: LangGraph-based workflow orchestration
- **MCP Support**: Model Context Protocol integration

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **VinAgent Team** for the powerful agent framework foundation
- **LangChain Team** for the incredible LLM orchestration framework
- **LangGraph Team** for advanced graph-based agent capabilities
- **Streamlit Team** for the beautiful web interface framework
- **Tavily Team** for AI-powered search and discovery capabilities
- **The AI Community** for continuous inspiration and collaboration