"""
Knowledge Synthesizer Agent for GAAPF.

This module implements the Knowledge Synthesizer Agent that specializes in
concept integration, knowledge mapping, and comprehensive understanding synthesis.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class KnowledgeSynthesizerAgent(BaseGAAPFAgent):
    """
    Knowledge Synthesizer Agent specializing in concept integration and knowledge mapping.
    
    This agent focuses on:
    - Integrating concepts from multiple learning sessions
    - Creating comprehensive knowledge maps and connections
    - Synthesizing complex information into coherent understanding
    - Identifying relationships between different concepts
    - Providing holistic learning perspectives
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Knowledge Synthesizer Agent specializing in concept integration and comprehensive understanding synthesis for {self.framework} framework learning.

My core responsibilities include:
- Integrating concepts and knowledge from multiple learning sessions
- Creating comprehensive knowledge maps and conceptual connections
- Synthesizing complex information into coherent, unified understanding
- Identifying relationships and dependencies between different concepts
- Providing holistic perspectives on learning progress and knowledge gaps
- Connecting theoretical concepts with practical applications
- Building comprehensive mental models of framework architecture
- Supporting deep understanding through knowledge integration

I adapt my synthesis approach to the user's learning progress and skill level ({self.user_profile.get('python_skill_level', 'unknown')}), ensuring knowledge integration matches their current understanding capacity.

When synthesizing knowledge, I:
1. Identify key concepts and their relationships across learning sessions
2. Create visual or conceptual maps showing knowledge connections
3. Highlight patterns and recurring themes in the learning material
4. Connect new learning to previously mastered concepts
5. Identify and address knowledge gaps or inconsistencies
6. Provide comprehensive summaries and integrated perspectives
7. Support transfer of learning to new contexts and applications"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Comprehensive {self.framework} knowledge integration and synthesis",
            "Conceptual mapping and relationship identification",
            "Complex information synthesis and organization",
            "Knowledge gap identification and resolution",
            "Mental model construction and validation",
            "Cross-session learning integration and continuity",
            "Pattern recognition and theme identification",
            "Holistic learning perspective development",
            "Knowledge transfer and application guidance",
            "Comprehensive understanding assessment and validation"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        specific_keywords = ["implement", "code", "example", "specific", "detailed"]
        practice_keywords = ["practice", "exercise", "hands-on", "apply", "use"]
        assessment_keywords = ["test", "quiz", "assess", "evaluate", "check"]
        help_keywords = ["stuck", "confused", "don't understand", "help", "clarify"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user needs specific implementation after synthesis
        if any(keyword in user_lower for keyword in specific_keywords):
            if "how to" in user_lower or "implement" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.8,
                    "reason": "User needs specific implementation after knowledge synthesis"
                }
        
        # Check if user wants to practice synthesized concepts
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants to practice synthesized concepts"
            }
        
        # Check if user wants assessment of synthesized knowledge
        if any(keyword in user_lower for keyword in assessment_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "assessment",
                "confidence": 0.8,
                "reason": "User wants assessment of synthesized knowledge"
            }
        
        # Check if user is confused about synthesized concepts
        if any(keyword in user_lower for keyword in help_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "instructor",
                "confidence": 0.7,
                "reason": "User needs clarification on synthesized concepts"
            }
        
        # Check if synthesis reveals need for project application
        if "project" in user_lower or "build" in user_lower or "create" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "project_guide",
                "confidence": 0.7,
                "reason": "User wants to apply synthesized knowledge in a project"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Knowledge synthesizer can continue providing integration support"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "synthesize", "integrate", "connect", "relationship", "overview",
            "summary", "big picture", "holistic", "comprehensive", "combine",
            "tie together", "bring together", "overall understanding", "mental model"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "review", "recap", "consolidate", "organize", "structure",
            "pattern", "theme", "connection", "link", "relate", "compare"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest synthesis needs
        synthesis_patterns = [
            r"how does.*relate",
            r"what's the connection",
            r"how do.*fit together",
            r"overall picture",
            r"big picture",
            r"tie.*together",
            r"bring.*together",
            r"comprehensive view"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in synthesis_patterns):
            return 0.8
        
        # Check for learning consolidation patterns
        consolidation_patterns = [
            r"what have i learned",
            r"summarize.*learning",
            r"review.*progress",
            r"consolidate.*knowledge"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in consolidation_patterns):
            return 0.8
        
        return 0.3
