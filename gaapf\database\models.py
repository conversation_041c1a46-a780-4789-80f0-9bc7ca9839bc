"""
Database models for GAAPF framework information storage.

This module defines SQLite-based models for storing framework configurations,
modules, and metadata that agents can query during learning sessions.
"""

import sqlite3
import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime

from gaapf.config.framework_configs import (
    SupportedFrameworks, 
    FrameworkConfig, 
    FrameworkModule,
    ModuleDifficulty,
    ModuleType
)


@dataclass
class FrameworkDB:
    """Database model for framework information."""
    id: Optional[int] = None
    framework_id: str = ""
    name: str = ""
    description: str = ""
    version: str = ""
    learning_paths: str = ""  # JSON string
    resources: str = ""  # JSON string
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


@dataclass
class ModuleDB:
    """Database model for framework module information."""
    id: Optional[int] = None
    framework_id: str = ""
    module_id: str = ""
    title: str = ""
    description: str = ""
    difficulty: str = ""
    module_type: str = ""
    prerequisites: str = ""  # JSON string
    topics: str = ""  # JSON string
    estimated_minutes: int = 60
    content_path: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class DatabaseManager:
    """
    Manages SQLite database operations for framework information.
    
    This class handles:
    1. Database initialization and schema creation
    2. Framework and module CRUD operations
    3. Query methods for agents to access framework information
    """
    
    def __init__(self, db_path: Path):
        """
        Initialize the database manager.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize the database and create tables if they don't exist."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create frameworks table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS frameworks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    framework_id TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT NOT NULL,
                    version TEXT NOT NULL,
                    learning_paths TEXT DEFAULT '{}',
                    resources TEXT DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create modules table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS modules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    framework_id TEXT NOT NULL,
                    module_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    difficulty TEXT NOT NULL,
                    module_type TEXT NOT NULL,
                    prerequisites TEXT DEFAULT '[]',
                    topics TEXT DEFAULT '[]',
                    estimated_minutes INTEGER DEFAULT 60,
                    content_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (framework_id) REFERENCES frameworks (framework_id),
                    UNIQUE(framework_id, module_id)
                )
            """)
            
            # Create indexes for better query performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_frameworks_id ON frameworks(framework_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_modules_framework ON modules(framework_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_modules_id ON modules(module_id)")
            
            conn.commit()
    
    def store_framework_config(self, config: FrameworkConfig) -> bool:
        """
        Store a framework configuration in the database.
        
        Args:
            config: FrameworkConfig instance to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Insert or update framework
                cursor.execute("""
                    INSERT OR REPLACE INTO frameworks 
                    (framework_id, name, description, version, learning_paths, resources, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    config.framework.value,
                    config.name,
                    config.description,
                    config.version,
                    json.dumps(config.learning_paths),
                    json.dumps(config.resources),
                    datetime.now().isoformat()
                ))
                
                # Delete existing modules for this framework
                cursor.execute("DELETE FROM modules WHERE framework_id = ?", (config.framework.value,))
                
                # Insert modules
                for module in config.modules:
                    cursor.execute("""
                        INSERT INTO modules 
                        (framework_id, module_id, title, description, difficulty, module_type, 
                         prerequisites, topics, estimated_minutes, content_path)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        config.framework.value,
                        module.module_id,
                        module.title,
                        module.description,
                        module.difficulty.value,
                        module.module_type.value,
                        json.dumps(module.prerequisites),
                        json.dumps(module.topics),
                        module.estimated_minutes,
                        module.content_path
                    ))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"Error storing framework config: {e}")
            return False
    
    def get_framework_info(self, framework_id: str) -> Optional[Dict[str, Any]]:
        """
        Get framework information by ID.
        
        Args:
            framework_id: Framework identifier
            
        Returns:
            Dictionary with framework information or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT framework_id, name, description, version, learning_paths, resources
                    FROM frameworks WHERE framework_id = ?
                """, (framework_id,))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'framework_id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'version': row[3],
                        'learning_paths': json.loads(row[4]),
                        'resources': json.loads(row[5])
                    }
                return None
                
        except Exception as e:
            print(f"Error getting framework info: {e}")
            return None
    
    def get_framework_modules(self, framework_id: str) -> List[Dict[str, Any]]:
        """
        Get all modules for a framework.
        
        Args:
            framework_id: Framework identifier
            
        Returns:
            List of module dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT module_id, title, description, difficulty, module_type, 
                           prerequisites, topics, estimated_minutes, content_path
                    FROM modules WHERE framework_id = ?
                    ORDER BY module_id
                """, (framework_id,))
                
                modules = []
                for row in cursor.fetchall():
                    modules.append({
                        'module_id': row[0],
                        'title': row[1],
                        'description': row[2],
                        'difficulty': row[3],
                        'module_type': row[4],
                        'prerequisites': json.loads(row[5]),
                        'topics': json.loads(row[6]),
                        'estimated_minutes': row[7],
                        'content_path': row[8]
                    })
                
                return modules
                
        except Exception as e:
            print(f"Error getting framework modules: {e}")
            return []
    
    def get_module_info(self, framework_id: str, module_id: str) -> Optional[Dict[str, Any]]:
        """
        Get specific module information.
        
        Args:
            framework_id: Framework identifier
            module_id: Module identifier
            
        Returns:
            Dictionary with module information or None if not found
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT module_id, title, description, difficulty, module_type, 
                           prerequisites, topics, estimated_minutes, content_path
                    FROM modules WHERE framework_id = ? AND module_id = ?
                """, (framework_id, module_id))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'module_id': row[0],
                        'title': row[1],
                        'description': row[2],
                        'difficulty': row[3],
                        'module_type': row[4],
                        'prerequisites': json.loads(row[5]),
                        'topics': json.loads(row[6]),
                        'estimated_minutes': row[7],
                        'content_path': row[8]
                    }
                return None
                
        except Exception as e:
            print(f"Error getting module info: {e}")
            return None
    
    def search_framework_content(self, query: str, framework_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search framework and module content by query.
        
        Args:
            query: Search query
            framework_id: Optional framework to limit search to
            
        Returns:
            List of matching results
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Search in frameworks and modules
                if framework_id:
                    cursor.execute("""
                        SELECT 'framework' as type, framework_id, name, description, '' as module_id
                        FROM frameworks 
                        WHERE framework_id = ? AND (name LIKE ? OR description LIKE ?)
                        UNION ALL
                        SELECT 'module' as type, framework_id, title as name, description, module_id
                        FROM modules 
                        WHERE framework_id = ? AND (title LIKE ? OR description LIKE ? OR topics LIKE ?)
                    """, (framework_id, f'%{query}%', f'%{query}%', 
                          framework_id, f'%{query}%', f'%{query}%', f'%{query}%'))
                else:
                    cursor.execute("""
                        SELECT 'framework' as type, framework_id, name, description, '' as module_id
                        FROM frameworks 
                        WHERE name LIKE ? OR description LIKE ?
                        UNION ALL
                        SELECT 'module' as type, framework_id, title as name, description, module_id
                        FROM modules 
                        WHERE title LIKE ? OR description LIKE ? OR topics LIKE ?
                    """, (f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%', f'%{query}%'))
                
                results = []
                for row in cursor.fetchall():
                    results.append({
                        'type': row[0],
                        'framework_id': row[1],
                        'name': row[2],
                        'description': row[3],
                        'module_id': row[4] if row[4] else None
                    })
                
                return results
                
        except Exception as e:
            print(f"Error searching framework content: {e}")
            return []
    
    def get_all_frameworks(self) -> List[Dict[str, Any]]:
        """
        Get all available frameworks.
        
        Returns:
            List of framework dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT framework_id, name, description, version
                    FROM frameworks
                    ORDER BY name
                """)
                
                frameworks = []
                for row in cursor.fetchall():
                    frameworks.append({
                        'framework_id': row[0],
                        'name': row[1],
                        'description': row[2],
                        'version': row[3]
                    })
                
                return frameworks
                
        except Exception as e:
            print(f"Error getting all frameworks: {e}")
            return []
