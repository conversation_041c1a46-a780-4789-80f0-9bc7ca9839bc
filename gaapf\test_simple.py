#!/usr/bin/env python3
"""
Simple test script for GAAPF system structure.

This script tests the basic structure and imports without requiring
full LLM integration or external dependencies.
"""

import sys
from pathlib import Path

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_imports():
    """Test basic imports."""
    print("🧪 Testing Basic Imports")
    
    try:
        # Test configuration imports
        from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
        print("✅ User profiles configuration imported successfully")
        
        from gaapf.config.framework_configs import SupportedFrameworks, FrameworkConfig
        print("✅ Framework configurations imported successfully")
        
        # Test that we can create basic objects
        profile = UserProfile(
            user_id="test_user",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            learning_pace=LearningPace.MODERATE,
            preferred_learning_style=LearningStyle.HANDS_ON,
            learning_goals=["Learn <PERSON>hain", "Build AI applications"]
        )
        print("✅ User profile creation successful")
        
        # Test framework config
        frameworks = SupportedFrameworks.list_frameworks()
        print(f"✅ Available frameworks: {frameworks}")
        
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_directory_structure():
    """Test that all required directories exist."""
    print("\n📁 Testing Directory Structure")
    
    required_dirs = [
        "gaapf/agents",
        "gaapf/core", 
        "gaapf/config",
        "gaapf/interfaces",
        "gaapf/interfaces/cli",
        "gaapf/interfaces/web",
        "gaapf/memory",
        "gaapf/tools"
    ]
    
    all_exist = True
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} (missing)")
            all_exist = False
    
    return all_exist

def test_agent_files():
    """Test that agent files exist."""
    print("\n🤖 Testing Agent Files")
    
    agent_files = [
        "gaapf/agents/base_agent.py",
        "gaapf/agents/instructor_agent.py",
        "gaapf/agents/code_assistant_agent.py",
        "gaapf/agents/documentation_expert_agent.py",
        "gaapf/agents/practice_facilitator_agent.py",
        "gaapf/agents/mentor_agent.py"
    ]
    
    all_exist = True
    for file_path in agent_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def test_core_files():
    """Test that core system files exist."""
    print("\n🏗️ Testing Core System Files")
    
    core_files = [
        "gaapf/core/constellation.py",
        "gaapf/core/temporal_state.py",
        "gaapf/core/learning_hub.py"
    ]
    
    all_exist = True
    for file_path in core_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def test_interface_files():
    """Test that interface files exist."""
    print("\n🖥️ Testing Interface Files")
    
    interface_files = [
        "gaapf/main.py",
        "gaapf/__main__.py",
        "gaapf/streamlit_demo.py",
        "gaapf/interfaces/cli/app.py",
        "gaapf/interfaces/web/app.py"
    ]
    
    all_exist = True
    for file_path in interface_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def test_documentation():
    """Test that documentation files exist."""
    print("\n📚 Testing Documentation")
    
    doc_files = [
        "gaapf/README.md",
        "gaapf/ARCHITECTURE.md",
        "gaapf/requirements.txt",
        "gaapf/setup.py"
    ]
    
    all_exist = True
    for file_path in doc_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def main():
    """Main test function."""
    print("🧪 GAAPF Simple Structure Test")
    print("=" * 50)
    
    # Run all tests
    tests = [
        ("Basic Imports", test_imports),
        ("Directory Structure", test_directory_structure),
        ("Agent Files", test_agent_files),
        ("Core Files", test_core_files),
        ("Interface Files", test_interface_files),
        ("Documentation", test_documentation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All structure tests passed! GAAPF project structure is complete.")
        print("\n📝 Next Steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Configure API keys (see README.md)")
        print("3. Run the system: python -m gaapf --interface cli")
        return 0
    else:
        print("\n❌ Some structure tests failed. Please check the missing files.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
