"""
Motivational Coach Agent for GAAPF.

This module implements the Motivational Coach Agent that specializes in
providing encouragement, motivation, and emotional support during learning.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class MotivationalCoachAgent(BaseGAAPFAgent):
    """
    Motivational Coach Agent specializing in encouragement and learning support.
    
    This agent focuses on:
    - Providing encouragement and motivation during challenging learning
    - Offering emotional support and confidence building
    - Helping overcome learning obstacles and frustrations
    - Celebrating achievements and progress milestones
    - Maintaining learner engagement and persistence
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Motivational Coach Agent specializing in encouragement and emotional support for {self.framework} framework learning.

My core responsibilities include:
- Providing encouragement and motivation during challenging learning phases
- Offering emotional support and confidence building strategies
- Helping overcome learning obstacles, frustrations, and setbacks
- Celebrating achievements, progress milestones, and small wins
- Maintaining learner engagement, persistence, and positive mindset
- Providing perspective on learning challenges and growth opportunities
- Offering strategies for managing learning stress and overwhelm
- Supporting goal-setting and maintaining learning momentum

I adapt my motivational approach to the user's personality, learning style ({self.user_profile.get('preferred_learning_style', 'mixed')}), and current emotional state, providing personalized encouragement and support.

When providing motivational support, I:
1. Acknowledge and validate the learner's feelings and challenges
2. Provide specific, genuine encouragement and positive reinforcement
3. Help reframe challenges as learning opportunities
4. Celebrate progress and achievements, no matter how small
5. Offer practical strategies for overcoming specific obstacles
6. Share inspiring perspectives on the learning journey
7. Help maintain focus on long-term goals and growth"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Motivational support for {self.framework} learning challenges",
            "Emotional intelligence and empathetic communication",
            "Confidence building and self-efficacy enhancement",
            "Learning obstacle identification and resolution strategies",
            "Achievement recognition and celebration techniques",
            "Goal-setting and momentum maintenance strategies",
            "Stress management and overwhelm reduction techniques",
            "Growth mindset development and resilience building",
            "Personalized encouragement and positive reinforcement",
            "Learning journey perspective and long-term vision support"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        technical_keywords = ["code", "implement", "syntax", "error", "debug", "fix"]
        learning_keywords = ["learn", "understand", "explain", "teach", "concept"]
        practice_keywords = ["practice", "exercise", "hands-on", "try", "do"]
        assessment_keywords = ["assess", "evaluate", "check", "test", "quiz"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user is ready to tackle technical challenges after motivation
        if any(keyword in user_lower for keyword in technical_keywords):
            if "ready" in content_lower or "motivated" in content_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.7,
                    "reason": "User is motivated and ready for technical assistance"
                }
        
        # Check if user wants to learn after getting motivated
        if any(keyword in user_lower for keyword in learning_keywords):
            if "let's" in user_lower or "ready to" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User is motivated and ready to learn concepts"
                }
        
        # Check if user wants to practice after motivation
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User is motivated and wants to practice"
            }
        
        # Check if user wants assessment after gaining confidence
        if any(keyword in user_lower for keyword in assessment_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "assessment",
                "confidence": 0.7,
                "reason": "User is confident and ready for assessment"
            }
        
        # Check if user needs ongoing mentoring support
        if "guidance" in user_lower or "mentor" in user_lower or "support" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "mentor",
                "confidence": 0.7,
                "reason": "User needs ongoing mentoring support"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Motivational coach can continue providing emotional support"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "frustrated", "overwhelmed", "stuck", "difficult", "hard", "impossible",
            "give up", "quit", "discouraged", "demotivated", "tired", "stressed",
            "can't do", "too hard", "not good enough", "failing", "hopeless"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "challenging", "struggle", "confused", "lost", "unsure", "doubt",
            "worried", "anxious", "nervous", "intimidated", "scared", "afraid"
        ]
        
        # Positive engagement keywords
        positive_keywords = [
            "celebrate", "achievement", "progress", "success", "accomplished",
            "proud", "excited", "motivated", "inspired", "confident"
        ]
        
        # Check for high confidence indicators (negative emotions)
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.95
        
        # Check for positive engagement needs
        if any(keyword in message_lower for keyword in positive_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.8
        
        # Check for question patterns that suggest motivational needs
        motivational_patterns = [
            r"should i give up",
            r"is it worth",
            r"am i good enough",
            r"can i do this",
            r"will i ever",
            r"how do you stay motivated",
            r"feeling discouraged"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in motivational_patterns):
            return 0.9
        
        return 0.2
