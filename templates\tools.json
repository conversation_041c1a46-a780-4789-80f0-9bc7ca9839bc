{"add": {"tool_name": "add", "arguments": {"a": "integer", "b": "integer"}, "return": "content_and_artifact", "docstring": "Add two numbers", "module_path": "__mcp__", "tool_type": "mcp", "tool_call_id": "tool_e25d9a0c-ec8c-483b-ada7-70779a23c668"}, "multiply": {"tool_name": "multiply", "arguments": {"a": "integer", "b": "integer"}, "return": "content_and_artifact", "docstring": "Multiply two numbers", "module_path": "__mcp__", "tool_type": "mcp", "tool_call_id": "tool_088b618f-093b-4d29-a8ed-1836cd474486"}, "get_framework_info": {"tool_name": "get_framework_info", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get detailed information about a specific framework including its description,\n    version, learning paths, and available resources. Use this when you need to provide information\n    about a framework's capabilities, structure, or general information.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_818dfaac-b81c-4b9e-9543-87611a5e8ccb", "is_runtime": true}, "get_module_info": {"tool_name": "get_module_info", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get detailed information about a specific learning module including its\n    description, difficulty level, topics covered, prerequisites, and estimated completion time.\n    Use this when you need to provide specific information about a learning module.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_1f99935c-7848-43e3-a895-11740a81588e", "is_runtime": true}, "search_framework_content": {"tool_name": "search_framework_content", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Search for information across frameworks and modules based on a query.\n    This tool searches through framework names, descriptions, module titles, descriptions, and topics.\n    Use this when you need to find relevant information about specific concepts, features, or topics.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_38a396ad-6c7a-4d89-8d61-e1a071ee3c15", "is_runtime": true}, "list_frameworks": {"tool_name": "list_frameworks", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get a list of all available frameworks in the system with their basic information.\n    Use this when you need to show what frameworks are available for learning.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_cfe4ae6b-7170-4c3c-b26f-58fc0e352486", "is_runtime": true}, "get_framework_modules": {"tool_name": "get_framework_modules", "arguments": {"kwargs": "Any"}, "return": "Any", "docstring": "Get all learning modules available for a specific framework with their details.\n    Use this when you need to show what modules are available for learning in a particular framework.", "module_path": "__runtime__", "tool_type": "function", "tool_call_id": "tool_0cd73d01-9da7-4c54-9a1b-947987ffeb32", "is_runtime": true}}