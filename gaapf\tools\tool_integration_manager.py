"""
Tool Integration Manager for GAAPF.

This module provides comprehensive tool integration and management capabilities,
coordinating all available tools for optimal learning experiences.
"""

import os
from typing import Dict, List, Optional, Any
from pathlib import Path

from langchain_core.tools import BaseTool

from gaapf.tools.framework_tools import FrameworkToolsFactory
from gaapf.tools.tavily_search_tools import create_tavily_tools
from gaapf.tools.file_tools import create_file_tools
from gaapf.tools.learning_assessment_tools import create_learning_assessment_tools


class ToolIntegrationManager:
    """
    Comprehensive tool integration manager for GAAPF.
    
    This class manages all available tools and provides intelligent tool selection
    and coordination for optimal learning experiences.
    """
    
    def __init__(self, data_path: Path):
        """
        Initialize the tool integration manager.
        
        Args:
            data_path: Path to the data directory
        """
        self.data_path = data_path
        self.framework_tools_factory = FrameworkToolsFactory.get_instance(data_path)
        self._tool_cache = {}
        self._tool_categories = {}
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize all available tools and categorize them."""
        # Framework tools
        framework_tools = self.framework_tools_factory.create_framework_tools()
        self._tool_categories["framework"] = framework_tools
        
        # Search tools (Tavily)
        search_tools = create_tavily_tools()
        self._tool_categories["search"] = search_tools
        
        # File tools
        file_tools = create_file_tools()
        self._tool_categories["file"] = file_tools
        
        # Learning assessment tools
        assessment_tools = create_learning_assessment_tools()
        self._tool_categories["assessment"] = assessment_tools
        
        # Create unified tool cache
        all_tools = []
        for category_tools in self._tool_categories.values():
            all_tools.extend(category_tools)
        
        self._tool_cache = {tool.name: tool for tool in all_tools}
    
    def get_all_tools(self) -> List[BaseTool]:
        """Get all available tools."""
        return list(self._tool_cache.values())
    
    def get_tools_by_category(self, category: str) -> List[BaseTool]:
        """
        Get tools by category.
        
        Args:
            category: Tool category (framework, search, file, assessment)
            
        Returns:
            List of tools in the specified category
        """
        return self._tool_categories.get(category, [])
    
    def get_tool_by_name(self, tool_name: str) -> Optional[BaseTool]:
        """
        Get a specific tool by name.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Tool instance or None if not found
        """
        return self._tool_cache.get(tool_name)
    
    def get_recommended_tools_for_agent(self, agent_type: str, framework: str = None) -> List[BaseTool]:
        """
        Get recommended tools for a specific agent type.
        
        Args:
            agent_type: Type of agent (instructor, code_assistant, etc.)
            framework: Optional framework context
            
        Returns:
            List of recommended tools for the agent
        """
        recommendations = {
            "instructor": [
                "get_framework_info",
                "get_module_info",
                "search_framework_content",
                "tavily_search",
                "framework_specific_search"
            ],
            "code_assistant": [
                "generate_code",
                "execute_code",
                "get_framework_info",
                "get_module_info",
                "tavily_search"
            ],
            "documentation_expert": [
                "get_framework_info",
                "get_module_info",
                "search_framework_content",
                "tavily_search",
                "tavily_extract",
                "tavily_crawl"
            ],
            "practice_facilitator": [
                "generate_code",
                "execute_code",
                "generate_quiz",
                "get_module_info"
            ],
            "mentor": [
                "get_framework_info",
                "assess_skills",
                "tavily_search",
                "generate_quiz"
            ],
            "assessment": [
                "generate_quiz",
                "assess_skills",
                "get_module_info",
                "execute_code"
            ],
            "research_assistant": [
                "tavily_search",
                "tavily_extract",
                "tavily_crawl",
                "framework_specific_search",
                "search_framework_content"
            ],
            "project_guide": [
                "generate_code",
                "get_framework_info",
                "get_module_info",
                "tavily_search"
            ],
            "troubleshooter": [
                "execute_code",
                "tavily_search",
                "framework_specific_search",
                "get_framework_info"
            ],
            "motivational_coach": [
                "assess_skills",
                "get_framework_info"
            ],
            "knowledge_synthesizer": [
                "get_framework_info",
                "get_module_info",
                "search_framework_content",
                "assess_skills"
            ],
            "progress_tracker": [
                "assess_skills",
                "get_framework_info",
                "get_module_info"
            ]
        }
        
        recommended_tool_names = recommendations.get(agent_type, [])
        recommended_tools = []
        
        for tool_name in recommended_tool_names:
            tool = self.get_tool_by_name(tool_name)
            if tool:
                recommended_tools.append(tool)
        
        return recommended_tools
    
    def get_tools_for_learning_phase(self, phase: str, framework: str = None) -> List[BaseTool]:
        """
        Get tools appropriate for a specific learning phase.
        
        Args:
            phase: Learning phase (introduction, practice, assessment, project)
            framework: Optional framework context
            
        Returns:
            List of tools appropriate for the learning phase
        """
        phase_tools = {
            "introduction": [
                "get_framework_info",
                "get_module_info",
                "tavily_search",
                "framework_specific_search"
            ],
            "practice": [
                "generate_code",
                "execute_code",
                "get_module_info",
                "generate_quiz"
            ],
            "assessment": [
                "generate_quiz",
                "assess_skills",
                "execute_code"
            ],
            "project": [
                "generate_code",
                "execute_code",
                "get_framework_info",
                "tavily_search"
            ],
            "research": [
                "tavily_search",
                "tavily_extract",
                "tavily_crawl",
                "framework_specific_search",
                "search_framework_content"
            ]
        }
        
        tool_names = phase_tools.get(phase, [])
        tools = []
        
        for tool_name in tool_names:
            tool = self.get_tool_by_name(tool_name)
            if tool:
                tools.append(tool)
        
        return tools
    
    def get_tool_usage_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about tool availability and usage.
        
        Returns:
            Dictionary with tool statistics
        """
        stats = {
            "total_tools": len(self._tool_cache),
            "categories": {
                category: len(tools) 
                for category, tools in self._tool_categories.items()
            },
            "tool_availability": {},
            "configuration_status": {}
        }
        
        # Check tool availability
        stats["tool_availability"]["tavily_search"] = bool(os.getenv("TAVILY_API_KEY"))
        stats["tool_availability"]["framework_database"] = self.data_path.exists()
        
        # Configuration status
        stats["configuration_status"]["data_path"] = str(self.data_path)
        stats["configuration_status"]["tavily_configured"] = bool(os.getenv("TAVILY_API_KEY"))
        
        return stats
    
    def validate_tool_configuration(self) -> Dict[str, Any]:
        """
        Validate tool configuration and identify any issues.
        
        Returns:
            Dictionary with validation results
        """
        validation = {
            "status": "valid",
            "issues": [],
            "warnings": [],
            "recommendations": []
        }
        
        # Check Tavily API key
        if not os.getenv("TAVILY_API_KEY"):
            validation["warnings"].append("TAVILY_API_KEY not configured - search tools will be limited")
            validation["recommendations"].append("Set TAVILY_API_KEY environment variable for enhanced search capabilities")
        
        # Check database availability
        db_path = self.data_path / "frameworks.db"
        if not db_path.exists():
            validation["issues"].append("Framework database not found")
            validation["recommendations"].append("Initialize framework database for full functionality")
            validation["status"] = "issues_found"
        
        # Check data directory
        if not self.data_path.exists():
            validation["issues"].append("Data directory does not exist")
            validation["recommendations"].append(f"Create data directory at {self.data_path}")
            validation["status"] = "issues_found"
        
        return validation
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions of all available tools.
        
        Returns:
            Dictionary mapping tool names to descriptions
        """
        return {
            tool_name: tool.description 
            for tool_name, tool in self._tool_cache.items()
        }
    
    def suggest_tools_for_query(self, query: str, context: Dict[str, Any] = None) -> List[str]:
        """
        Suggest appropriate tools for a given query.
        
        Args:
            query: User query or task description
            context: Optional context information
            
        Returns:
            List of suggested tool names
        """
        query_lower = query.lower()
        suggestions = []
        
        # Keyword-based tool suggestions
        if any(word in query_lower for word in ["search", "find", "look up", "research"]):
            suggestions.extend(["tavily_search", "framework_specific_search", "search_framework_content"])
        
        if any(word in query_lower for word in ["code", "example", "implement", "write"]):
            suggestions.extend(["generate_code", "execute_code"])
        
        if any(word in query_lower for word in ["quiz", "test", "assess", "evaluate"]):
            suggestions.extend(["generate_quiz", "assess_skills"])
        
        if any(word in query_lower for word in ["framework", "module", "information"]):
            suggestions.extend(["get_framework_info", "get_module_info"])
        
        # Remove duplicates while preserving order
        unique_suggestions = []
        for suggestion in suggestions:
            if suggestion not in unique_suggestions:
                unique_suggestions.append(suggestion)
        
        return unique_suggestions[:5]  # Return top 5 suggestions


# Global instance for easy access
_tool_manager_instance = None

def get_tool_manager(data_path: Path = None) -> ToolIntegrationManager:
    """
    Get the global tool manager instance.
    
    Args:
        data_path: Path to data directory (required for first call)
        
    Returns:
        ToolIntegrationManager instance
    """
    global _tool_manager_instance
    
    if _tool_manager_instance is None:
        if data_path is None:
            raise ValueError("data_path is required for first initialization")
        _tool_manager_instance = ToolIntegrationManager(data_path)
    
    return _tool_manager_instance
