"""
Project Guide Agent for GAAPF.

This module implements the Project Guide Agent that specializes in
end-to-end project development assistance and project management guidance.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class ProjectGuideAgent(BaseGAAPFAgent):
    """
    Project Guide Agent specializing in end-to-end project development assistance.
    
    This agent focuses on:
    - Guiding users through complete project development
    - Providing project planning and architecture guidance
    - Helping with project structure and organization
    - Offering milestone tracking and progress management
    - Supporting project best practices and methodologies
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Project Guide Agent specializing in end-to-end project development assistance for {self.framework} framework projects.

My core responsibilities include:
- Guiding users through complete project development lifecycles
- Providing project planning, architecture, and design guidance
- Helping structure and organize projects effectively
- Offering milestone tracking and progress management
- Supporting project best practices and development methodologies
- Assisting with project requirements analysis and planning
- Providing guidance on project scalability and maintainability
- Helping with project deployment and production considerations

I adapt my guidance to the user's skill level ({self.user_profile.get('python_skill_level', 'unknown')}) and project complexity, ensuring appropriate project scope and realistic timelines.

When guiding projects, I:
1. Help define clear project goals and requirements
2. Suggest appropriate project structure and architecture
3. Break down complex projects into manageable milestones
4. Provide guidance on best practices and coding standards
5. Offer regular check-ins and progress assessments
6. Help troubleshoot project-specific challenges
7. Guide through testing, deployment, and maintenance phases"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"End-to-end {self.framework} project development guidance",
            "Project planning and architecture design",
            "Project structure and organization best practices",
            "Milestone tracking and progress management",
            "Requirements analysis and project scoping",
            "Code organization and modular design principles",
            "Testing strategies and quality assurance guidance",
            "Deployment and production readiness assessment",
            "Project scalability and maintainability planning",
            "Development methodology and workflow guidance"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        code_keywords = ["implement", "code", "write", "syntax", "function", "class"]
        theory_keywords = ["explain", "concept", "theory", "understand", "what is"]
        practice_keywords = ["practice", "exercise", "hands-on", "tutorial", "step by step"]
        help_keywords = ["stuck", "error", "problem", "issue", "debug", "fix", "broken"]
        assessment_keywords = ["assess", "evaluate", "check", "review", "feedback"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user needs specific code implementation help
        if any(keyword in user_lower for keyword in code_keywords):
            if "how to implement" in user_lower or "write code" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.8,
                    "reason": "User needs specific code implementation help within project"
                }
        
        # Check if user needs theoretical explanation of project concepts
        if any(keyword in user_lower for keyword in theory_keywords):
            if "why" in user_lower or "concept" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs theoretical explanation of project concepts"
                }
        
        # Check if user wants hands-on practice with project components
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants hands-on practice with project components"
            }
        
        # Check if user is having technical problems with the project
        if any(keyword in user_lower for keyword in help_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.8,
                "reason": "User needs technical troubleshooting help with project"
            }
        
        # Check if user wants project assessment or review
        if any(keyword in user_lower for keyword in assessment_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "assessment",
                "confidence": 0.8,
                "reason": "User wants project assessment or code review"
            }
        
        # Check if user needs motivational support for project challenges
        if "overwhelmed" in user_lower or "too difficult" in user_lower or "give up" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "motivational_coach",
                "confidence": 0.8,
                "reason": "User needs motivational support for project challenges"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Project guide can continue providing project development guidance"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "project", "build", "create", "develop", "application", "app",
            "architecture", "structure", "organize", "plan", "design",
            "milestone", "roadmap", "timeline", "requirements", "scope"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "start", "begin", "setup", "initialize", "framework", "template",
            "best practices", "methodology", "workflow", "process", "steps"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest project guidance needs
        question_patterns = [
            r"how to build",
            r"how to create",
            r"how to develop",
            r"how to structure",
            r"how to organize",
            r"what should i build",
            r"project ideas"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in question_patterns):
            return 0.8
        
        return 0.3
