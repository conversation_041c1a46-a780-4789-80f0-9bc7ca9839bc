from abc import ABC, abstractmethod
from pathlib import Path
from typing import Optional, Literal, Union
import json
import os
import time
from aucodb.graph import LLMGraphTransformer
from vinagent.logging_config import get_logger

# Get logger using centralized configuration
logger = get_logger(__name__)


class PathValidationError(Exception):
    """Custom exception for path validation errors."""
    pass


def validate_memory_path(path: Union[str, Path]) -> Path:
    """
    Validate and normalize memory path for cross-platform compatibility.

    Args:
        path: Memory file path (string or Path object)

    Returns:
        Path: Validated and normalized Path object

    Raises:
        PathValidationError: If path is invalid or unsafe
    """
    if not path:
        raise PathValidationError("Memory path cannot be empty or None")

    # Convert to Path object
    if isinstance(path, str):
        # Handle empty or whitespace-only strings
        if not path.strip():
            raise PathValidationError("Memory path cannot be empty or whitespace-only")
        path_obj = Path(path.strip())
    else:
        path_obj = Path(path)

    # Normalize path for cross-platform compatibility
    try:
        path_obj = path_obj.resolve()
    except (OSError, ValueError) as e:
        raise PathValidationError(f"Invalid path format: {e}")

    # Validate file extension
    valid_extensions = {'.json', '.jsonl'}
    if path_obj.suffix.lower() not in valid_extensions:
        raise PathValidationError(
            f"Memory path must have a valid extension {valid_extensions}. "
            f"Got: {path_obj.suffix}"
        )

    # Check for invalid characters in filename
    filename = path_obj.name
    invalid_chars = set('<>:"|?*')
    if any(char in filename for char in invalid_chars):
        raise PathValidationError(
            f"Memory filename contains invalid characters: {invalid_chars & set(filename)}"
        )

    # Validate path length (Windows has 260 char limit, be conservative)
    if len(str(path_obj)) > 250:
        raise PathValidationError(
            f"Memory path too long ({len(str(path_obj))} chars). Maximum 250 characters."
        )

    # Check for reserved names on Windows
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
        'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
    }
    stem_upper = path_obj.stem.upper()
    if stem_upper in reserved_names:
        raise PathValidationError(
            f"Memory filename '{path_obj.stem}' is a reserved name on Windows"
        )

    return path_obj


def safe_json_load(file_path: Path, default_value=None):
    """
    Safely load JSON file with error handling.

    Args:
        file_path: Path to JSON file
        default_value: Value to return if file is corrupted or missing

    Returns:
        Loaded JSON data or default_value
    """
    if default_value is None:
        default_value = {}

    try:
        if not file_path.exists():
            logger.info(f"Memory file does not exist, will create: {file_path}")
            return default_value

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content:
                logger.warning(f"Memory file is empty: {file_path}")
                return default_value

            return json.loads(content)

    except json.JSONDecodeError as e:
        logger.error(f"Corrupted JSON in memory file {file_path}: {e}")
        # Create backup of corrupted file
        backup_path = file_path.with_suffix(f'.corrupted_{int(time.time())}.bak')
        try:
            file_path.rename(backup_path)
            logger.info(f"Corrupted file backed up to: {backup_path}")
        except Exception as backup_error:
            logger.error(f"Failed to backup corrupted file: {backup_error}")
        return default_value

    except (IOError, OSError) as e:
        logger.error(f"IO error reading memory file {file_path}: {e}")
        return default_value

    except Exception as e:
        logger.error(f"Unexpected error reading memory file {file_path}: {e}")
        return default_value


def safe_json_save(data, file_path: Path, user_id: str = None):
    """
    Safely save JSON data with error handling and atomic writes.

    Args:
        data: Data to save
        file_path: Path to save to
        user_id: Optional user ID for logging
    """
    try:
        # Ensure parent directory exists
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # Use atomic write (write to temp file, then rename)
        temp_path = file_path.with_suffix(f'.tmp_{os.getpid()}')

        with open(temp_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)

        # Atomic rename
        temp_path.replace(file_path)

        if user_id:
            logger.info(f"Successfully saved memory for user: {user_id}")

    except (IOError, OSError) as e:
        logger.error(f"Failed to save memory file {file_path}: {e}")
        # Clean up temp file if it exists
        temp_path = file_path.with_suffix(f'.tmp_{os.getpid()}')
        if temp_path.exists():
            try:
                temp_path.unlink()
            except:
                pass
        raise

    except Exception as e:
        logger.error(f"Unexpected error saving memory file {file_path}: {e}")
        raise


class MemoryMeta(ABC):
    @abstractmethod
    def update_memory(self, graph: list):
        pass
    
    @abstractmethod
    def save_short_term_memory(self, llm, message):
        pass

    @abstractmethod
    def save_memory(self, message: str, *args, **kwargs):
        pass

class Memory(MemoryMeta):
    '''This stores the memory of the conversation.
    '''
    def __init__(self,
            memory_path: Optional[Union[Path, str]] = Path('templates/memory.jsonl'),
            is_reset_memory: bool=False,
            is_logging: bool=False,
        *args, **kwargs):

        # Validate and normalize the memory path
        try:
            if memory_path is None:
                memory_path = Path('templates/memory.jsonl')
            self.memory_path = validate_memory_path(memory_path)
        except PathValidationError as e:
            logger.error(f"Invalid memory path: {e}")
            # Fallback to default path
            self.memory_path = Path('templates/memory.jsonl')
            logger.info(f"Using fallback memory path: {self.memory_path}")

        self.is_reset_memory = is_reset_memory
        self.is_logging = is_logging

        # Ensure parent directory exists
        try:
            self.memory_path.parent.mkdir(parents=True, exist_ok=True)
        except (OSError, PermissionError) as e:
            logger.error(f"Failed to create memory directory: {e}")
            raise

        # Initialize memory file
        self._initialize_memory_file()

    def _initialize_memory_file(self):
        """Initialize memory file with proper error handling."""
        try:
            if not self.memory_path.exists():
                # Create new empty memory file
                safe_json_save({}, self.memory_path)
                logger.info(f"Created new memory file: {self.memory_path}")
            elif self.is_reset_memory:
                # Reset existing memory file
                safe_json_save({}, self.memory_path)
                logger.info(f"Reset memory file: {self.memory_path}")
        except Exception as e:
            logger.error(f"Failed to initialize memory file: {e}")
            raise

    def load_memory(self, load_type: Literal['list', 'string'] = 'list', user_id: str = None):
        """
        Load memory data with robust error handling.

        Args:
            load_type: Format to return data ('list' or 'string')
            user_id: Optional user ID to filter data

        Returns:
            Memory data in requested format
        """
        # Validate user_id
        if user_id is not None:
            if not isinstance(user_id, str) or not user_id.strip():
                logger.warning(f"Invalid user_id: {user_id}, using None")
                user_id = None
            else:
                user_id = user_id.strip()

        # Load data using safe JSON loader
        data = safe_json_load(self.memory_path, default_value={})

        # Extract user-specific data
        if not user_id:  # Load all memory
            data_user = data
        else:
            if user_id in data:  # Load memory by user_id
                data_user = data[user_id]
            else:
                data_user = []

        # Return in requested format
        if load_type == 'list':
            return data_user
        elif load_type == 'string':
            if isinstance(data_user, list):
                message = self.revert_object_mess(data_user)
                return message
            else:
                logger.warning(f"Expected list for string conversion, got {type(data_user)}")
                return ""
        else:
            logger.error(f"Invalid load_type: {load_type}")
            return data_user

    def save_memory(self, obj: list, memory_path: Path, user_id: str):
        """
        Save memory data with robust error handling.

        Args:
            obj: Memory data to save
            memory_path: Path to save to
            user_id: User identifier
        """
        # Validate inputs
        if not isinstance(obj, list):
            logger.error(f"Expected list for memory data, got {type(obj)}")
            return

        if not user_id or not isinstance(user_id, str) or not user_id.strip():
            logger.error(f"Invalid user_id: {user_id}")
            return

        user_id = user_id.strip()

        try:
            # Load existing memory
            memory = self.load_memory(load_type='list')
            if not isinstance(memory, dict):
                logger.warning(f"Memory data is not dict, creating new: {type(memory)}")
                memory = {}

            # Update user's memory
            memory[user_id] = obj

            # Save using safe JSON saver
            safe_json_save(memory, memory_path, user_id)

            if self.is_logging:
                logger.info(f"Saved memory for user: {user_id}")

        except Exception as e:
            logger.error(f"Failed to save memory for user {user_id}: {e}")
            raise

    def save_short_term_memory(self, llm, message, user_id):
        graph_transformer = LLMGraphTransformer(
            llm = llm
        )
        graph = graph_transformer.generate_graph(message)        
        self.update_memory(graph, user_id)
        return graph

    def revert_object_mess(self, object: list[dict]):
        mess = []
        for line in object:
            head, _, relation, relation_properties, tail, _ = list(line.values())
            relation_additional= f"[{relation_properties}]" if relation_properties else ""
            mess.append(f"{head} -> {relation}{relation_additional} -> {tail}")
        mess = "\n".join(mess)
        return mess

    def update_memory(self, graph: list, user_id: str):
        memory_about_user = self.load_memory(load_type='list', user_id=user_id)
        if memory_about_user:
            index_memory = [(item['head'], item['relation'], item['tail']) for item in memory_about_user]
            index_memory_head_relation_tail_type = [(item['head'], item['relation'],  item['tail_type']) for item in memory_about_user]
        else:
            index_memory = []
            index_memory_head_relation_tail_type = []
            
        if graph:
            for line in graph:
                head, head_type, relation, relation_properties, tail, tail_type= list(line.values())
                lookup_hrt = (head, relation, tail)
                lookup_hrttp = (head, relation, tail_type)
                if lookup_hrt in index_memory:
                    if self.is_logging:
                        logger.info(f"Bypass {line}")
                    pass
                elif lookup_hrttp in index_memory_head_relation_tail_type:
                    index_match = index_memory_head_relation_tail_type.index(lookup_hrttp)
                    if self.is_logging:
                        logger.info(f"Update new line: {line}\nfrom old line {memory_about_user[index_match]}")
                    memory_about_user[index_match] = line
                else:
                    if self.is_logging:
                        logger.info(f"Insert new line: {line}")
                    memory_about_user.append(line)
        else:
            if self.is_logging:
                logger.info(f"No thing updated")
        
        self.save_memory(obj=memory_about_user, memory_path=self.memory_path, user_id=user_id)
        return memory_about_user
