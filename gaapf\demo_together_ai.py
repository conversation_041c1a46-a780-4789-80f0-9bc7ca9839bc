#!/usr/bin/env python3
"""
Demo script for Together AI integration with GAAPF.

This script demonstrates how to use Together AI as an LLM provider in the GAAPF system.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

def demo_together_ai_config():
    """Demonstrate Together AI configuration."""
    print("🚀 Together AI Integration Demo")
    print("=" * 50)
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Create configuration instance
        config = EnvironmentConfig()
        
        print("📋 Configuration Properties:")
        print(f"  Together API Key: {'✅ Set' if config.together_api_key else '❌ Not set'}")
        print(f"  Together Model: {config.together_model}")
        print(f"  Default Provider: {config.default_llm_provider}")
        
        # Show available providers
        providers = config.get_available_llm_providers()
        print(f"\n🤖 Available LLM Providers:")
        for provider, available in providers.items():
            status = "✅" if available else "❌"
            print(f"  {status} {provider.title()}")
        
        # Test Together AI specifically
        if providers.get('together', False):
            print(f"\n🎯 Testing Together AI LLM Creation...")
            try:
                llm = config.create_llm(provider='together')
                print(f"✅ Together AI LLM created successfully!")
                print(f"   Type: {type(llm).__name__}")
                print(f"   Model: {config.together_model}")
                return llm
            except Exception as e:
                print(f"❌ Together AI LLM creation failed: {e}")
                return None
        else:
            print(f"\n⚠️  Together AI not available (API key not configured)")
            print(f"   To use Together AI:")
            print(f"   1. Get API key from: https://api.together.xyz/settings/api-keys")
            print(f"   2. Add TOGETHER_API_KEY=your_key to .env file")
            print(f"   3. Optionally set DEFAULT_LLM_PROVIDER=together")
            return None
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return None

def demo_together_ai_usage(llm):
    """Demonstrate basic Together AI usage."""
    if not llm:
        print("\n⚠️  Skipping usage demo (LLM not available)")
        return
    
    print(f"\n💬 Testing Together AI Chat...")
    try:
        from langchain_core.messages import HumanMessage
        
        # Simple test message
        messages = [HumanMessage(content="Hello! Can you briefly explain what LangChain is?")]
        response = llm.invoke(messages)
        
        print(f"✅ Together AI Response:")
        print(f"   {response.content[:200]}...")
        
    except Exception as e:
        print(f"❌ Together AI usage test failed: {e}")

def demo_agent_integration():
    """Demonstrate Together AI integration with GAAPF agents."""
    print(f"\n🤖 Agent Integration Example:")
    print(f"   Together AI can be used with all GAAPF agents:")
    print(f"   - Instructor Agent: Teaching AI frameworks")
    print(f"   - Code Assistant: Code generation and debugging")
    print(f"   - Documentation Expert: Framework documentation")
    print(f"   - Practice Facilitator: Hands-on exercises")
    print(f"   - Mentor Agent: Learning guidance")
    
    print(f"\n📝 Example Usage:")
    print(f"""
# Set Together AI as default provider in .env
DEFAULT_LLM_PROVIDER=together
TOGETHER_API_KEY=your_together_api_key

# Run GAAPF CLI with Together AI
python -m gaapf

# Or programmatically:
from gaapf.config.env_config import get_config
from gaapf.agents.base_agent import create_agent

config = get_config()
llm = config.create_llm(provider='together')

agent = await create_agent(
    agent_type='instructor',
    user_profile=user_profile,
    framework='langchain',
    module_id='basics',
    session_id='demo',
    llm=llm
)
""")

def main():
    """Main demo function."""
    # Demo configuration
    llm = demo_together_ai_config()
    
    # Demo usage if available
    demo_together_ai_usage(llm)
    
    # Demo agent integration
    demo_agent_integration()
    
    print(f"\n🎉 Together AI Integration Demo Complete!")
    print(f"\n💡 Benefits of Together AI:")
    print(f"   - Cost-effective pricing")
    print(f"   - Access to open-source models (Llama, Mistral, etc.)")
    print(f"   - High performance and reliability")
    print(f"   - Easy integration with existing GAAPF workflows")

if __name__ == "__main__":
    main()
