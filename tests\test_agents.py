"""
Comprehensive tests for GAAPF agents.

This module tests all agent implementations including:
- Agent initialization and configuration
- Agent response generation
- Handoff analysis and decision making
- Tool integration and usage
- Memory integration
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from pathlib import Path
import tempfile
import json

from gaapf.agents.base_agent import BaseGAAPFAgent, create_agent
from gaapf.agents.instructor_agent import InstructorAgent
from gaapf.agents.code_assistant_agent import CodeAssistantAgent
from gaapf.agents.documentation_expert_agent import DocumentationExpertAgent
from gaapf.agents.practice_facilitator_agent import PracticeFacilitatorAgent
from gaapf.agents.mentor_agent import MentorAgent
from gaapf.agents.assessment_agent import AssessmentAgent
from gaapf.agents.research_assistant_agent import ResearchAssistantAgent
from gaapf.agents.project_guide_agent import ProjectGuideAgent
from gaapf.agents.troubleshooter_agent import TroubleshooterAgent
from gaapf.agents.motivational_coach_agent import MotivationalCoachAgent
from gaapf.agents.knowledge_synthesizer_agent import KnowledgeSynthesizerAgent
from gaapf.agents.progress_tracker_agent import ProgressTrackerAgent


class TestBaseAgent:
    """Test the base agent functionality."""
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return llm
    
    @pytest.fixture
    def user_profile(self):
        """Sample user profile for testing."""
        return {
            "user_id": "test_user",
            "python_skill_level": "intermediate",
            "preferred_learning_style": "hands_on",
            "learning_goals": ["master_langchain"]
        }
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_base_agent_initialization(self, mock_llm, user_profile, temp_data_path):
        """Test base agent initialization."""
        agent = BaseGAAPFAgent(
            agent_type="test",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        assert agent.agent_type == "test"
        assert agent.framework == "langchain"
        assert agent.module_id == "test_module"
        assert agent.session_id == "test_session"
        assert agent.user_profile == user_profile
    
    @pytest.mark.asyncio
    async def test_base_agent_invoke(self, mock_llm, user_profile, temp_data_path):
        """Test base agent invocation."""
        agent = BaseGAAPFAgent(
            agent_type="test",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        response = await agent.ainvoke(
            query="Test query",
            user_id="test_user"
        )
        
        assert response is not None
        mock_llm.ainvoke.assert_called_once()
    
    def test_confidence_score_calculation(self, mock_llm, user_profile, temp_data_path):
        """Test confidence score calculation."""
        agent = BaseGAAPFAgent(
            agent_type="test",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Test with different message types
        score1 = agent.get_confidence_score("How do I use LangChain?")
        score2 = agent.get_confidence_score("What is machine learning?")
        
        assert 0.0 <= score1 <= 1.0
        assert 0.0 <= score2 <= 1.0


class TestSpecializedAgents:
    """Test specialized agent implementations."""
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return llm
    
    @pytest.fixture
    def user_profile(self):
        """Sample user profile for testing."""
        return {
            "user_id": "test_user",
            "python_skill_level": "intermediate",
            "preferred_learning_style": "hands_on"
        }
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_instructor_agent_confidence(self, mock_llm, user_profile, temp_data_path):
        """Test instructor agent confidence scoring."""
        agent = InstructorAgent(
            agent_type="instructor",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Instructor should have high confidence for explanation requests
        score = agent.get_confidence_score("Explain how LangChain works")
        assert score > 0.7
        
        # Lower confidence for code requests
        score = agent.get_confidence_score("Write code for me")
        assert score < 0.7
    
    def test_code_assistant_confidence(self, mock_llm, user_profile, temp_data_path):
        """Test code assistant agent confidence scoring."""
        agent = CodeAssistantAgent(
            agent_type="code_assistant",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Code assistant should have high confidence for code requests
        score = agent.get_confidence_score("Write a LangChain example")
        assert score > 0.8
        
        # Lower confidence for theory questions
        score = agent.get_confidence_score("Explain the theory behind AI")
        assert score < 0.6
    
    def test_troubleshooter_confidence(self, mock_llm, user_profile, temp_data_path):
        """Test troubleshooter agent confidence scoring."""
        agent = TroubleshooterAgent(
            agent_type="troubleshooter",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Troubleshooter should have high confidence for error messages
        score = agent.get_confidence_score("I'm getting an error: AttributeError")
        assert score > 0.9
        
        # Lower confidence for general questions
        score = agent.get_confidence_score("How do I learn programming?")
        assert score < 0.5
    
    def test_motivational_coach_confidence(self, mock_llm, user_profile, temp_data_path):
        """Test motivational coach agent confidence scoring."""
        agent = MotivationalCoachAgent(
            agent_type="motivational_coach",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Motivational coach should have high confidence for emotional support
        score = agent.get_confidence_score("I'm feeling frustrated and want to give up")
        assert score > 0.9
        
        # Lower confidence for technical questions
        score = agent.get_confidence_score("How do I implement a chain?")
        assert score < 0.3


class TestAgentHandoffs:
    """Test agent handoff analysis and decision making."""
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return llm
    
    @pytest.fixture
    def user_profile(self):
        """Sample user profile for testing."""
        return {
            "user_id": "test_user",
            "python_skill_level": "intermediate"
        }
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_instructor_to_code_assistant_handoff(self, mock_llm, user_profile, temp_data_path):
        """Test handoff from instructor to code assistant."""
        agent = InstructorAgent(
            agent_type="instructor",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Content that should trigger handoff to code assistant
        content = "Now that you understand the concept, let's implement it in code."
        user_message = "Can you show me how to implement this?"
        
        handoff_analysis = agent._analyze_content_for_handoff(content, user_message)
        
        assert handoff_analysis["needs_handoff"] == True
        assert handoff_analysis["suggested_agent"] == "code_assistant"
        assert handoff_analysis["confidence"] > 0.6
    
    def test_code_assistant_to_troubleshooter_handoff(self, mock_llm, user_profile, temp_data_path):
        """Test handoff from code assistant to troubleshooter."""
        agent = CodeAssistantAgent(
            agent_type="code_assistant",
            llm=mock_llm,
            user_profile=user_profile,
            framework="langchain",
            module_id="test_module",
            session_id="test_session",
            data_path=temp_data_path
        )
        
        # Content that should trigger handoff to troubleshooter
        content = "Here's the code implementation."
        user_message = "I'm getting an error when I run this code"
        
        handoff_analysis = agent._analyze_content_for_handoff(content, user_message)
        
        assert handoff_analysis["needs_handoff"] == True
        assert handoff_analysis["suggested_agent"] == "troubleshooter"


class TestAgentFactory:
    """Test agent creation factory."""
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return llm
    
    @pytest.fixture
    def user_profile(self):
        """Sample user profile for testing."""
        return {
            "user_id": "test_user",
            "python_skill_level": "intermediate"
        }
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_create_all_agent_types(self, mock_llm, user_profile, temp_data_path):
        """Test creation of all agent types."""
        agent_types = [
            "instructor", "code_assistant", "documentation_expert",
            "practice_facilitator", "mentor", "assessment",
            "research_assistant", "project_guide", "troubleshooter",
            "motivational_coach", "knowledge_synthesizer", "progress_tracker"
        ]
        
        for agent_type in agent_types:
            agent = create_agent(
                agent_type=agent_type,
                llm=mock_llm,
                user_profile=user_profile,
                framework="langchain",
                module_id="test_module",
                session_id="test_session",
                data_path=temp_data_path
            )
            
            assert agent is not None
            assert agent.agent_type == agent_type
    
    def test_invalid_agent_type(self, mock_llm, user_profile, temp_data_path):
        """Test creation with invalid agent type."""
        with pytest.raises(ValueError):
            create_agent(
                agent_type="invalid_agent",
                llm=mock_llm,
                user_profile=user_profile,
                framework="langchain",
                module_id="test_module",
                session_id="test_session",
                data_path=temp_data_path
            )
