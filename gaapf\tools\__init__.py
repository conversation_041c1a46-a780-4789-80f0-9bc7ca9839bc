"""
Tools for GAAPF agents.
"""

from gaapf.tools.framework_tools import (
    FrameworkToolsFactory,
    FrameworkInfoTool,
    ModuleInfoTool,
    FrameworkSearchTool,
    ListFrameworksTool,
    FrameworkModulesTool
)

from gaapf.tools.tavily_search_tools import (
    create_tavily_tools,
    TavilySearchTool,
    TavilyExtractTool,
    TavilyCrawlTool,
    FrameworkSpecificSearchTool
)

from gaapf.tools.file_tools import (
    create_file_tools,
    CodeGenerationTool,
    CodeExecutionTool
)

from gaapf.tools.learning_assessment_tools import (
    create_learning_assessment_tools,
    QuizGenerationTool,
    SkillAssessmentTool
)

from gaapf.tools.tool_integration_manager import (
    ToolIntegrationManager,
    get_tool_manager
)

__all__ = [
    # Framework tools
    'FrameworkToolsFactory',
    'FrameworkInfoTool',
    'ModuleInfoTool',
    'FrameworkSearchTool',
    'ListFrameworksTool',
    'FrameworkModulesTool',

    # Search tools
    'create_tavily_tools',
    'TavilySearchTool',
    'TavilyExtractTool',
    'TavilyCrawlTool',
    'FrameworkSpecificSearchTool',

    # File tools
    'create_file_tools',
    'CodeGenerationTool',
    'CodeExecutionTool',

    # Assessment tools
    'create_learning_assessment_tools',
    'QuizGenerationTool',
    'SkillAssessmentTool',

    # Tool management
    'ToolIntegrationManager',
    'get_tool_manager'
]