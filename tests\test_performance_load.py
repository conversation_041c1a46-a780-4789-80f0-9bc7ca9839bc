"""
Performance and Load Tests for vinagent project.

This module tests:
- Performance benchmarks for critical paths
- Load testing under high concurrency
- Memory usage and resource consumption
- Response time measurements
- Throughput testing
- Scalability limits
- Resource cleanup and garbage collection
"""

import pytest
import tempfile
import json
import time
import threading
import psutil
import os
from pathlib import Path
from unittest.mock import Mock
from concurrent.futures import ThreadPoolExecutor, as_completed
import asyncio

# Import vinagent components
from vinagent.agent.agent import Agent
from vinagent.memory.memory import Memory
from vinagent.register.tool import ToolManager


class TestAgentPerformance:
    """Test Agent performance characteristics."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def fast_mock_llm(self):
        """Fast mock LLM for performance testing."""
        llm = Mock()
        llm.model_name = "fast-mock-llm"
        
        def mock_invoke(messages):
            # Simulate minimal processing time
            time.sleep(0.001)  # 1ms delay
            mock_response = Mock()
            mock_response.content = "Fast response"
            return mock_response
        
        async def mock_ainvoke(messages):
            # Simulate minimal async processing time
            await asyncio.sleep(0.001)  # 1ms delay
            mock_response = Mock()
            mock_response.content = "Fast async response"
            return mock_response
        
        llm.invoke = mock_invoke
        llm.ainvoke = mock_ainvoke
        return llm
    
    @pytest.fixture
    def performance_agent(self, fast_mock_llm, temp_data_path):
        """Create agent optimized for performance testing."""
        tools_file = temp_data_path / "perf_tools.json"
        tools_file.write_text(json.dumps({}))
        
        return Agent(
            llm=fast_mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Performance test agent"
        )
    
    @pytest.mark.benchmark(group="agent_invoke")
    def test_agent_invoke_performance(self, benchmark, performance_agent):
        """Benchmark agent invoke performance."""
        def invoke_agent():
            return performance_agent.invoke("Performance test message")
        
        # Benchmark the invoke method
        result = benchmark(invoke_agent)
        
        # Verify result is valid
        assert result is not None
        assert hasattr(result, 'content')
    
    @pytest.mark.benchmark(group="agent_invoke")
    @pytest.mark.asyncio
    async def test_agent_async_invoke_performance(self, benchmark, performance_agent):
        """Benchmark agent async invoke performance."""
        async def async_invoke_agent():
            return await performance_agent.ainvoke("Async performance test message")
        
        # Benchmark the async invoke method
        result = await benchmark(async_invoke_agent)
        
        # Verify result is valid
        assert result is not None
        assert hasattr(result, 'content')
    
    def test_agent_memory_usage(self, performance_agent):
        """Test agent memory usage during operation."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Perform multiple operations
        for i in range(100):
            response = performance_agent.invoke(f"Memory test message {i}")
            assert response is not None
        
        # Get final memory usage
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 50MB for 100 operations)
        assert memory_increase < 50 * 1024 * 1024  # 50MB
    
    def test_agent_response_time_consistency(self, performance_agent):
        """Test consistency of agent response times."""
        response_times = []
        
        # Measure response times for multiple invocations
        for i in range(50):
            start_time = time.time()
            response = performance_agent.invoke(f"Consistency test {i}")
            end_time = time.time()
            
            response_times.append(end_time - start_time)
            assert response is not None
        
        # Calculate statistics
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        
        # Response times should be consistent (max shouldn't be more than 10x min)
        assert max_time / min_time < 10
        
        # Average response time should be reasonable (less than 1 second)
        assert avg_time < 1.0


class TestMemoryPerformance:
    """Test Memory system performance."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.mark.benchmark(group="memory_operations")
    def test_memory_save_performance(self, benchmark, temp_data_path):
        """Benchmark memory save performance."""
        memory_path = temp_data_path / "perf_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        test_data = [{"role": "user", "content": "Performance test data"}]
        user_id = "perf_test_user"
        
        def save_memory():
            memory.save_memory(test_data, memory_path, user_id)
        
        # Benchmark the save operation
        benchmark(save_memory)
        
        # Verify data was saved
        assert memory_path.exists()
    
    @pytest.mark.benchmark(group="memory_operations")
    def test_memory_load_performance(self, benchmark, temp_data_path):
        """Benchmark memory load performance."""
        memory_path = temp_data_path / "perf_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Pre-populate memory with test data
        test_data = [{"role": "user", "content": f"Test message {i}"} for i in range(100)]
        user_id = "perf_test_user"
        memory.save_memory(test_data, memory_path, user_id)
        
        def load_memory():
            return memory.load_memory(user_id=user_id, load_type='list')
        
        # Benchmark the load operation
        result = benchmark(load_memory)
        
        # Verify data was loaded
        assert result is not None
        assert len(result) > 0
    
    def test_memory_large_dataset_performance(self, temp_data_path):
        """Test memory performance with large datasets."""
        memory_path = temp_data_path / "large_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Create large dataset (1000 entries)
        large_data = [
            {"role": "user", "content": f"Large dataset message {i}"}
            for i in range(1000)
        ]
        
        user_id = "large_data_user"
        
        # Measure save time
        start_time = time.time()
        memory.save_memory(large_data, memory_path, user_id)
        save_time = time.time() - start_time
        
        # Measure load time
        start_time = time.time()
        loaded_data = memory.load_memory(user_id=user_id, load_type='list')
        load_time = time.time() - start_time
        
        # Verify data integrity
        assert loaded_data is not None
        assert len(loaded_data) == 1000
        
        # Performance should be reasonable (less than 5 seconds each)
        assert save_time < 5.0
        assert load_time < 5.0
    
    def test_memory_concurrent_access_performance(self, temp_data_path):
        """Test memory performance under concurrent access."""
        memory_path = temp_data_path / "concurrent_memory.jsonl"
        
        def concurrent_memory_operation(user_id):
            memory = Memory(memory_path=memory_path)
            test_data = [{"role": "user", "content": f"Concurrent message from {user_id}"}]
            
            # Perform save and load operations
            start_time = time.time()
            memory.save_memory(test_data, memory_path, user_id)
            loaded_data = memory.load_memory(user_id=user_id, load_type='list')
            end_time = time.time()
            
            return end_time - start_time, loaded_data is not None
        
        # Run concurrent operations
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [
                executor.submit(concurrent_memory_operation, f"user_{i}")
                for i in range(20)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        # Verify all operations completed successfully
        assert len(results) == 20
        
        # Check that all operations succeeded
        for duration, success in results:
            assert success == True
            assert duration < 2.0  # Each operation should complete within 2 seconds


class TestToolManagerPerformance:
    """Test ToolManager performance."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def large_tools_config(self, temp_data_path):
        """Create large tools configuration for performance testing."""
        tools_file = temp_data_path / "large_tools.json"
        
        # Create configuration with many tools
        tools_data = {}
        for i in range(100):
            tools_data[f"tool_{i}"] = {
                "name": f"tool_{i}",
                "description": f"Performance test tool {i}",
                "tool_type": "function",
                "parameters": {
                    "param1": "string",
                    "param2": "number"
                }
            }
        
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return tools_file
    
    @pytest.mark.benchmark(group="tool_manager")
    def test_tool_manager_initialization_performance(self, benchmark, large_tools_config):
        """Benchmark ToolManager initialization with large configuration."""
        def init_tool_manager():
            return ToolManager(tools_path=large_tools_config)
        
        # Benchmark initialization
        tool_manager = benchmark(init_tool_manager)
        
        # Verify initialization succeeded
        assert tool_manager is not None
        assert tool_manager.tools_path == large_tools_config
    
    def test_tool_manager_memory_usage(self, large_tools_config):
        """Test ToolManager memory usage with large configuration."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Initialize ToolManager with large configuration
        tool_manager = ToolManager(tools_path=large_tools_config)
        
        # Get memory usage after initialization
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 20MB for 100 tools)
        assert memory_increase < 20 * 1024 * 1024  # 20MB
        
        # Verify tool manager is functional
        assert tool_manager is not None


class TestLoadTesting:
    """Test system behavior under load."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def load_test_agent(self, temp_data_path):
        """Create agent for load testing."""
        mock_llm = Mock()
        mock_llm.model_name = "load-test-llm"
        
        def mock_invoke(messages):
            # Simulate variable processing time
            time.sleep(0.01 + (hash(str(messages)) % 10) * 0.001)  # 10-20ms
            mock_response = Mock()
            mock_response.content = f"Load test response for {len(str(messages))} chars"
            return mock_response
        
        mock_llm.invoke = mock_invoke
        
        tools_file = temp_data_path / "load_tools.json"
        tools_file.write_text(json.dumps({}))
        
        return Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Load test agent"
        )
    
    def test_high_concurrency_load(self, load_test_agent):
        """Test system behavior under high concurrency."""
        def concurrent_invoke(message_id):
            try:
                start_time = time.time()
                response = load_test_agent.invoke(f"Concurrent message {message_id}")
                end_time = time.time()
                
                return {
                    "message_id": message_id,
                    "success": response is not None,
                    "duration": end_time - start_time,
                    "response_length": len(response.content) if response else 0
                }
            except Exception as e:
                return {
                    "message_id": message_id,
                    "success": False,
                    "error": str(e),
                    "duration": 0,
                    "response_length": 0
                }
        
        # Run high concurrency test
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [
                executor.submit(concurrent_invoke, i)
                for i in range(100)
            ]
            
            results = [future.result() for future in as_completed(futures)]
        
        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]
        
        # At least 90% of requests should succeed
        success_rate = len(successful_requests) / len(results)
        assert success_rate >= 0.9
        
        # Average response time should be reasonable
        if successful_requests:
            avg_duration = sum(r["duration"] for r in successful_requests) / len(successful_requests)
            assert avg_duration < 1.0  # Less than 1 second average
    
    def test_sustained_load(self, load_test_agent):
        """Test system behavior under sustained load."""
        duration_seconds = 10  # 10 second test
        start_time = time.time()
        request_count = 0
        successful_requests = 0
        
        while time.time() - start_time < duration_seconds:
            try:
                response = load_test_agent.invoke(f"Sustained load message {request_count}")
                if response is not None:
                    successful_requests += 1
                request_count += 1
                
                # Small delay to prevent overwhelming the system
                time.sleep(0.01)
                
            except Exception:
                request_count += 1
        
        # Calculate throughput
        actual_duration = time.time() - start_time
        throughput = request_count / actual_duration
        success_rate = successful_requests / request_count if request_count > 0 else 0
        
        # Should handle at least 10 requests per second
        assert throughput >= 10
        
        # Should maintain high success rate
        assert success_rate >= 0.95
    
    def test_memory_leak_detection(self, load_test_agent):
        """Test for memory leaks during extended operation."""
        # Get initial memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Perform many operations
        for i in range(200):
            response = load_test_agent.invoke(f"Memory leak test {i}")
            assert response is not None
            
            # Periodically check memory usage
            if i % 50 == 0:
                current_memory = process.memory_info().rss
                memory_increase = current_memory - initial_memory
                
                # Memory increase should be bounded (less than 100MB)
                assert memory_increase < 100 * 1024 * 1024  # 100MB
        
        # Final memory check
        final_memory = process.memory_info().rss
        total_memory_increase = final_memory - initial_memory
        
        # Total memory increase should be reasonable
        assert total_memory_increase < 150 * 1024 * 1024  # 150MB
