"""
Web search tool implementation for GAAPF.

This module implements a web search tool using the Tavily API.
"""

from typing import Dict, List, Optional, Any
import os
from pydantic import BaseModel, Field

from langchain_core.tools import BaseTool


class WebSearchInput(BaseModel):
    """Input for web search tool."""
    
    query: str = Field(description="The search query")
    max_results: int = Field(default=5, description="Maximum number of results to return")


class WebSearchTool(BaseTool):
    """
    Web search tool for GAAPF.
    
    Uses the Tavily API to perform web searches.
    """
    
    name = "web_search"
    description = "Search the web for information about Python AI frameworks"
    args_schema = WebSearchInput
    
    def _run(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Run the web search.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            Search results
        """
        # In a real implementation, this would use the Tavily API
        # For now, we'll return a placeholder
        
        try:
            # Check if <PERSON>ly is available
            from tavily import Tavily<PERSON>lient
            
            # Get API key
            api_key = os.environ.get("TAVILY_API_KEY")
            if not api_key:
                return {
                    "error": "Tavily API key not found. Please set the TAVILY_API_KEY environment variable.",
                    "results": []
                }
                
            # Create client
            client = TavilyClient(api_key=api_key)
            
            # Perform search
            response = client.search(
                query=query,
                max_results=max_results,
                search_depth="advanced"
            )
            
            # Format results
            results = []
            for result in response.get("results", []):
                results.append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0)
                })
                
            return {
                "query": query,
                "results": results
            }
            
        except ImportError:
            # Tavily not installed, return placeholder
            return {
                "error": "Tavily package not installed. Please install with 'pip install tavily-python'.",
                "results": []
            }
        except Exception as e:
            # Handle other errors
            return {
                "error": str(e),
                "results": []
            }
            
    async def _arun(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Run the web search asynchronously.
        
        Args:
            query: Search query
            max_results: Maximum number of results to return
            
        Returns:
            Search results
        """
        # For now, just call the synchronous version
        return self._run(query=query, max_results=max_results) 