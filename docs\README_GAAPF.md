# GAAPF - Guidance AI Agent for Python Framework

A sophisticated AI-powered learning system that provides personalized guidance for mastering Python AI frameworks through intelligent agent constellations.

## 🌟 Overview

GAAPF (Guidance AI Agent for Python Framework) is an advanced learning platform that uses multiple specialized AI agents working in constellation to provide personalized, adaptive learning experiences for Python AI frameworks. The system employs cutting-edge AI orchestration, real-time analytics, and intelligent memory systems to create an optimal learning environment.

## ✨ Key Features

### 🤖 **Intelligent Agent Constellation**
- **12 Specialized Agents**: Each with unique expertise and capabilities
- **Dynamic Agent Selection**: AI-powered handoff decisions based on context
- **LangGraph Integration**: Sophisticated workflow orchestration
- **Real-time Adaptation**: Constellation adapts based on learning progress

### 🧠 **Advanced Memory Systems**
- **Three-Tier Architecture**: Conversation, Knowledge, and User memory
- **Persistent Learning Context**: Maintains context across sessions
- **Knowledge Graph Integration**: Concept relationships and prerequisites
- **vinagent Integration**: Enhanced memory capabilities

### 📊 **Real-Time Analytics**
- **Comprehensive Metrics**: Engagement, effectiveness, and adaptive metrics
- **Learning Pattern Recognition**: AI-powered insight generation
- **Progress Visualization**: Rich dashboards and trend analysis
- **Personalized Recommendations**: Data-driven learning suggestions

### 🎯 **Adaptive Learning Engine**
- **Knowledge Graph**: Concept relationships and learning paths
- **Prerequisite Tracking**: Intelligent dependency resolution
- **Gap Analysis**: Identifies and fills knowledge gaps
- **Personalized Paths**: Customized learning journeys

### 🖥️ **Professional CLI Interface**
- **Rich Console**: Beautiful visualizations and interactive menus
- **Session Management**: Continue previous sessions seamlessly
- **Progress Tracking**: Visual progress indicators and analytics
- **Export Capabilities**: Multiple formats for learning data

### 🔧 **Robust Framework Support**
- **10 Frameworks**: Comprehensive coverage of AI frameworks
- **Enhanced Metadata**: Learning objectives and assessment criteria
- **Database System**: Efficient caching and retrieval
- **Modular Architecture**: Easy framework addition

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- API key for LLM provider (Together AI recommended, OpenAI supported)
- 4GB+ RAM for optimal performance

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd gaapf
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment:**
```bash
cp .env.example .env
# Edit .env with your API keys and preferences
```

4. **Initialize the system:**
```bash
python -m gaapf.interfaces.cli.app
```

### First Run

1. **Register a new user** with your learning preferences
2. **Select a framework** to start learning (LangChain recommended for beginners)
3. **Choose a module** based on your skill level
4. **Start your learning session** with personalized AI guidance

## 🏗️ Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    GAAPF Architecture                        │
├─────────────────────────────────────────────────────────────┤
│  CLI Interface                                              │
│  ├── Rich Console UI                                        │
│  ├── Interactive Menus                                      │
│  └── Progress Visualization                                 │
├─────────────────────────────────────────────────────────────┤
│  Learning Hub Core                                          │
│  ├── Session Management                                     │
│  ├── User Profile Management                                │
│  └── Message Processing                                     │
├─────────────────────────────────────────────────────────────┤
│  Constellation Manager                                      │
│  ├── Agent Orchestration                                    │
│  ├── LangGraph Integration                                  │
│  └── Dynamic Handoff Logic                                  │
├─────────────────────────────────────────────────────────────┤
│  Specialized Agents (12)                                    │
│  ├── Instructor Agent        ├── Code Assistant            │
│  ├── Documentation Expert    ├── Practice Facilitator      │
│  ├── Mentor Agent           ├── Assessment Agent           │
│  ├── Research Assistant     ├── Project Guide             │
│  ├── Troubleshooter         ├── Motivational Coach        │
│  ├── Knowledge Synthesizer  └── Progress Tracker          │
├─────────────────────────────────────────────────────────────┤
│  Memory Systems                                             │
│  ├── Conversation Memory (Session Context)                  │
│  ├── Knowledge Memory (Domain Knowledge)                    │
│  └── User Memory (Long-term Learning Data)                  │
├─────────────────────────────────────────────────────────────┤
│  Knowledge Graph                                            │
│  ├── Concept Relationships                                  │
│  ├── Learning Path Optimization                             │
│  └── Prerequisite Tracking                                  │
├─────────────────────────────────────────────────────────────┤
│  Analytics Engine                                           │
│  ├── Real-time Metrics Collection                           │
│  ├── Learning Effectiveness Analysis                        │
│  └── Adaptive Optimization                                  │
├─────────────────────────────────────────────────────────────┤
│  Framework Database                                         │
│  ├── SQLite with Caching                                    │
│  ├── Module Metadata                                        │
│  └── Learning Objectives                                    │
└─────────────────────────────────────────────────────────────┘
```

### Agent Specializations

| Agent | Primary Function | Confidence Triggers |
|-------|------------------|-------------------|
| **Instructor** | Concept explanation and theory | "explain", "what is", "how does" |
| **Code Assistant** | Code examples and implementation | "code", "example", "implement" |
| **Documentation Expert** | Official docs and references | "documentation", "reference", "API" |
| **Practice Facilitator** | Hands-on exercises | "practice", "exercise", "try" |
| **Mentor** | Learning guidance and strategy | "how to learn", "study plan", "roadmap" |
| **Assessment** | Knowledge evaluation | "test", "quiz", "assess", "evaluate" |
| **Research Assistant** | Latest information and trends | "latest", "new", "research", "trends" |
| **Project Guide** | End-to-end project guidance | "project", "build", "create application" |
| **Troubleshooter** | Error resolution and debugging | "error", "bug", "not working", "issue" |
| **Motivational Coach** | Encouragement and motivation | "frustrated", "difficult", "give up" |
| **Knowledge Synthesizer** | Connecting concepts | "relationship", "compare", "synthesize" |
| **Progress Tracker** | Learning progress analysis | "progress", "improvement", "next steps" |

## 🎯 Supported Frameworks

### **Tier 1 - Full Support**
- **LangChain** (v0.3.25+): Building applications with language models
- **LangGraph** (v0.4.7+): Stateful, multi-agent applications
- **CrewAI**: Collaborative AI agent frameworks
- **AutoGen**: Multi-agent conversation frameworks

### **Tier 2 - Growing Support**
- **LlamaIndex**: Data framework for LLM applications
- **Haystack**: End-to-end NLP framework
- **Semantic Kernel**: Microsoft's AI orchestration
- **Guidance**: Structured generation framework

### **Tier 3 - Basic Support**
- **Transformers**: Hugging Face transformers library
- **OpenAI API**: Direct OpenAI API integration

## 📚 Learning Modules

Each framework includes comprehensive modules:

### Module Types
- **📖 Concept**: Theoretical understanding
- **🛠️ Tutorial**: Step-by-step guidance
- **🚀 Project**: Hands-on implementation
- **🎯 Challenge**: Problem-solving exercises
- **📋 Reference**: Quick reference materials
- **✅ Assessment**: Knowledge evaluation
- **🎪 Workshop**: Interactive sessions
- **📊 Case Study**: Real-world examples
- **🧪 Lab**: Experimental learning

### Difficulty Levels
- **🟢 Beginner**: No prior experience required
- **🟡 Intermediate**: Basic Python knowledge needed
- **🟠 Advanced**: Solid programming background
- **🔴 Expert**: Professional-level expertise

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following configuration:

```bash
# LLM Provider Configuration
TOGETHER_API_KEY=your_together_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_LLM_PROVIDER=together

# System Configuration
GAAPF_DATA_PATH=./data
GAAPF_LOG_LEVEL=INFO
GAAPF_MAX_SESSIONS=10

# Analytics Configuration
ANALYTICS_ENABLED=true
METRICS_COLLECTION_INTERVAL=60
DASHBOARD_REFRESH_RATE=30

# Memory Configuration
MEMORY_TTL_HOURS=24
CACHE_SIZE_LIMIT=1000
AUTO_SAVE_INTERVAL=300

# Framework Configuration
DEFAULT_FRAMEWORK=langchain
AUTO_DETECT_SKILL_LEVEL=true
PERSONALIZATION_ENABLED=true
```

### Advanced Configuration

For advanced users, additional configuration files are available:

- `config/framework_configs.py`: Framework-specific settings
- `config/user_profiles.py`: User profile templates
- `config/agent_configs.py`: Agent behavior customization

## 📖 Usage Examples

### Basic Learning Session

```python
from gaapf.core.learning_hub import LearningHubCore
from gaapf.config.user_profiles import UserProfile, SkillLevel

# Initialize learning hub
hub = LearningHubCore()
await hub.initialize()

# Create user profile
profile = UserProfile(
    user_id="learner_123",
    python_skill_level=SkillLevel.INTERMEDIATE,
    preferred_learning_style="hands_on"
)

# Start learning session
session_id = await hub.create_session(
    user_id="learner_123",
    framework="langchain",
    module_id="lc_basics"
)

# Process learning interaction
result = await hub.process_message(
    user_id="learner_123",
    message="How do I create a simple chain in LangChain?",
    session_id=session_id
)

print(result["response"])
```

### Advanced Analytics Usage

```python
from gaapf.core.analytics_system import get_analytics_engine
from gaapf.core.analytics_system import EngagementMetrics

# Get analytics engine
analytics = get_analytics_engine()

# Record engagement metrics
engagement = EngagementMetrics(
    session_duration=3600.0,
    interaction_count=25,
    attention_score=0.85,
    completion_rate=0.9
)

analytics.record_engagement_metrics(
    user_id="learner_123",
    session_id="session_456",
    framework="langchain",
    module_id="lc_basics",
    metrics=engagement
)

# Get real-time dashboard
dashboard = analytics.get_real_time_dashboard("learner_123")
print(dashboard)
```

### Knowledge Graph Integration

```python
from gaapf.core.knowledge_graph import KnowledgeGraphManager, ConceptNode

# Initialize knowledge graph
kg = KnowledgeGraphManager()

# Add a concept
concept = ConceptNode(
    concept_id="langchain_memory",
    name="LangChain Memory",
    framework="langchain",
    difficulty_level="intermediate",
    prerequisites=["langchain_basics"]
)

kg.add_concept(concept)

# Find learning path
path = kg.find_learning_path(
    start_concepts=["langchain_basics"],
    target_concept="langchain_agents"
)

print(f"Learning path: {path}")
```

## 🧪 Testing

### Running Tests

```bash
# Install test dependencies
pip install -r requirements-test.txt

# Run all tests
python tests/run_tests.py

# Run specific test categories
python tests/run_tests.py --unit          # Unit tests only
python tests/run_tests.py --integration   # Integration tests only
python tests/run_tests.py --coverage      # With coverage report

# Run specific test file
python tests/run_tests.py --specific tests/test_agents.py

# Generate test report
python tests/run_tests.py --report test_report.md
```

### Test Coverage

The test suite includes:
- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow testing
- **Performance Tests**: Load and performance validation
- **Mock Tests**: LLM provider simulation

Current test coverage: **85%+** across all modules
