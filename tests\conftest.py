"""
Pytest configuration and shared fixtures for GAAPF tests.

This module provides:
- Common test fixtures
- Test configuration
- Mock objects and utilities
- Test data setup and teardown
"""

import pytest
import asyncio
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from typing import Dict, Any, List

# Import GAAPF components for fixture creation
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from gaapf.config.framework_configs import FrameworkModule, SupportedFrameworks, ModuleDifficulty, ModuleType


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_data_path():
    """Provide a temporary directory for test data."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def mock_llm():
    """Mock LLM for testing."""
    llm = Mock()
    llm.ainvoke = AsyncMock(return_value=Mock(content="Mock LLM response"))
    llm.invoke = Mock(return_value=Mock(content="Mock LLM response"))
    return llm


@pytest.fixture
def sample_user_profiles():
    """Sample user profiles for testing."""
    return {
        "beginner": UserProfile(
            user_id="beginner_user",
            programming_experience_years=0,
            python_skill_level=SkillLevel.BEGINNER,
            learning_pace=LearningPace.SLOW,
            preferred_learning_style=LearningStyle.VISUAL
        ),
        "intermediate": UserProfile(
            user_id="intermediate_user",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            learning_pace=LearningPace.MODERATE,
            preferred_learning_style=LearningStyle.HANDS_ON
        ),
        "advanced": UserProfile(
            user_id="advanced_user",
            programming_experience_years=8,
            python_skill_level=SkillLevel.ADVANCED,
            learning_pace=LearningPace.FAST,
            preferred_learning_style=LearningStyle.READING
        )
    }


@pytest.fixture
def sample_framework_modules():
    """Sample framework modules for testing."""
    return [
        FrameworkModule(
            module_id="test_basics",
            title="Test Framework Basics",
            description="Introduction to test framework",
            framework=SupportedFrameworks.LANGCHAIN,
            difficulty=ModuleDifficulty.BEGINNER,
            module_type=ModuleType.CONCEPT,
            topics=["basics", "introduction"],
            estimated_minutes=30
        ),
        FrameworkModule(
            module_id="test_intermediate",
            title="Intermediate Test Concepts",
            description="Intermediate concepts in test framework",
            framework=SupportedFrameworks.LANGCHAIN,
            difficulty=ModuleDifficulty.INTERMEDIATE,
            module_type=ModuleType.TUTORIAL,
            prerequisites=["test_basics"],
            topics=["chains", "memory"],
            estimated_minutes=45
        ),
        FrameworkModule(
            module_id="test_advanced",
            title="Advanced Test Implementation",
            description="Advanced implementation techniques",
            framework=SupportedFrameworks.LANGCHAIN,
            difficulty=ModuleDifficulty.ADVANCED,
            module_type=ModuleType.PROJECT,
            prerequisites=["test_basics", "test_intermediate"],
            topics=["agents", "complex_chains"],
            estimated_minutes=90
        )
    ]


@pytest.fixture
def sample_learning_metrics():
    """Sample learning metrics for testing."""
    return {
        "engagement": {
            "session_duration": 3600.0,
            "interaction_count": 25,
            "question_count": 8,
            "response_time_avg": 2.5,
            "attention_score": 0.85,
            "completion_rate": 0.9
        },
        "effectiveness": {
            "comprehension_score": 0.82,
            "retention_score": 0.75,
            "application_score": 0.88,
            "progress_velocity": 1.2,
            "concept_mastery_rate": 0.78,
            "error_recovery_rate": 0.92
        },
        "adaptive": {
            "difficulty_appropriateness": 0.85,
            "learning_style_alignment": 0.90,
            "constellation_effectiveness": 0.87,
            "tool_usage_efficiency": 0.83,
            "handoff_success_rate": 0.95,
            "personalization_score": 0.88
        }
    }


@pytest.fixture
def sample_conversation_history():
    """Sample conversation history for testing."""
    return [
        {
            "role": "user",
            "content": "Hi, I want to learn LangChain",
            "timestamp": "2024-01-01T10:00:00Z"
        },
        {
            "role": "assistant",
            "content": "Great! I'd be happy to help you learn LangChain. Let's start with the basics.",
            "timestamp": "2024-01-01T10:00:05Z",
            "agent_type": "instructor"
        },
        {
            "role": "user",
            "content": "What are the main components?",
            "timestamp": "2024-01-01T10:01:00Z"
        },
        {
            "role": "assistant",
            "content": "The main components of LangChain are LLMs, Chains, Prompts, and Memory.",
            "timestamp": "2024-01-01T10:01:10Z",
            "agent_type": "instructor"
        },
        {
            "role": "user",
            "content": "Can you show me some code?",
            "timestamp": "2024-01-01T10:02:00Z"
        },
        {
            "role": "assistant",
            "content": "Certainly! Here's a simple example of creating a chain in LangChain...",
            "timestamp": "2024-01-01T10:02:15Z",
            "agent_type": "code_assistant"
        }
    ]


@pytest.fixture
def sample_knowledge_graph_data():
    """Sample knowledge graph data for testing."""
    return {
        "concepts": [
            {
                "concept_id": "langchain_basics",
                "name": "LangChain Basics",
                "framework": "langchain",
                "difficulty_level": "beginner",
                "prerequisites": [],
                "learning_objectives": ["Understand LangChain components"]
            },
            {
                "concept_id": "langchain_chains",
                "name": "LangChain Chains",
                "framework": "langchain",
                "difficulty_level": "intermediate",
                "prerequisites": ["langchain_basics"],
                "learning_objectives": ["Create and use chains"]
            },
            {
                "concept_id": "langchain_agents",
                "name": "LangChain Agents",
                "framework": "langchain",
                "difficulty_level": "advanced",
                "prerequisites": ["langchain_basics", "langchain_chains"],
                "learning_objectives": ["Build autonomous agents"]
            }
        ],
        "relationships": [
            {
                "from_concept": "langchain_basics",
                "to_concept": "langchain_chains",
                "relationship_type": "prerequisite"
            },
            {
                "from_concept": "langchain_chains",
                "to_concept": "langchain_agents",
                "relationship_type": "prerequisite"
            }
        ]
    }


@pytest.fixture
def mock_analytics_data():
    """Mock analytics data for testing."""
    return {
        "user_progress": {
            "total_sessions": 15,
            "total_time_hours": 12.5,
            "frameworks_studied": ["langchain", "langgraph"],
            "concepts_mastered": 25,
            "average_score": 78.5
        },
        "recent_sessions": [
            {
                "session_id": "session_1",
                "framework": "langchain",
                "date": "2024-01-15",
                "duration": 45,
                "score": 85.0,
                "concepts_covered": ["chains", "prompts"]
            },
            {
                "session_id": "session_2",
                "framework": "langgraph",
                "date": "2024-01-14",
                "duration": 60,
                "score": 72.0,
                "concepts_covered": ["graphs", "nodes"]
            }
        ],
        "learning_patterns": [
            {
                "pattern_type": "difficulty_progression",
                "pattern_data": {"trend": "increasing"},
                "confidence": 0.85
            },
            {
                "pattern_type": "learning_style",
                "pattern_data": {"preferred": "hands_on"},
                "confidence": 0.92
            }
        ]
    }


@pytest.fixture
def test_environment_setup():
    """Set up test environment variables."""
    original_env = os.environ.copy()
    
    # Set test environment variables
    test_env = {
        "GAAPF_ENV": "test",
        "GAAPF_DATA_PATH": "test_data",
        "TOGETHER_API_KEY": "test_together_key",
        "OPENAI_API_KEY": "test_openai_key"
    }
    
    os.environ.update(test_env)
    
    yield test_env
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


class MockMemorySystem:
    """Mock memory system for testing."""
    
    def __init__(self):
        self.conversation_memory = {}
        self.knowledge_memory = {}
        self.user_memory = {}
    
    def add_conversation_entry(self, session_id: str, entry: Dict[str, Any]):
        """Add conversation entry."""
        if session_id not in self.conversation_memory:
            self.conversation_memory[session_id] = []
        self.conversation_memory[session_id].append(entry)
    
    def get_conversation_history(self, session_id: str) -> List[Dict[str, Any]]:
        """Get conversation history."""
        return self.conversation_memory.get(session_id, [])
    
    def add_knowledge_entry(self, concept_id: str, knowledge: Dict[str, Any]):
        """Add knowledge entry."""
        self.knowledge_memory[concept_id] = knowledge
    
    def get_knowledge(self, concept_id: str) -> Dict[str, Any]:
        """Get knowledge entry."""
        return self.knowledge_memory.get(concept_id, {})
    
    def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]):
        """Update user profile."""
        if user_id not in self.user_memory:
            self.user_memory[user_id] = {}
        self.user_memory[user_id].update(profile_data)
    
    def get_user_profile(self, user_id: str) -> Dict[str, Any]:
        """Get user profile."""
        return self.user_memory.get(user_id, {})


@pytest.fixture
def mock_memory_system():
    """Mock memory system for testing."""
    return MockMemorySystem()


class MockAnalyticsEngine:
    """Mock analytics engine for testing."""
    
    def __init__(self):
        self.metrics = []
        self.dashboards = {}
    
    def record_metric(self, metric_data: Dict[str, Any]):
        """Record a metric."""
        self.metrics.append(metric_data)
    
    def get_dashboard(self, user_id: str) -> Dict[str, Any]:
        """Get dashboard data."""
        return self.dashboards.get(user_id, {
            "total_sessions": 0,
            "average_score": 0.0,
            "recent_activity": []
        })
    
    def analyze_effectiveness(self, user_id: str) -> Dict[str, Any]:
        """Analyze learning effectiveness."""
        return {
            "overall_score": 0.75,
            "recommendations": ["Continue current approach"],
            "insights": ["Good progress detected"]
        }


@pytest.fixture
def mock_analytics_engine():
    """Mock analytics engine for testing."""
    return MockAnalyticsEngine()


# Test utilities
def create_test_session_data(user_id: str, framework: str, module_id: str) -> Dict[str, Any]:
    """Create test session data."""
    return {
        "session_id": f"test_session_{user_id}",
        "user_id": user_id,
        "framework": framework,
        "module_id": module_id,
        "start_time": "2024-01-01T10:00:00Z",
        "status": "active"
    }


def create_test_agent_response(agent_type: str, content: str) -> Dict[str, Any]:
    """Create test agent response."""
    return {
        "agent_type": agent_type,
        "response": content,
        "confidence": 0.85,
        "handoff_suggestion": None,
        "metadata": {
            "processing_time": 1.5,
            "tokens_used": 150
        }
    }


# Pytest configuration
def pytest_configure(config):
    """Configure pytest."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection."""
    for item in items:
        # Add markers based on test file names
        if "integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_" in item.nodeid and "integration" not in item.nodeid:
            item.add_marker(pytest.mark.unit)
        
        # Mark async tests
        if asyncio.iscoroutinefunction(item.function):
            item.add_marker(pytest.mark.asyncio)
