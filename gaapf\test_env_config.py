#!/usr/bin/env python3
"""
Test script for environment configuration.

This script tests the .env file loading and configuration validation.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_env_config():
    """Test environment configuration loading."""
    print("🧪 Testing Environment Configuration")
    
    try:
        from gaapf.config.env_config import get_config, reload_config
        
        # Test basic config loading
        config = get_config()
        print("✅ Environment configuration loaded successfully")
        
        # Test configuration validation
        validation = config.validate_configuration()
        print(f"✅ Configuration validation completed")
        
        # Print configuration status
        config.print_configuration_status()
        
        # Test available providers
        providers = config.get_available_llm_providers()
        print(f"\n🤖 Available LLM Providers:")
        for provider, available in providers.items():
            status = "✅" if available else "❌"
            print(f"  {status} {provider}")
        
        # Test LLM creation if any provider is available
        if any(providers.values()):
            try:
                llm = config.create_llm()
                print(f"✅ LLM creation successful: {type(llm).__name__}")
            except Exception as e:
                print(f"⚠️  LLM creation failed: {e}")
        else:
            print("⚠️  No LLM providers available - skipping LLM creation test")
        
        return validation["valid"]
        
    except Exception as e:
        print(f"❌ Environment configuration test failed: {e}")
        return False

def test_env_file_handling():
    """Test .env file handling."""
    print("\n📁 Testing .env File Handling")
    
    # Check if .env.example exists
    env_example = Path(".env.example")
    if env_example.exists():
        print("✅ .env.example file exists")
    else:
        print("❌ .env.example file missing")
        return False
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("✅ .env file exists")
        
        # Test reloading configuration
        try:
            from gaapf.config.env_config import reload_config
            config = reload_config()
            print("✅ Configuration reload successful")
        except Exception as e:
            print(f"❌ Configuration reload failed: {e}")
            return False
    else:
        print("⚠️  .env file not found (this is expected if not configured)")
        print("   Copy .env.example to .env and configure your API keys")
    
    return True

def test_import_dependencies():
    """Test that all required dependencies are available."""
    print("\n📦 Testing Dependencies")
    
    dependencies = [
        ("python-dotenv", "dotenv"),
        ("langchain-core", "langchain_core"),
        ("questionary", "questionary"),
        ("rich", "rich"),
        ("pydantic", "pydantic")
    ]
    
    all_available = True
    for dep_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✅ {dep_name}")
        except ImportError:
            print(f"❌ {dep_name} (missing)")
            all_available = False
    
    # Test optional dependencies
    optional_deps = [
        ("langchain-openai", "langchain_openai"),
        ("langchain-google-genai", "langchain_google_genai"),
        ("langchain-anthropic", "langchain_anthropic"),
        ("langchain-together", "langchain_together"),
        ("tavily-python", "tavily")
    ]
    
    print("\n📦 Optional Dependencies:")
    for dep_name, import_name in optional_deps:
        try:
            __import__(import_name)
            print(f"✅ {dep_name}")
        except ImportError:
            print(f"⚠️  {dep_name} (optional)")
    
    return all_available

def main():
    """Main test function."""
    print("🧪 GAAPF Environment Configuration Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Import Dependencies", test_import_dependencies),
        (".env File Handling", test_env_file_handling),
        ("Environment Configuration", test_env_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All environment configuration tests passed!")
        print("\n📝 Next Steps:")
        print("1. Configure your API keys in .env file")
        print("2. Run: python -m gaapf")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
