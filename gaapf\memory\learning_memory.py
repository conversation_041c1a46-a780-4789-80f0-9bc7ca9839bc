"""
Learning memory implementation for GAAPF.

This module implements a specialized memory system for tracking
learning progress and patterns.
"""

from typing import Dict, List, Optional, Any
import json
import os
from datetime import datetime
from pathlib import Path


class LearningMemory:
    """
    Learning memory for GAAPF.
    
    Specialized memory system for tracking learning progress and patterns.
    """
    
    def __init__(
        self,
        user_id: str,
        framework: str = None,
        module_id: str = None,
        session_id: str = None,
        storage_path: Path = None
    ):
        """
        Initialize the learning memory.
        
        Args:
            user_id: User identifier
            framework: Target framework
            module_id: Current learning module
            session_id: Session identifier
            storage_path: Path to store memory data
        """
        self.user_id = user_id
        self.framework = framework
        self.module_id = module_id
        self.session_id = session_id
        self.storage_path = storage_path
        
        # Initialize conversation history
        self.messages = []
        
        self.learning_metrics = {
            "concepts_introduced": [],
            "concepts_understood": [],
            "concepts_confused": [],
            "questions_asked": [],
            "examples_provided": [],
            "exercises_completed": [],
            "last_activity_timestamp": datetime.now().isoformat()
        }
        
        # Load existing memory if available
        if storage_path and session_id:
            self._load_memory()
            
    def add_user_message(self, message: str) -> None:
        """
        Add a user message to memory.
        
        Args:
            message: User message
        """
        self.messages.append({"role": "user", "content": message, "timestamp": datetime.now().isoformat()})
        
        # Update learning metrics
        self.learning_metrics["last_activity_timestamp"] = datetime.now().isoformat()
        
        # Track questions
        if "?" in message:
            self.learning_metrics["questions_asked"].append({
                "timestamp": datetime.now().isoformat(),
                "question": message
            })
            
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def add_ai_message(self, message: str) -> None:
        """
        Add an AI message to memory.
        
        Args:
            message: AI message
        """
        self.messages.append({"role": "ai", "content": message, "timestamp": datetime.now().isoformat()})
        
        # Update learning metrics
        self.learning_metrics["last_activity_timestamp"] = datetime.now().isoformat()
        
        # Track examples
        if "```" in message:
            self.learning_metrics["examples_provided"].append({
                "timestamp": datetime.now().isoformat(),
                "example": message
            })
            
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def mark_concept_introduced(self, concept: str) -> None:
        """
        Mark a concept as introduced.
        
        Args:
            concept: Concept name
        """
        self.learning_metrics["concepts_introduced"].append({
            "timestamp": datetime.now().isoformat(),
            "concept": concept
        })
        
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def mark_concept_understood(self, concept: str) -> None:
        """
        Mark a concept as understood.
        
        Args:
            concept: Concept name
        """
        self.learning_metrics["concepts_understood"].append({
            "timestamp": datetime.now().isoformat(),
            "concept": concept
        })
        
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def mark_concept_confused(self, concept: str) -> None:
        """
        Mark a concept as confused.
        
        Args:
            concept: Concept name
        """
        self.learning_metrics["concepts_confused"].append({
            "timestamp": datetime.now().isoformat(),
            "concept": concept
        })
        
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def mark_exercise_completed(self, exercise_id: str, success: bool = True) -> None:
        """
        Mark an exercise as completed.
        
        Args:
            exercise_id: Exercise identifier
            success: Whether the exercise was completed successfully
        """
        self.learning_metrics["exercises_completed"].append({
            "timestamp": datetime.now().isoformat(),
            "exercise_id": exercise_id,
            "success": success
        })
        
        # Save memory if storage path is available
        if self.storage_path and self.session_id:
            self._save_memory()
            
    def get_learning_metrics(self) -> Dict[str, Any]:
        """
        Get learning metrics.
        
        Returns:
            Learning metrics
        """
        return self.learning_metrics
        
    def get_understood_concepts(self) -> List[str]:
        """
        Get concepts marked as understood.
        
        Returns:
            List of understood concepts
        """
        return [item["concept"] for item in self.learning_metrics["concepts_understood"]]
        
    def get_confused_concepts(self) -> List[str]:
        """
        Get concepts marked as confused.
        
        Returns:
            List of confused concepts
        """
        return [item["concept"] for item in self.learning_metrics["concepts_confused"]]
        
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """
        Get the conversation history.
        
        Returns:
            List of conversation messages
        """
        return self.messages
        
    def _save_memory(self) -> None:
        """Save memory to storage."""
        if not self.storage_path or not self.session_id:
            return
            
        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_path, exist_ok=True)
        
        # Create file path
        file_path = self.storage_path / f"{self.session_id}.json"
        
        # Save memory data
        memory_data = {
            "user_id": self.user_id,
            "framework": self.framework,
            "module_id": self.module_id,
            "session_id": self.session_id,
            "messages": self.messages,
            "learning_metrics": self.learning_metrics
        }
        
        with open(file_path, "w") as f:
            json.dump(memory_data, f, indent=2)
            
    def _load_memory(self) -> None:
        """Load memory from storage."""
        if not self.storage_path or not self.session_id:
            return
            
        # Create file path
        file_path = self.storage_path / f"{self.session_id}.json"
        
        # Check if file exists
        if not os.path.exists(file_path):
            return
            
        # Load memory data
        try:
            with open(file_path, "r") as f:
                memory_data = json.load(f)
                
            # Update memory attributes
            self.messages = memory_data.get("messages", [])
            self.learning_metrics = memory_data.get("learning_metrics", self.learning_metrics)
        except Exception as e:
            print(f"Error loading memory: {e}") 