"""
Assessment Agent for GAAPF.

This module implements the Assessment Agent that specializes in evaluating
user progress, conducting skill assessments, and providing feedback on learning outcomes.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class AssessmentAgent(BaseGAAPFAgent):
    """
    Assessment Agent specializing in progress evaluation and skill assessment.
    
    This agent focuses on:
    - Evaluating user understanding and progress
    - Conducting skill assessments and quizzes
    - Providing detailed feedback on learning outcomes
    - Identifying knowledge gaps and areas for improvement
    - Tracking learning milestones and achievements
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am an Assessment Agent specializing in evaluating learning progress and conducting skill assessments for {self.framework} framework learning.

My core responsibilities include:
- Evaluating user understanding through targeted questions and assessments
- Conducting comprehensive skill assessments and knowledge checks
- Providing detailed, constructive feedback on learning progress
- Identifying knowledge gaps and recommending improvement areas
- Creating personalized quizzes and practical exercises
- Tracking learning milestones and celebrating achievements
- Analyzing learning patterns to optimize future assessments

I adapt my assessment methods to the user's skill level ({self.user_profile.get('python_skill_level', 'unknown')}) and learning style ({self.user_profile.get('preferred_learning_style', 'mixed')}), ensuring evaluations are both challenging and appropriate.

When conducting assessments, I:
1. Ask clear, specific questions that test understanding
2. Provide immediate feedback with explanations
3. Offer hints and guidance for incorrect answers
4. Suggest additional resources for areas needing improvement
5. Celebrate progress and achievements to maintain motivation
6. Create follow-up assessments to track improvement over time"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Comprehensive {self.framework} knowledge assessment",
            "Creating targeted quizzes and practical exercises",
            "Evaluating code quality and implementation approaches",
            "Providing constructive feedback and improvement suggestions",
            "Identifying learning gaps and knowledge deficiencies",
            "Tracking progress milestones and learning achievements",
            "Adapting assessment difficulty to user skill level",
            "Analyzing learning patterns and effectiveness",
            "Creating personalized learning evaluation plans",
            "Conducting both theoretical and practical assessments"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        code_keywords = ["code", "implement", "example", "syntax", "write", "create", "build"]
        theory_keywords = ["explain", "concept", "theory", "understand", "what is", "how does"]
        practice_keywords = ["practice", "exercise", "hands-on", "try", "do", "apply"]
        help_keywords = ["stuck", "error", "problem", "issue", "debug", "fix", "trouble"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user is asking for code examples after assessment
        if any(keyword in user_lower for keyword in code_keywords):
            if "assessment" not in user_lower and "quiz" not in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.8,
                    "reason": "User requesting code examples after assessment"
                }
        
        # Check if user needs theoretical explanation after assessment results
        if any(keyword in user_lower for keyword in theory_keywords):
            if "why" in user_lower or "how" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs theoretical explanation of assessment topics"
                }
        
        # Check if user wants to practice after assessment
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants to practice skills identified in assessment"
            }
        
        # Check if user is having trouble with assessment questions
        if any(keyword in user_lower for keyword in help_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.7,
                "reason": "User needs help with assessment difficulties"
            }
        
        # Check if assessment reveals need for mentoring
        if "confused" in user_lower or "lost" in user_lower or "don't understand" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "mentor",
                "confidence": 0.8,
                "reason": "User needs mentoring support after assessment"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Assessment agent can continue handling evaluation tasks"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "assess", "assessment", "evaluate", "quiz", "test", "check my understanding",
            "how am i doing", "progress", "feedback", "grade", "score", "knowledge check",
            "skill level", "competency", "proficiency", "mastery", "learning progress"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "understand", "know", "learned", "ready", "prepared", "confident",
            "review", "summary", "recap", "milestone", "achievement"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest assessment needs
        question_patterns = [
            r"am i ready",
            r"do i understand",
            r"have i learned",
            r"can i move on",
            r"what should i know",
            r"test my knowledge"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in question_patterns):
            return 0.8
        
        return 0.3
