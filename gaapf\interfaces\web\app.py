"""
Web interface for GAAPF using FastAPI.

This module provides a web interface for interacting with the GAAPF system.
"""

import asyncio
import logging
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gaapf.core.constellation import ConstellationManager, ConstellationType
from gaapf.core.temporal_state import TemporalStateManager
from gaapf.core.learning_hub import LearningHubCore
from gaapf.agents import (
    BaseAgent,
    CodeAssistantAgent,
    DocumentationExpertAgent,
    InstructorAgent,
    MentorAgent,
    PracticeFacilitatorAgent,
)
from gaapf.memory import LearningMemory
from gaapf.config.framework_configs import FrameworkConfig
from gaapf.config.user_profiles import UserProfile
from gaapf.interfaces.web.static_handler import setup_static_files

logger = logging.getLogger(__name__)

class SessionRequest(BaseModel):
    """Request model for creating a new session."""
    user_id: str
    framework: str
    skill_level: str
    learning_goals: List[str]
    session_type: str = "interactive"

class MessageRequest(BaseModel):
    """Request model for sending a message."""
    session_id: str
    content: str
    agent_type: Optional[str] = None

class GAAPFWeb:
    """Web interface for GAAPF using FastAPI."""

    def __init__(self, data_path: Path):
        """Initialize the web interface.
        
        Args:
            data_path: Path to the data directory.
        """
        self.app = FastAPI(
            title="GAAPF - Guidance AI Agent for Python Framework",
            description="A system of specialized AI agents for learning Python frameworks",
            version="0.1.0",
        )
        self.data_path = data_path
        self.data_path.mkdir(exist_ok=True, parents=True)
        
        # Setup CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # In production, replace with specific origins
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup static file serving
        setup_static_files(self.app)
        
        # Initialize managers
        self.constellation_manager = ConstellationManager()
        self.temporal_state_manager = TemporalStateManager()
        self.learning_hub = LearningHubCore(data_path=self.data_path)
        
        # Active sessions
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.active_connections: Dict[str, List[WebSocket]] = {}
        
        # Register routes
        self._register_routes()
    
    def _register_routes(self):
        """Register API routes."""
        
        @self.app.get("/api")
        async def root():
            """Root endpoint."""
            return {"message": "Welcome to GAAPF API", "status": "online"}
        
        @self.app.get("/api/frameworks")
        async def get_frameworks():
            """Get available frameworks."""
            return {"frameworks": FrameworkConfig.get_available_frameworks()}
        
        @self.app.post("/api/sessions")
        async def create_session(request: SessionRequest):
            """Create a new learning session."""
            try:
                # Create user profile
                user_profile = UserProfile(
                    user_id=request.user_id,
                    skill_level=request.skill_level,
                    learning_goals=request.learning_goals,
                    preferred_framework=request.framework
                )
                
                # Generate session ID
                session_id = f"{request.user_id}_{request.framework}_{uuid.uuid4().hex[:8]}"
                
                # For simplicity, we'll use a mock implementation for now
                # In a real implementation, we would create proper agents
                
                # Mock constellation of agents
                agents = {
                    "instructor": InstructorAgent(agent_type="instructor"),
                    "mentor": MentorAgent(agent_type="mentor"),
                    "codeAssistant": CodeAssistantAgent(agent_type="codeAssistant"),
                    "documentationExpert": DocumentationExpertAgent(agent_type="documentationExpert"),
                    "practiceFacilitator": PracticeFacilitatorAgent(agent_type="practiceFacilitator")
                }
                
                # Mock constellation
                constellation = {
                    "agents": list(agents.values()),
                    "get_agent": lambda agent_type: next((a for a in agents.values() if a.agent_type == agent_type), None),
                    "process_message": lambda message, state: f"Response to: {message}"
                }
                
                # Create temporal state
                temporal_state = {}
                
                # Initialize memory
                memory = LearningMemory(
                    user_id=request.user_id,
                    framework=request.framework,
                    session_id=session_id,
                    storage_path=self.data_path / "memory"
                )
                
                # Store session
                self.active_sessions[session_id] = {
                    "user_profile": user_profile,
                    "constellation": constellation,
                    "temporal_state": temporal_state,
                    "memory": memory,
                }
                
                return {
                    "session_id": session_id,
                    "message": f"Session created for {request.framework} learning",
                    "agents": [agent.agent_type for agent in agents.values()]
                }
            
            except Exception as e:
                logger.error(f"Error creating session: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to create session: {str(e)}")
        
        @self.app.post("/api/messages")
        async def send_message(request: MessageRequest):
            """Send a message to the system."""
            if request.session_id not in self.active_sessions:
                raise HTTPException(status_code=404, detail="Session not found")
            
            try:
                session = self.active_sessions[request.session_id]
                constellation = session["constellation"]
                temporal_state = session["temporal_state"]
                memory = session["memory"]
                
                # Add message to memory
                memory.add_user_message(request.content)
                
                # Process message through the system
                if request.agent_type:
                    # Direct message to specific agent
                    agent = constellation.get_agent(request.agent_type)
                    if not agent:
                        raise HTTPException(status_code=404, detail=f"Agent {request.agent_type} not found")
                    
                    # In a real implementation, this would call the agent's process_message method
                    response = f"Response from {agent.agent_type}: I received your message: {request.content}"
                else:
                    # Let the constellation handle routing
                    response = constellation.process_message(request.content, temporal_state)
                
                # Add response to memory
                memory.add_ai_message(response)
                
                # Broadcast to all connected websockets for this session
                if request.session_id in self.active_connections:
                    for connection in self.active_connections[request.session_id]:
                        await connection.send_json({"type": "message", "content": response})
                
                return {"response": response}
            
            except Exception as e:
                logger.error(f"Error processing message: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Failed to process message: {str(e)}")
        
        @self.app.websocket("/api/ws/{session_id}")
        async def websocket_endpoint(websocket: WebSocket, session_id: str):
            """WebSocket endpoint for real-time communication."""
            await websocket.accept()
            
            if session_id not in self.active_sessions:
                await websocket.close(code=1000, reason="Session not found")
                return
            
            # Register connection
            if session_id not in self.active_connections:
                self.active_connections[session_id] = []
            self.active_connections[session_id].append(websocket)
            
            try:
                while True:
                    data = await websocket.receive_json()
                    
                    if "content" in data:
                        # Process message
                        session = self.active_sessions[session_id]
                        constellation = session["constellation"]
                        temporal_state = session["temporal_state"]
                        memory = session["memory"]
                        
                        # Add message to memory
                        memory.add_user_message(data["content"])
                        
                        agent_type = data.get("agent_type")
                        if agent_type:
                            agent = constellation.get_agent(agent_type)
                            if not agent:
                                await websocket.send_json({"error": f"Agent {agent_type} not found"})
                                continue
                            
                            # In a real implementation, this would call the agent's process_message method
                            response = f"Response from {agent.agent_type}: I received your message: {data['content']}"
                        else:
                            response = constellation.process_message(data["content"], temporal_state)
                        
                        # Add response to memory
                        memory.add_ai_message(response)
                        
                        # Send response back to this client
                        await websocket.send_json({"type": "message", "content": response})
                        
                        # Broadcast to other connected clients for this session
                        for conn in self.active_connections[session_id]:
                            if conn != websocket:
                                await conn.send_json({"type": "message", "content": response})
            
            except WebSocketDisconnect:
                # Remove connection
                if session_id in self.active_connections:
                    self.active_connections[session_id].remove(websocket)
                    if not self.active_connections[session_id]:
                        del self.active_connections[session_id]
            
            except Exception as e:
                logger.error(f"WebSocket error: {str(e)}")
                await websocket.close(code=1000, reason=f"Error: {str(e)}")
        
        @self.app.get("/api/sessions/{session_id}")
        async def get_session(session_id: str):
            """Get session details."""
            if session_id not in self.active_sessions:
                raise HTTPException(status_code=404, detail="Session not found")
            
            session = self.active_sessions[session_id]
            return {
                "session_id": session_id,
                "user": session["user_profile"].user_id,
                "framework": session["user_profile"].preferred_framework,
                "agents": [agent.agent_type for agent in session["constellation"]["agents"]]
            }
        
        @self.app.delete("/api/sessions/{session_id}")
        async def end_session(session_id: str):
            """End a session."""
            if session_id not in self.active_sessions:
                raise HTTPException(status_code=404, detail="Session not found")
            
            # Close all websocket connections
            if session_id in self.active_connections:
                for connection in self.active_connections[session_id]:
                    await connection.close(code=1000, reason="Session ended")
                del self.active_connections[session_id]
            
            # Remove session
            del self.active_sessions[session_id]
            
            return {"message": "Session ended successfully"}
    
    async def run(self):
        """Run the web interface."""
        import uvicorn
        
        config = uvicorn.Config(
            app=self.app,
            host="0.0.0.0",
            port=8000,
            log_level="info"
        )
        server = uvicorn.Server(config)
        await server.serve() 