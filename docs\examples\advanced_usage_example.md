# GAAPF Advanced Usage Example

This example demonstrates advanced GAAPF features including programmatic usage, custom agent development, analytics integration, and building complete learning applications.

## 🚀 Programmatic GAAPF Usage

### Building a Custom Learning Application

```python
import asyncio
from pathlib import Path
from gaapf.core.learning_hub import LearningHubCore
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningStyle, LearningPace
from gaapf.core.analytics_system import get_analytics_engine
from gaapf.core.knowledge_graph import KnowledgeGraphManager
from gaapf.config.env_config import get_config

class AdvancedLearningApp:
    """Advanced learning application using GAAPF programmatically."""
    
    def __init__(self, data_path: Path = Path("data")):
        self.data_path = data_path
        self.learning_hub = None
        self.analytics = None
        self.knowledge_graph = None
        self.config = get_config()
    
    async def initialize(self):
        """Initialize all GAAPF components."""
        print("🚀 Initializing Advanced Learning Application...")
        
        # Initialize core components
        self.learning_hub = LearningHubCore(data_path=self.data_path)
        await self.learning_hub.initialize()
        
        # Initialize analytics
        self.analytics = get_analytics_engine(self.data_path / "analytics")
        self.analytics.start_real_time_processing()
        
        # Initialize knowledge graph
        self.knowledge_graph = KnowledgeGraphManager(self.data_path / "knowledge_graph")
        
        print("✅ All components initialized successfully!")
    
    async def create_advanced_user_profile(self, user_data: dict) -> UserProfile:
        """Create an advanced user profile with detailed preferences."""
        profile = UserProfile(
            user_id=user_data["user_id"],
            programming_experience_years=user_data["experience_years"],
            python_skill_level=SkillLevel(user_data["python_level"]),
            learning_pace=LearningPace(user_data["pace"]),
            preferred_learning_style=LearningStyle(user_data["learning_style"]),
            
            # Advanced preferences
            ai_framework_experience=user_data.get("ai_experience", {}),
            learning_goals=user_data.get("goals", []),
            time_availability=user_data.get("time_per_week", 10),
            preferred_difficulty_progression=user_data.get("difficulty_progression", "gradual"),
            accessibility_needs=user_data.get("accessibility", []),
            motivation_factors=user_data.get("motivation", ["achievement", "knowledge"])
        )
        
        # Save profile
        await self.learning_hub.save_user_profile(profile)
        
        # Initialize user in analytics
        self.analytics.initialize_user(profile.user_id, profile.to_dict())
        
        return profile
    
    async def create_personalized_learning_path(self, user_id: str, target_framework: str) -> dict:
        """Create a personalized learning path using knowledge graph."""
        print(f"🎯 Creating personalized learning path for {user_id}...")
        
        # Get user profile and progress
        profile = await self.learning_hub.get_user_profile(user_id)
        progress = await self.learning_hub.get_learning_progress(user_id)
        
        # Analyze user's current knowledge
        mastered_concepts = []
        if progress:
            mastered_concepts = [
                concept for concept, score in progress.get("concept_scores", {}).items()
                if score >= 0.8
            ]
        
        # Get knowledge gaps
        target_concepts = self._get_framework_target_concepts(target_framework, profile.python_skill_level)
        gaps = self.knowledge_graph.identify_knowledge_gaps(user_id, target_concepts)
        
        # Create optimized learning path
        learning_path = []
        for target_concept in target_concepts:
            path = self.knowledge_graph.find_learning_path(
                start_concepts=mastered_concepts,
                target_concept=target_concept,
                user_id=user_id
            )
            learning_path.extend(path)
        
        # Remove duplicates while preserving order
        unique_path = []
        seen = set()
        for concept in learning_path:
            if concept not in seen:
                unique_path.append(concept)
                seen.add(concept)
        
        # Estimate time and difficulty
        path_analysis = self._analyze_learning_path(unique_path, profile)
        
        return {
            "user_id": user_id,
            "target_framework": target_framework,
            "learning_path": unique_path,
            "estimated_time_hours": path_analysis["total_time"],
            "difficulty_progression": path_analysis["difficulty_curve"],
            "knowledge_gaps": gaps,
            "personalization_score": path_analysis["personalization_score"]
        }
    
    async def conduct_adaptive_learning_session(self, user_id: str, session_config: dict) -> dict:
        """Conduct an adaptive learning session with real-time optimization."""
        print(f"🎓 Starting adaptive learning session for {user_id}...")
        
        # Create session
        session_id = await self.learning_hub.create_session(
            user_id=user_id,
            framework=session_config["framework"],
            module_id=session_config["module_id"],
            llm=self.config.create_llm()
        )
        
        session_results = {
            "session_id": session_id,
            "interactions": [],
            "agent_usage": {},
            "learning_metrics": {},
            "adaptations_made": []
        }
        
        # Simulate learning interactions with adaptive responses
        for interaction in session_config.get("interactions", []):
            print(f"💬 Processing: {interaction['message'][:50]}...")
            
            # Process message through learning hub
            result = await self.learning_hub.process_message(
                user_id=user_id,
                message=interaction["message"],
                session_id=session_id
            )
            
            # Record interaction
            session_results["interactions"].append({
                "user_message": interaction["message"],
                "agent_response": result["response"],
                "agent_path": result["agent_path"],
                "confidence_scores": result.get("confidence_scores", {}),
                "handoff_reasons": result.get("handoff_reasons", [])
            })
            
            # Track agent usage
            for agent_type in result["agent_path"]:
                session_results["agent_usage"][agent_type] = session_results["agent_usage"].get(agent_type, 0) + 1
            
            # Simulate user engagement metrics
            engagement_score = self._calculate_engagement_score(interaction, result)
            comprehension_score = self._assess_comprehension(interaction, result)
            
            # Record metrics in analytics
            from gaapf.core.analytics_system import EngagementMetrics, LearningEffectivenessMetrics
            
            engagement_metrics = EngagementMetrics(
                session_duration=interaction.get("duration", 300),
                interaction_count=1,
                question_count=1 if "?" in interaction["message"] else 0,
                response_time_avg=2.5,
                attention_score=engagement_score,
                completion_rate=interaction.get("completion", 0.8)
            )
            
            effectiveness_metrics = LearningEffectivenessMetrics(
                comprehension_score=comprehension_score,
                retention_score=0.75,  # Would be measured over time
                application_score=0.8,
                progress_velocity=1.2,
                concept_mastery_rate=0.78,
                error_recovery_rate=0.9
            )
            
            self.analytics.record_engagement_metrics(
                user_id=user_id,
                session_id=session_id,
                framework=session_config["framework"],
                module_id=session_config["module_id"],
                metrics=engagement_metrics
            )
            
            self.analytics.record_effectiveness_metrics(
                user_id=user_id,
                session_id=session_id,
                framework=session_config["framework"],
                module_id=session_config["module_id"],
                metrics=effectiveness_metrics
            )
            
            # Check for needed adaptations
            adaptation = await self._check_for_adaptations(user_id, session_id, engagement_score, comprehension_score)
            if adaptation:
                session_results["adaptations_made"].append(adaptation)
                print(f"🔄 Adaptation applied: {adaptation['type']}")
        
        # End session and get final metrics
        await self.learning_hub.end_session(session_id)
        
        # Get session analytics
        session_analytics = self.analytics.analyze_learning_effectiveness(user_id, time_window_hours=1)
        session_results["learning_metrics"] = session_analytics
        
        print(f"✅ Session completed with {len(session_results['interactions'])} interactions")
        return session_results
    
    async def generate_comprehensive_report(self, user_id: str, time_period_days: int = 30) -> dict:
        """Generate a comprehensive learning report with insights and recommendations."""
        print(f"📊 Generating comprehensive report for {user_id}...")
        
        # Get user profile and progress
        profile = await self.learning_hub.get_user_profile(user_id)
        progress = await self.learning_hub.get_learning_progress(user_id)
        
        # Get analytics data
        analytics_data = self.analytics.analyze_learning_effectiveness(user_id, time_period_days * 24)
        dashboard_data = self.analytics.get_real_time_dashboard(user_id)
        
        # Get knowledge graph insights
        user_knowledge = self.knowledge_graph.get_user_knowledge(user_id)
        suggestions = self.knowledge_graph.suggest_next_concepts(user_id)
        
        # Compile comprehensive report
        report = {
            "user_profile": profile.to_dict() if profile else {},
            "reporting_period": f"Last {time_period_days} days",
            "learning_summary": {
                "total_sessions": progress.get("total_sessions", 0),
                "total_time_hours": progress.get("total_time_hours", 0),
                "frameworks_studied": progress.get("frameworks_studied", []),
                "concepts_mastered": len([c for c, s in user_knowledge.items() if s.get("mastery_level", 0) >= 0.8]),
                "overall_progress_score": analytics_data.get("overall_score", 0)
            },
            "performance_analysis": {
                "engagement_trends": analytics_data.get("engagement_analysis", {}),
                "learning_effectiveness": analytics_data.get("effectiveness_analysis", {}),
                "adaptive_performance": analytics_data.get("adaptive_analysis", {})
            },
            "knowledge_state": {
                "mastered_concepts": [c for c, s in user_knowledge.items() if s.get("mastery_level", 0) >= 0.8],
                "learning_concepts": [c for c, s in user_knowledge.items() if 0.3 <= s.get("mastery_level", 0) < 0.8],
                "knowledge_gaps": [c for c, s in user_knowledge.items() if s.get("mastery_level", 0) < 0.3]
            },
            "recommendations": {
                "next_concepts": [s["concept"] for s in suggestions[:5]],
                "learning_optimizations": analytics_data.get("recommendations", []),
                "personalized_suggestions": self._generate_personalized_suggestions(profile, analytics_data)
            },
            "insights": {
                "learning_patterns": self._identify_learning_patterns(analytics_data),
                "strengths": self._identify_strengths(analytics_data),
                "improvement_areas": self._identify_improvement_areas(analytics_data)
            }
        }
        
        return report
    
    def _get_framework_target_concepts(self, framework: str, skill_level: SkillLevel) -> list:
        """Get target concepts for a framework based on skill level."""
        framework_concepts = {
            "langchain": {
                SkillLevel.BEGINNER: ["langchain_basics", "simple_chains", "prompts"],
                SkillLevel.INTERMEDIATE: ["langchain_memory", "agents", "tools", "vector_stores"],
                SkillLevel.ADVANCED: ["custom_components", "production_deployment", "optimization"]
            },
            "langgraph": {
                SkillLevel.BEGINNER: ["langgraph_basics", "state_management"],
                SkillLevel.INTERMEDIATE: ["complex_workflows", "multi_agent_systems"],
                SkillLevel.ADVANCED: ["advanced_orchestration", "performance_optimization"]
            }
        }
        return framework_concepts.get(framework, {}).get(skill_level, [])
    
    def _analyze_learning_path(self, path: list, profile: UserProfile) -> dict:
        """Analyze a learning path for time estimation and difficulty."""
        # Simplified analysis - in production, this would be more sophisticated
        base_time_per_concept = 2  # hours
        difficulty_multipliers = {"beginner": 1.0, "intermediate": 1.5, "advanced": 2.0}
        
        total_time = len(path) * base_time_per_concept
        if profile.learning_pace == LearningPace.SLOW:
            total_time *= 1.5
        elif profile.learning_pace == LearningPace.FAST:
            total_time *= 0.7
        
        return {
            "total_time": total_time,
            "difficulty_curve": "gradual",  # Would analyze actual concept difficulties
            "personalization_score": 0.85  # Would calculate based on user preferences
        }
    
    def _calculate_engagement_score(self, interaction: dict, result: dict) -> float:
        """Calculate engagement score for an interaction."""
        # Simplified engagement calculation
        base_score = 0.7
        
        # Boost for questions
        if "?" in interaction["message"]:
            base_score += 0.1
        
        # Boost for longer messages (more engagement)
        if len(interaction["message"]) > 50:
            base_score += 0.1
        
        # Boost for follow-up questions
        if any(word in interaction["message"].lower() for word in ["why", "how", "what if", "can you"]):
            base_score += 0.1
        
        return min(base_score, 1.0)
    
    def _assess_comprehension(self, interaction: dict, result: dict) -> float:
        """Assess comprehension based on interaction."""
        # Simplified comprehension assessment
        base_score = 0.75
        
        # Would implement more sophisticated NLP-based assessment
        # For now, using simple heuristics
        
        return base_score
    
    async def _check_for_adaptations(self, user_id: str, session_id: str, engagement: float, comprehension: float) -> dict:
        """Check if adaptations are needed based on current metrics."""
        adaptations = []
        
        if engagement < 0.5:
            adaptations.append({
                "type": "engagement_boost",
                "reason": "Low engagement detected",
                "action": "Switch to more interactive content"
            })
        
        if comprehension < 0.6:
            adaptations.append({
                "type": "difficulty_reduction",
                "reason": "Low comprehension detected",
                "action": "Provide additional examples and explanations"
            })
        
        return adaptations[0] if adaptations else None
    
    def _generate_personalized_suggestions(self, profile: UserProfile, analytics: dict) -> list:
        """Generate personalized suggestions based on profile and analytics."""
        suggestions = []
        
        if profile.preferred_learning_style == LearningStyle.HANDS_ON:
            suggestions.append("Focus on more practical exercises and coding examples")
        
        if analytics.get("overall_score", 0) > 0.8:
            suggestions.append("Consider advancing to more challenging topics")
        
        return suggestions
    
    def _identify_learning_patterns(self, analytics: dict) -> list:
        """Identify learning patterns from analytics data."""
        patterns = []
        
        # Would implement pattern recognition algorithms
        patterns.append("Consistent improvement in comprehension scores")
        patterns.append("Higher engagement with hands-on content")
        
        return patterns
    
    def _identify_strengths(self, analytics: dict) -> list:
        """Identify user strengths from analytics."""
        return ["Quick problem-solving", "Strong practical application", "Good retention"]
    
    def _identify_improvement_areas(self, analytics: dict) -> list:
        """Identify areas for improvement."""
        return ["Theoretical understanding", "Complex concept synthesis"]


# Example usage
async def main():
    """Demonstrate advanced GAAPF usage."""
    app = AdvancedLearningApp()
    await app.initialize()
    
    # Create advanced user profile
    user_data = {
        "user_id": "advanced_learner_001",
        "experience_years": 5,
        "python_level": "intermediate",
        "pace": "moderate",
        "learning_style": "hands_on",
        "ai_experience": {"langchain": "beginner", "tensorflow": "intermediate"},
        "goals": ["Master LangChain", "Build production AI apps"],
        "time_per_week": 15,
        "difficulty_progression": "adaptive",
        "motivation": ["achievement", "career_growth"]
    }
    
    profile = await app.create_advanced_user_profile(user_data)
    print(f"✅ Created profile for {profile.user_id}")
    
    # Create personalized learning path
    learning_path = await app.create_personalized_learning_path(
        user_id=profile.user_id,
        target_framework="langchain"
    )
    print(f"📚 Learning path created with {len(learning_path['learning_path'])} concepts")
    print(f"⏱️ Estimated time: {learning_path['estimated_time_hours']} hours")
    
    # Conduct adaptive learning session
    session_config = {
        "framework": "langchain",
        "module_id": "lc_basics",
        "interactions": [
            {"message": "What is LangChain and why should I use it?", "duration": 300},
            {"message": "Can you show me a practical example of a chain?", "duration": 450},
            {"message": "I'm getting an error when I try to run this code", "duration": 600},
            {"message": "How does this compare to using OpenAI API directly?", "duration": 350}
        ]
    }
    
    session_results = await app.conduct_adaptive_learning_session(
        user_id=profile.user_id,
        session_config=session_config
    )
    print(f"🎓 Session completed with {len(session_results['interactions'])} interactions")
    print(f"🤖 Agents used: {list(session_results['agent_usage'].keys())}")
    
    # Generate comprehensive report
    report = await app.generate_comprehensive_report(profile.user_id, time_period_days=7)
    print(f"📊 Generated comprehensive report")
    print(f"📈 Overall progress score: {report['learning_summary']['overall_progress_score']:.2f}")
    print(f"🎯 Next recommended concepts: {report['recommendations']['next_concepts']}")
    
    print("\n🎉 Advanced GAAPF demonstration completed successfully!")

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔧 Custom Agent Development

### Creating a Specialized Domain Agent

```python
from gaapf.agents.base_agent import BaseGAAPFAgent
from typing import Dict, Any, List

class MLOpsSpecialistAgent(BaseGAAPFAgent):
    """Specialized agent for MLOps and production deployment guidance."""
    
    def __init__(self, **kwargs):
        super().__init__(agent_type="mlops_specialist", **kwargs)
        self.specialization_areas = [
            "model_deployment", "monitoring", "versioning", 
            "ci_cd", "infrastructure", "scaling"
        ]
    
    def get_confidence_score(self, message: str) -> float:
        """Calculate confidence score for MLOps-related queries."""
        message_lower = message.lower()
        
        # High confidence triggers
        high_confidence_keywords = [
            "deploy", "production", "monitoring", "mlops", 
            "docker", "kubernetes", "ci/cd", "pipeline",
            "scaling", "infrastructure", "versioning"
        ]
        
        # Medium confidence triggers
        medium_confidence_keywords = [
            "performance", "optimization", "testing",
            "automation", "workflow", "integration"
        ]
        
        # Calculate base confidence
        confidence = 0.1  # Base confidence
        
        for keyword in high_confidence_keywords:
            if keyword in message_lower:
                confidence += 0.15
        
        for keyword in medium_confidence_keywords:
            if keyword in message_lower:
                confidence += 0.08
        
        # Boost for MLOps-specific patterns
        if any(pattern in message_lower for pattern in [
            "how to deploy", "production ready", "model monitoring",
            "ci/cd for ml", "mlops best practices"
        ]):
            confidence += 0.2
        
        return min(confidence, 1.0)
    
    def _create_system_prompt(self) -> str:
        """Create specialized system prompt for MLOps guidance."""
        return f"""You are an MLOps Specialist Agent, part of the GAAPF learning system.

Your expertise includes:
- Model deployment strategies and best practices
- Production monitoring and observability
- ML pipeline automation and CI/CD
- Infrastructure scaling and optimization
- Model versioning and experiment tracking
- Performance optimization in production

User Profile: {self.user_profile}
Current Framework: {self.framework}
Learning Context: {self.module_id}

Provide practical, production-focused guidance for MLOps challenges.
Include specific tools, techniques, and real-world examples.
Always consider scalability, reliability, and maintainability.

When appropriate, suggest hands-on exercises or point to relevant documentation.
If the query is outside your MLOps expertise, suggest a handoff to a more appropriate agent.
"""
    
    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """Analyze if handoff to another agent is needed."""
        content_lower = content.lower()
        message_lower = user_message.lower()
        
        # Check for handoff triggers
        if any(keyword in message_lower for keyword in [
            "basic concept", "what is", "beginner", "introduction"
        ]):
            return {
                "needs_handoff": True,
                "suggested_agent": "instructor",
                "confidence": 0.8,
                "reason": "Basic conceptual question better handled by instructor"
            }
        
        if any(keyword in message_lower for keyword in [
            "code example", "implementation", "write code", "show me how"
        ]) and "deploy" not in message_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "code_assistant",
                "confidence": 0.7,
                "reason": "Code implementation request"
            }
        
        if any(keyword in message_lower for keyword in [
            "error", "bug", "not working", "issue", "problem"
        ]):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.9,
                "reason": "Error resolution needed"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.0,
            "reason": "Within MLOps specialist expertise"
        }

# Register the custom agent
from gaapf.agents.base_agent import AGENT_REGISTRY
AGENT_REGISTRY["mlops_specialist"] = MLOpsSpecialistAgent
```

This advanced example demonstrates the full power of GAAPF's programmatic interface, showing how to build sophisticated learning applications with real-time adaptation, comprehensive analytics, and custom agent development.
