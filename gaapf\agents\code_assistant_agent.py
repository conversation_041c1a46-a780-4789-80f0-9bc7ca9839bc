"""
Code assistant agent implementation for GAAPF.

This agent specializes in providing code examples, implementation guidance,
and practical coding assistance for Python AI frameworks.
"""

from typing import Dict, List, Optional, Any
import re

from gaapf.agents.base_agent import BaseGAAPFAgent


class CodeAssistantAgent(BaseGAAPFAgent):
    """
    Code assistant agent that provides code examples and implementation guidance.

    This agent specializes in:
    - Writing and explaining code examples
    - Providing implementation guidance
    - Demonstrating best practices
    - Code review and optimization suggestions
    - Debugging assistance
    """

    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return (
            f"You are an expert {self.framework} code assistant and developer. Your role is to provide "
            "practical code examples, implementation guidance, and hands-on coding assistance. You excel at "
            "writing clean, well-documented code that follows best practices and demonstrates proper usage "
            f"of {self.framework} features. You adapt your code examples to the user's skill level "
            f"({self.user_profile.get('skill_level', 'intermediate')}) and provide clear explanations of "
            "what the code does and why it's structured that way. Always include comments and follow "
            "Python coding standards."
        )

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Expert {self.framework} programming and implementation",
            "Writing clean, well-documented code examples",
            "Explaining code structure and logic",
            "Demonstrating best practices and design patterns",
            "Code review and optimization suggestions",
            "Debugging and troubleshooting assistance",
            "Adapting code complexity to user skill level",
            "Integration with external libraries and tools"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        user_lower = user_message.lower()
        content_lower = content.lower()

        # Keywords that suggest need for other agents
        theory_keywords = ["why", "explain concept", "theory", "understand", "how does it work"]
        practice_keywords = ["practice", "exercise", "tutorial", "step by step", "guide me through"]
        docs_keywords = ["documentation", "official docs", "reference", "api docs"]
        troubleshoot_keywords = ["error", "not working", "bug", "issue", "problem", "fix"]

        # Check for theoretical explanation needs
        if any(keyword in user_lower for keyword in theory_keywords):
            if not any(code_word in user_lower for code_word in ["code", "example", "implement"]):
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs theoretical explanation rather than code"
                }

        # Check for practice/tutorial needs
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants guided practice or tutorials"
            }

        # Check for documentation needs
        if any(keyword in user_lower for keyword in docs_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "documentation_expert",
                "confidence": 0.6,
                "reason": "User is looking for official documentation"
            }

        # Check for complex troubleshooting
        if any(keyword in user_lower for keyword in troubleshoot_keywords):
            if "complex" in user_lower or "multiple" in user_lower or "system" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "troubleshooter",
                    "confidence": 0.7,
                    "reason": "Complex troubleshooting may need specialized help"
                }

        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Content is appropriate for code assistance"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "code", "implement", "write", "build", "create", "function", "class",
            "example", "show me", "how to code", "programming", "script", "method"
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "syntax", "usage", "api", "library", "module", "import", "install",
            "setup", "configuration", "integration"
        ]

        # Low confidence keywords (better handled by other agents)
        low_confidence_keywords = [
            "explain", "theory", "concept", "why", "understand", "learn about",
            "practice", "exercise", "tutorial", "documentation", "docs"
        ]

        # Calculate confidence based on keyword presence
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        elif any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        elif any(keyword in message_lower for keyword in low_confidence_keywords):
            return 0.3
        else:
            # Default confidence for general questions
            return 0.5