#!/usr/bin/env python3
"""
Test script to verify the logging fix works correctly.

This script tests:
1. Centralized logging configuration
2. Agent logging functionality
3. No duplicate logging configuration conflicts
4. Proper logger inheritance across modules
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_centralized_logging():
    """Test the centralized logging configuration."""
    print("🧪 Testing centralized logging configuration...")
    
    try:
        from vinagent.logging_config import Vinagent<PERSON>ogger, get_logger, initialize_logging
        
        # Test initialization
        initialize_logging(log_level="INFO")
        assert VinagentLogger.is_initialized(), "Logging should be initialized"
        
        # Test logger creation
        logger = get_logger("test_logger")
        assert logger is not None, "Logger should be created"
        
        # Test logging output
        logger.info("Test log message from centralized logger")
        
        print("✅ Centralized logging configuration works")
        return True
        
    except Exception as e:
        print(f"❌ Centralized logging test failed: {e}")
        return False


def test_agent_logging():
    """Test that agents can use logging properly."""
    print("\n🧪 Testing agent logging functionality...")
    
    try:
        from vinagent.agent import Agent
        from langchain_together import ChatTogether
        
        # Create a simple agent
        llm = ChatTogether(model="meta-llama/Llama-3.3-70B-Instruct-Turbo-Free")
        agent = Agent(
            llm=llm,
            description="Test agent for logging verification",
            skills=["Testing logging functionality"]
        )
        
        # Test that the agent has access to logging
        # This should not raise any errors
        print("✅ Agent can be created with centralized logging")
        return True
        
    except Exception as e:
        print(f"❌ Agent logging test failed: {e}")
        return False


def test_no_duplicate_basicconfig():
    """Test that there are no duplicate basicConfig calls causing conflicts."""
    print("\n🧪 Testing for duplicate basicConfig conflicts...")
    
    try:
        import logging
        
        # Get the root logger
        root_logger = logging.getLogger()
        
        # Count handlers - should only have one console handler
        console_handlers = [h for h in root_logger.handlers if isinstance(h, logging.StreamHandler)]
        
        if len(console_handlers) <= 1:
            print("✅ No duplicate console handlers found")
            return True
        else:
            print(f"❌ Found {len(console_handlers)} console handlers - indicates duplicate basicConfig calls")
            return False
            
    except Exception as e:
        print(f"❌ Duplicate basicConfig test failed: {e}")
        return False


def test_module_loggers():
    """Test that different modules can get loggers properly."""
    print("\n🧪 Testing module logger creation...")
    
    try:
        from vinagent.logging_config import get_logger
        
        # Test getting loggers for different modules
        modules = [
            "vinagent.agent.agent",
            "vinagent.register.tool", 
            "vinagent.memory.memory",
            "gaapf.main"
        ]
        
        for module_name in modules:
            logger = get_logger(module_name)
            assert logger is not None, f"Logger for {module_name} should not be None"
            logger.info(f"Test message from {module_name}")
        
        print("✅ All module loggers work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Module logger test failed: {e}")
        return False


async def test_gaapf_integration():
    """Test that GAAPF can initialize with the new logging system."""
    print("\n🧪 Testing GAAPF integration with centralized logging...")
    
    try:
        # Set environment variables for testing
        os.environ["LOG_LEVEL"] = "INFO"
        
        # Import GAAPF main module
        from gaapf.main import logger as gaapf_logger
        
        # Test that GAAPF logger works
        gaapf_logger.info("Test message from GAAPF main")
        
        print("✅ GAAPF integration with centralized logging works")
        return True
        
    except Exception as e:
        print(f"❌ GAAPF integration test failed: {e}")
        return False


def main():
    """Run all logging tests."""
    print("🚀 Starting logging system tests...\n")
    
    tests = [
        test_centralized_logging,
        test_no_duplicate_basicconfig,
        test_module_loggers,
        test_agent_logging,
    ]
    
    async_tests = [
        test_gaapf_integration,
    ]
    
    results = []
    
    # Run synchronous tests
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Run asynchronous tests
    for test in async_tests:
        try:
            result = asyncio.run(test())
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All logging tests passed! The logging fix is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
