"""
Integration tests for GAAPF system.

This module tests the integration between different components:
- Learning Hub with agents and memory systems
- Agent constellation management
- End-to-end learning sessions
- CLI integration
- Configuration and environment setup
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

from gaapf.core.learning_hub import LearningHubCore
from gaapf.core.constellation import ConstellationManager
from gaapf.config.framework_configs import SupportedFrameworks
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from gaapf.config.env_config import EnvironmentConfig
from gaapf.interfaces.cli.app import GAAPFCli


class TestLearningHubIntegration:
    """Test Learning Hub integration with other components."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response from LLM"))
        return llm
    
    @pytest.fixture
    def sample_user_profile(self):
        """Sample user profile for testing."""
        return UserProfile(
            user_id="test_user",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            learning_pace=LearningPace.MODERATE,
            preferred_learning_style=LearningStyle.HANDS_ON
        )
    
    @pytest.fixture
    async def learning_hub(self, temp_data_path, mock_llm):
        """Learning hub instance for testing."""
        hub = LearningHubCore(data_path=temp_data_path)
        await hub.initialize()
        hub.llm = mock_llm
        return hub
    
    @pytest.mark.asyncio
    async def test_user_profile_management(self, learning_hub, sample_user_profile):
        """Test user profile creation and retrieval."""
        # Save user profile
        await learning_hub.save_user_profile(sample_user_profile)
        
        # Retrieve user profile
        retrieved_profile = await learning_hub.get_user_profile("test_user")
        
        assert retrieved_profile is not None
        assert retrieved_profile.user_id == "test_user"
        assert retrieved_profile.python_skill_level == SkillLevel.INTERMEDIATE
    
    @pytest.mark.asyncio
    async def test_session_creation_and_management(self, learning_hub, sample_user_profile, mock_llm):
        """Test session creation and management."""
        # Save user profile first
        await learning_hub.save_user_profile(sample_user_profile)
        
        # Create session
        session_id = await learning_hub.create_session(
            user_id="test_user",
            framework="langchain",
            module_id="lc_basics",
            llm=mock_llm
        )
        
        assert session_id is not None
        assert session_id in learning_hub.active_sessions
        
        # Get session
        session = learning_hub.get_session(session_id)
        assert session is not None
        assert session["user_id"] == "test_user"
        assert session["framework"] == "langchain"
    
    @pytest.mark.asyncio
    async def test_message_processing(self, learning_hub, sample_user_profile, mock_llm):
        """Test message processing through the learning hub."""
        # Setup
        await learning_hub.save_user_profile(sample_user_profile)
        session_id = await learning_hub.create_session(
            user_id="test_user",
            framework="langchain",
            module_id="lc_basics",
            llm=mock_llm
        )
        
        # Process message
        result = await learning_hub.process_message(
            user_id="test_user",
            message="How do I create a simple chain in LangChain?",
            session_id=session_id
        )
        
        assert result is not None
        assert "response" in result
        assert "agent_path" in result
        assert len(result["agent_path"]) > 0
        
        # Verify LLM was called
        mock_llm.ainvoke.assert_called()
    
    @pytest.mark.asyncio
    async def test_learning_progress_tracking(self, learning_hub, sample_user_profile, mock_llm):
        """Test learning progress tracking."""
        # Setup
        await learning_hub.save_user_profile(sample_user_profile)
        session_id = await learning_hub.create_session(
            user_id="test_user",
            framework="langchain",
            module_id="lc_basics",
            llm=mock_llm
        )
        
        # Process some messages to generate progress
        messages = [
            "What is LangChain?",
            "How do I create a chain?",
            "Can you show me an example?"
        ]
        
        for message in messages:
            await learning_hub.process_message(
                user_id="test_user",
                message=message,
                session_id=session_id
            )
        
        # Get learning progress
        progress = await learning_hub.get_learning_progress("test_user")
        
        assert progress is not None
        assert "total_sessions" in progress
        assert "frameworks_studied" in progress
        assert progress["total_sessions"] >= 1
    
    @pytest.mark.asyncio
    async def test_session_ending(self, learning_hub, sample_user_profile, mock_llm):
        """Test session ending and cleanup."""
        # Setup
        await learning_hub.save_user_profile(sample_user_profile)
        session_id = await learning_hub.create_session(
            user_id="test_user",
            framework="langchain",
            module_id="lc_basics",
            llm=mock_llm
        )
        
        # Verify session exists
        assert session_id in learning_hub.active_sessions
        
        # End session
        await learning_hub.end_session(session_id)
        
        # Verify session was removed
        assert session_id not in learning_hub.active_sessions


class TestConstellationIntegration:
    """Test constellation management integration."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return llm
    
    @pytest.fixture
    def sample_user_profile(self):
        """Sample user profile for testing."""
        return {
            "user_id": "test_user",
            "python_skill_level": "intermediate",
            "preferred_learning_style": "hands_on"
        }
    
    @pytest.fixture
    def constellation_manager(self, temp_data_path):
        """Constellation manager for testing."""
        return ConstellationManager(data_path=temp_data_path)
    
    @pytest.mark.asyncio
    async def test_constellation_creation(self, constellation_manager, mock_llm, sample_user_profile):
        """Test constellation creation for different scenarios."""
        # Create constellation for beginner user
        constellation = await constellation_manager.create_constellation(
            user_profile=sample_user_profile,
            framework="langchain",
            module_id="lc_basics",
            session_id="test_session",
            llm=mock_llm
        )
        
        assert constellation is not None
        assert len(constellation.agents) > 0
        assert constellation.primary_agent is not None
    
    @pytest.mark.asyncio
    async def test_agent_selection_and_handoff(self, constellation_manager, mock_llm, sample_user_profile):
        """Test agent selection and handoff logic."""
        # Create constellation
        constellation = await constellation_manager.create_constellation(
            user_profile=sample_user_profile,
            framework="langchain",
            module_id="lc_basics",
            session_id="test_session",
            llm=mock_llm
        )
        
        # Test different message types
        test_cases = [
            ("Explain how LangChain works", "instructor"),
            ("Show me code example", "code_assistant"),
            ("I'm getting an error", "troubleshooter"),
            ("I'm feeling frustrated", "motivational_coach")
        ]
        
        for message, expected_agent_type in test_cases:
            selected_agent = await constellation.select_agent(message, "test_user")
            
            # Verify appropriate agent was selected
            assert selected_agent is not None
            # Note: Exact agent type matching depends on confidence scoring implementation
    
    @pytest.mark.asyncio
    async def test_constellation_adaptation(self, constellation_manager, mock_llm, sample_user_profile):
        """Test constellation adaptation based on user progress."""
        # Create initial constellation
        constellation = await constellation_manager.create_constellation(
            user_profile=sample_user_profile,
            framework="langchain",
            module_id="lc_basics",
            session_id="test_session",
            llm=mock_llm
        )
        
        initial_agent_count = len(constellation.agents)
        
        # Simulate user progress
        progress_data = {
            "comprehension_score": 0.9,
            "engagement_level": 0.8,
            "difficulty_preference": "advanced"
        }
        
        # Adapt constellation
        await constellation.adapt_to_progress(progress_data)
        
        # Verify adaptation occurred
        # (Specific assertions depend on adaptation logic implementation)
        assert len(constellation.agents) >= initial_agent_count


class TestEndToEndLearningSession:
    """Test complete end-to-end learning sessions."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for testing."""
        llm = Mock()
        llm.ainvoke = AsyncMock(return_value=Mock(content="Helpful response about LangChain"))
        return llm
    
    @pytest.fixture
    async def learning_hub(self, temp_data_path, mock_llm):
        """Learning hub instance for testing."""
        hub = LearningHubCore(data_path=temp_data_path)
        await hub.initialize()
        hub.llm = mock_llm
        return hub
    
    @pytest.mark.asyncio
    async def test_complete_learning_session(self, learning_hub, mock_llm):
        """Test a complete learning session from start to finish."""
        # Create user profile
        user_profile = UserProfile(
            user_id="integration_test_user",
            programming_experience_years=2,
            python_skill_level=SkillLevel.BEGINNER,
            learning_pace=LearningPace.SLOW,
            preferred_learning_style=LearningStyle.VISUAL
        )
        
        await learning_hub.save_user_profile(user_profile)
        
        # Create session
        session_id = await learning_hub.create_session(
            user_id="integration_test_user",
            framework="langchain",
            module_id="lc_basics",
            llm=mock_llm
        )
        
        # Simulate a learning conversation
        conversation_flow = [
            "Hi, I'm new to LangChain. Can you help me get started?",
            "What are the main components of LangChain?",
            "Can you show me a simple example?",
            "I'm getting confused. Can you explain it differently?",
            "That's helpful! Can I try writing some code?",
            "I wrote some code but it's not working. Can you help debug it?",
            "Great! What should I learn next?"
        ]
        
        responses = []
        for message in conversation_flow:
            result = await learning_hub.process_message(
                user_id="integration_test_user",
                message=message,
                session_id=session_id
            )
            
            responses.append(result)
            
            # Verify each response
            assert result is not None
            assert "response" in result
            assert "agent_path" in result
            assert len(result["response"]) > 0
        
        # Verify different agents were used throughout the conversation
        all_agents_used = set()
        for response in responses:
            all_agents_used.update(response["agent_path"])
        
        # Should have used multiple agents for different types of questions
        assert len(all_agents_used) >= 2
        
        # Get final progress
        progress = await learning_hub.get_learning_progress("integration_test_user")
        assert progress["total_sessions"] >= 1
        assert "langchain" in progress["frameworks_studied"]
        
        # End session
        await learning_hub.end_session(session_id)
        assert session_id not in learning_hub.active_sessions


class TestConfigurationIntegration:
    """Test configuration and environment setup integration."""
    
    def test_config_validation(self):
        """Test configuration validation."""
        # This test would check if configuration validation works properly
        # Note: Actual implementation depends on environment setup
        config = EnvironmentConfig()
        
        # Test that config object is created
        assert config is not None
        
        # Test validation method exists
        assert hasattr(config, 'validate_configuration')
    
    @patch.dict('os.environ', {
        'TOGETHER_API_KEY': 'test_key',
        'OPENAI_API_KEY': 'test_openai_key'
    })
    def test_llm_provider_selection(self):
        """Test LLM provider selection based on available keys."""
        config = EnvironmentConfig()
        
        available_providers = config.get_available_llm_providers()
        
        # Should detect available providers based on environment variables
        assert isinstance(available_providers, dict)
        
        # Test LLM creation
        try:
            llm = config.create_llm()
            assert llm is not None
        except Exception:
            # LLM creation might fail in test environment, but should not raise unexpected errors
            pass


class TestCLIIntegration:
    """Test CLI integration with core systems."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def cli_app(self, temp_data_path):
        """CLI app instance for testing."""
        return GAAPFCli(data_path=temp_data_path)
    
    @patch('gaapf.config.env_config.get_config')
    @patch('questionary.select')
    @patch('questionary.text')
    def test_cli_initialization(self, mock_text, mock_select, mock_get_config, cli_app):
        """Test CLI initialization process."""
        # Mock configuration
        mock_config = Mock()
        mock_config.validate_configuration.return_value = {"valid": True}
        mock_config.data_path = Path("test_data")
        mock_config.create_llm.return_value = Mock()
        mock_config.default_llm_provider = "together"
        mock_config.get_available_llm_providers.return_value = {"together": True}
        mock_get_config.return_value = mock_config
        
        # Test initialization
        # Note: Full CLI testing would require more complex mocking of async operations
        assert cli_app is not None
        assert cli_app.data_path is not None
