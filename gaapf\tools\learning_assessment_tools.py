"""
Learning assessment tools for GAAPF.

This module implements tools for learning assessment, progress tracking,
and skill evaluation as specified in the methodology.
"""

import json
import random
from typing import Dict, List, Optional, Any
from pathlib import Path

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


class QuizGenerationInput(BaseModel):
    """Input schema for quiz generation tool."""
    framework: str = Field(description="Target framework for the quiz")
    module_id: str = Field(description="Specific module to focus on")
    difficulty: str = Field(default="intermediate", description="Difficulty level: beginner, intermediate, advanced")
    question_count: int = Field(default=5, description="Number of questions to generate")


class SkillAssessmentInput(BaseModel):
    """Input schema for skill assessment tool."""
    framework: str = Field(description="Framework to assess")
    user_responses: List[Dict] = Field(description="User responses to assessment questions")
    assessment_type: str = Field(default="comprehensive", description="Type of assessment")


class ProgressTrackingInput(BaseModel):
    """Input schema for progress tracking tool."""
    user_id: str = Field(description="User identifier")
    framework: str = Field(description="Framework being learned")
    session_data: Dict = Field(description="Session data to track")


class QuizGenerationTool(BaseTool):
    """Tool for generating framework-specific quizzes and assessments."""

    name: str = "generate_quiz"
    description: str = """Generate interactive quizzes and assessments for framework learning.
    Creates multiple-choice questions, coding challenges, and concept checks based on the framework and difficulty level.
    Use this to test user understanding and provide learning assessments."""
    args_schema: type = QuizGenerationInput

    def _run(self, framework: str, module_id: str, difficulty: str = "intermediate", question_count: int = 5) -> str:
        """Generate a quiz for the specified framework and module."""
        try:
            # Get question bank for the framework
            question_bank = self._get_question_bank()
            
            if framework.lower() not in question_bank:
                return f"Quiz generation not supported for framework: {framework}"
            
            framework_questions = question_bank[framework.lower()]
            
            # Filter questions by difficulty and module
            filtered_questions = []
            for question in framework_questions:
                if (question.get("difficulty", "intermediate") == difficulty and
                    (question.get("module") == module_id or question.get("module") == "general")):
                    filtered_questions.append(question)
            
            if len(filtered_questions) < question_count:
                # Add general questions if not enough specific ones
                for question in framework_questions:
                    if question.get("module") == "general" and question not in filtered_questions:
                        filtered_questions.append(question)
                        if len(filtered_questions) >= question_count:
                            break
            
            # Select random questions
            selected_questions = random.sample(
                filtered_questions, 
                min(question_count, len(filtered_questions))
            )
            
            # Format quiz
            quiz = {
                "framework": framework,
                "module_id": module_id,
                "difficulty": difficulty,
                "question_count": len(selected_questions),
                "questions": selected_questions,
                "instructions": "Answer all questions to the best of your ability. Each question has one correct answer.",
                "scoring": {
                    "total_points": len(selected_questions) * 10,
                    "passing_score": len(selected_questions) * 7  # 70% to pass
                }
            }
            
            return json.dumps(quiz, indent=2)
            
        except Exception as e:
            return f"Error generating quiz: {str(e)}"

    def _get_question_bank(self) -> Dict[str, List[Dict]]:
        """Get the question bank for different frameworks."""
        return {
            "langchain": [
                {
                    "id": "lc_001",
                    "module": "general",
                    "difficulty": "beginner",
                    "type": "multiple_choice",
                    "question": "What is LangChain primarily used for?",
                    "options": [
                        "Building web applications",
                        "Building applications with Large Language Models",
                        "Database management",
                        "Image processing"
                    ],
                    "correct_answer": 1,
                    "explanation": "LangChain is a framework for building applications with Large Language Models (LLMs)."
                },
                {
                    "id": "lc_002",
                    "module": "general",
                    "difficulty": "beginner",
                    "type": "multiple_choice",
                    "question": "Which component is used to format inputs to language models in LangChain?",
                    "options": [
                        "Chain",
                        "Agent",
                        "PromptTemplate",
                        "Memory"
                    ],
                    "correct_answer": 2,
                    "explanation": "PromptTemplate is used to format and structure inputs to language models."
                },
                {
                    "id": "lc_003",
                    "module": "general",
                    "difficulty": "intermediate",
                    "type": "multiple_choice",
                    "question": "What is the purpose of Memory in LangChain?",
                    "options": [
                        "To store database connections",
                        "To maintain conversation history and context",
                        "To cache API responses",
                        "To store user preferences"
                    ],
                    "correct_answer": 1,
                    "explanation": "Memory components in LangChain maintain conversation history and context across interactions."
                },
                {
                    "id": "lc_004",
                    "module": "general",
                    "difficulty": "intermediate",
                    "type": "coding",
                    "question": "Write a simple LangChain chain that uses a PromptTemplate and an LLM.",
                    "expected_concepts": ["PromptTemplate", "LLMChain", "LLM"],
                    "sample_answer": "from langchain.llms import OpenAI\nfrom langchain.prompts import PromptTemplate\nfrom langchain.chains import LLMChain\n\nllm = OpenAI()\nprompt = PromptTemplate(input_variables=['topic'], template='Explain {topic}')\nchain = LLMChain(llm=llm, prompt=prompt)"
                },
                {
                    "id": "lc_005",
                    "module": "general",
                    "difficulty": "advanced",
                    "type": "multiple_choice",
                    "question": "Which agent type is best for complex reasoning tasks that require multiple steps?",
                    "options": [
                        "ZERO_SHOT_REACT_DESCRIPTION",
                        "CONVERSATIONAL_REACT_DESCRIPTION",
                        "SELF_ASK_WITH_SEARCH",
                        "REACT_DOCSTORE"
                    ],
                    "correct_answer": 0,
                    "explanation": "ZERO_SHOT_REACT_DESCRIPTION agents use ReAct (Reasoning + Acting) for complex multi-step reasoning."
                }
            ],
            "langgraph": [
                {
                    "id": "lg_001",
                    "module": "general",
                    "difficulty": "beginner",
                    "type": "multiple_choice",
                    "question": "What is LangGraph primarily designed for?",
                    "options": [
                        "Creating static workflows",
                        "Building stateful, multi-agent applications",
                        "Data visualization",
                        "Web scraping"
                    ],
                    "correct_answer": 1,
                    "explanation": "LangGraph is designed for building stateful, multi-agent applications with complex workflows."
                },
                {
                    "id": "lg_002",
                    "module": "general",
                    "difficulty": "beginner",
                    "type": "multiple_choice",
                    "question": "In LangGraph, what defines the structure of data passed between nodes?",
                    "options": [
                        "Schema",
                        "State",
                        "Config",
                        "Memory"
                    ],
                    "correct_answer": 1,
                    "explanation": "State defines the structure of data that flows through the graph between nodes."
                },
                {
                    "id": "lg_003",
                    "module": "general",
                    "difficulty": "intermediate",
                    "type": "multiple_choice",
                    "question": "What is the purpose of conditional edges in LangGraph?",
                    "options": [
                        "To add delays between nodes",
                        "To route execution based on state conditions",
                        "To handle errors",
                        "To save state to memory"
                    ],
                    "correct_answer": 1,
                    "explanation": "Conditional edges allow routing execution to different nodes based on the current state."
                },
                {
                    "id": "lg_004",
                    "module": "general",
                    "difficulty": "advanced",
                    "type": "coding",
                    "question": "Create a simple LangGraph workflow with two nodes and conditional routing.",
                    "expected_concepts": ["StateGraph", "add_node", "add_conditional_edges"],
                    "sample_answer": "from langgraph.graph import StateGraph, END\n\nworkflow = StateGraph(State)\nworkflow.add_node('node1', node1_func)\nworkflow.add_node('node2', node2_func)\nworkflow.add_conditional_edges('node1', routing_func)\nworkflow.set_entry_point('node1')"
                }
            ]
        }


class SkillAssessmentTool(BaseTool):
    """Tool for comprehensive skill assessment and evaluation."""

    name: str = "assess_skills"
    description: str = """Assess user skills and knowledge based on their responses to questions and exercises.
    Provides detailed feedback, identifies strengths and weaknesses, and suggests improvement areas.
    Use this to evaluate learning progress and provide personalized feedback."""
    args_schema: type = SkillAssessmentInput

    def _run(self, framework: str, user_responses: List[Dict], assessment_type: str = "comprehensive") -> str:
        """Assess user skills based on their responses."""
        try:
            # Calculate scores and analyze responses
            total_questions = len(user_responses)
            correct_answers = 0
            detailed_feedback = []
            
            for response in user_responses:
                question_id = response.get("question_id")
                user_answer = response.get("user_answer")
                correct_answer = response.get("correct_answer")
                question_type = response.get("question_type", "multiple_choice")
                
                if question_type == "multiple_choice":
                    is_correct = user_answer == correct_answer
                    if is_correct:
                        correct_answers += 1
                    
                    detailed_feedback.append({
                        "question_id": question_id,
                        "correct": is_correct,
                        "feedback": response.get("explanation", ""),
                        "improvement_suggestion": self._get_improvement_suggestion(question_id, is_correct)
                    })
                
                elif question_type == "coding":
                    # Analyze coding response
                    code_analysis = self._analyze_code_response(user_answer, response.get("expected_concepts", []))
                    score = code_analysis["score"]
                    if score >= 0.7:  # 70% threshold for coding questions
                        correct_answers += 1
                    
                    detailed_feedback.append({
                        "question_id": question_id,
                        "correct": score >= 0.7,
                        "score": score,
                        "code_analysis": code_analysis,
                        "improvement_suggestion": self._get_coding_improvement_suggestion(code_analysis)
                    })
            
            # Calculate overall assessment
            score_percentage = (correct_answers / total_questions) * 100 if total_questions > 0 else 0
            skill_level = self._determine_skill_level(score_percentage)
            
            assessment_result = {
                "framework": framework,
                "assessment_type": assessment_type,
                "overall_score": score_percentage,
                "skill_level": skill_level,
                "correct_answers": correct_answers,
                "total_questions": total_questions,
                "detailed_feedback": detailed_feedback,
                "strengths": self._identify_strengths(detailed_feedback),
                "improvement_areas": self._identify_improvement_areas(detailed_feedback),
                "recommendations": self._generate_recommendations(skill_level, framework)
            }
            
            return json.dumps(assessment_result, indent=2)
            
        except Exception as e:
            return f"Error assessing skills: {str(e)}"

    def _analyze_code_response(self, code: str, expected_concepts: List[str]) -> Dict:
        """Analyze a coding response for key concepts."""
        analysis = {
            "concepts_found": [],
            "concepts_missing": [],
            "score": 0.0,
            "syntax_issues": [],
            "suggestions": []
        }
        
        code_lower = code.lower()
        
        # Check for expected concepts
        for concept in expected_concepts:
            if concept.lower() in code_lower:
                analysis["concepts_found"].append(concept)
            else:
                analysis["concepts_missing"].append(concept)
        
        # Calculate score based on concept coverage
        if expected_concepts:
            analysis["score"] = len(analysis["concepts_found"]) / len(expected_concepts)
        
        # Basic syntax checks
        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            analysis["syntax_issues"].append(str(e))
            analysis["score"] *= 0.5  # Penalize syntax errors
        
        return analysis

    def _determine_skill_level(self, score: float) -> str:
        """Determine skill level based on score."""
        if score >= 90:
            return "Expert"
        elif score >= 80:
            return "Advanced"
        elif score >= 70:
            return "Intermediate"
        elif score >= 60:
            return "Beginner+"
        else:
            return "Beginner"

    def _identify_strengths(self, feedback: List[Dict]) -> List[str]:
        """Identify user strengths based on feedback."""
        strengths = []
        correct_count = sum(1 for f in feedback if f.get("correct", False))
        
        if correct_count >= len(feedback) * 0.8:
            strengths.append("Strong overall understanding")
        
        # Add more specific strength identification logic
        return strengths

    def _identify_improvement_areas(self, feedback: List[Dict]) -> List[str]:
        """Identify areas for improvement."""
        improvement_areas = []
        incorrect_feedback = [f for f in feedback if not f.get("correct", False)]
        
        if len(incorrect_feedback) > 0:
            improvement_areas.append("Review fundamental concepts")
        
        return improvement_areas

    def _generate_recommendations(self, skill_level: str, framework: str) -> List[str]:
        """Generate personalized recommendations."""
        recommendations = []
        
        if skill_level in ["Beginner", "Beginner+"]:
            recommendations.extend([
                f"Focus on {framework} fundamentals",
                "Practice with simple examples",
                "Review official documentation"
            ])
        elif skill_level == "Intermediate":
            recommendations.extend([
                f"Explore advanced {framework} features",
                "Work on practical projects",
                "Study best practices and patterns"
            ])
        else:  # Advanced/Expert
            recommendations.extend([
                f"Contribute to {framework} community",
                "Mentor other learners",
                "Explore cutting-edge features"
            ])
        
        return recommendations

    def _get_improvement_suggestion(self, question_id: str, is_correct: bool) -> str:
        """Get improvement suggestion for a specific question."""
        if is_correct:
            return "Great job! Continue building on this knowledge."
        else:
            return "Review this concept and try related practice exercises."

    def _get_coding_improvement_suggestion(self, code_analysis: Dict) -> str:
        """Get improvement suggestion for coding questions."""
        if code_analysis["score"] >= 0.8:
            return "Excellent coding skills! Consider exploring more advanced patterns."
        elif code_analysis["concepts_missing"]:
            missing = ", ".join(code_analysis["concepts_missing"])
            return f"Focus on implementing these concepts: {missing}"
        else:
            return "Practice more coding exercises to improve implementation skills."


def create_learning_assessment_tools() -> List[BaseTool]:
    """Create all learning assessment tools."""
    return [
        QuizGenerationTool(),
        SkillAssessmentTool()
    ]
