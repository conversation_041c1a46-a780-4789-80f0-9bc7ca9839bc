"""
Static file handler for the web interface.

This module provides functionality to serve static files for the web interface.
"""

import os
from pathlib import Path
from typing import Optional

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse


def setup_static_files(app: FastAPI, static_dir: Optional[Path] = None) -> None:
    """Setup static file serving for the web interface.
    
    Args:
        app: The FastAPI application.
        static_dir: Optional path to the static directory. If not provided,
            defaults to the 'static' directory in the same directory as this file.
    """
    if static_dir is None:
        static_dir = Path(os.path.dirname(os.path.abspath(__file__))) / "static"
    
    # Mount the static directory
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    @app.get("/")
    async def serve_index():
        """Serve the index.html file."""
        return FileResponse(str(static_dir / "index.html")) 