"""
Research Assistant Agent for GAAPF.

This module implements the Research Assistant Agent that specializes in
information discovery, research support, and finding relevant learning resources.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class ResearchAssistantAgent(BaseGAAPFAgent):
    """
    Research Assistant Agent specializing in information discovery and research support.
    
    This agent focuses on:
    - Finding relevant learning resources and documentation
    - Conducting research on specific topics and concepts
    - Discovering best practices and industry standards
    - Locating examples and case studies
    - Providing comprehensive information synthesis
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Research Assistant Agent specializing in information discovery and research support for {self.framework} framework learning.

My core responsibilities include:
- Finding and curating relevant learning resources and documentation
- Conducting thorough research on specific topics and concepts
- Discovering best practices, patterns, and industry standards
- Locating practical examples, case studies, and real-world applications
- Synthesizing information from multiple sources into coherent summaries
- Identifying the most current and authoritative sources
- Providing comprehensive background information on complex topics
- Supporting deep-dive investigations into advanced concepts

I adapt my research approach to the user's skill level ({self.user_profile.get('python_skill_level', 'unknown')}) and learning goals, ensuring I provide information at the appropriate depth and complexity.

When conducting research, I:
1. Identify the most relevant and authoritative sources
2. Provide comprehensive yet digestible information summaries
3. Include practical examples and real-world applications
4. Cite sources and provide links for further exploration
5. Organize information in a logical, easy-to-follow structure
6. Highlight key insights and important considerations
7. Suggest related topics for expanded learning"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Comprehensive {self.framework} research and information discovery",
            "Finding authoritative documentation and learning resources",
            "Identifying best practices and industry standards",
            "Locating practical examples and case studies",
            "Synthesizing complex information into clear summaries",
            "Evaluating source credibility and relevance",
            "Conducting deep-dive research on advanced topics",
            "Organizing and structuring research findings",
            "Providing comprehensive background information",
            "Supporting investigative learning approaches"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        code_keywords = ["implement", "code", "example", "syntax", "write", "create", "build"]
        theory_keywords = ["explain", "teach", "learn", "understand", "concept"]
        practice_keywords = ["practice", "exercise", "hands-on", "try", "do", "apply"]
        help_keywords = ["stuck", "error", "problem", "issue", "debug", "fix"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user wants to implement something after research
        if any(keyword in user_lower for keyword in code_keywords):
            if "how to" in user_lower or "implement" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.8,
                    "reason": "User wants to implement findings from research"
                }
        
        # Check if user needs detailed explanation after research overview
        if any(keyword in user_lower for keyword in theory_keywords):
            if "explain" in user_lower or "teach me" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs detailed explanation of research findings"
                }
        
        # Check if user wants to practice what they researched
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants to practice concepts from research"
            }
        
        # Check if user needs help with research findings
        if any(keyword in user_lower for keyword in help_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.7,
                "reason": "User needs help understanding research findings"
            }
        
        # Check if research reveals complex topics needing guidance
        if "complex" in user_lower or "advanced" in user_lower or "difficult" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "mentor",
                "confidence": 0.7,
                "reason": "Research reveals complex topics needing mentoring support"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Research assistant can continue providing information discovery"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "research", "find", "search", "look up", "information", "resources",
            "documentation", "best practices", "examples", "case studies",
            "what are", "tell me about", "learn about", "background", "overview"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "compare", "difference", "options", "alternatives", "approaches",
            "methods", "techniques", "patterns", "standards", "guidelines"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest research needs
        question_patterns = [
            r"what is",
            r"what are",
            r"how does",
            r"where can i find",
            r"tell me about",
            r"i need information",
            r"can you research"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in question_patterns):
            return 0.8
        
        return 0.3
