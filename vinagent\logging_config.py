"""
Centralized logging configuration for vinagent and GAAPF.

This module provides a unified logging configuration system that can be used
across all components to ensure consistent logging behavior and prevent
configuration conflicts.
"""

import logging
import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any


class VinagentLogger:
    """
    Centralized logger configuration for vinagent and GAAPF.
    
    This class ensures that logging is configured consistently across all
    components and prevents conflicts from multiple basicConfig() calls.
    """
    
    _initialized = False
    _loggers: Dict[str, logging.Logger] = {}
    
    @classmethod
    def initialize(
        cls,
        log_level: Optional[str] = None,
        log_format: Optional[str] = None,
        log_file: Optional[Path] = None,
        console_output: bool = True
    ) -> None:
        """
        Initialize the logging configuration.
        
        Args:
            log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            log_format: Custom log format string
            log_file: Optional file to write logs to
            console_output: Whether to output logs to console
        """
        if cls._initialized:
            return
            
        # Get log level from environment or parameter
        if log_level is None:
            log_level = os.getenv("LOG_LEVEL", "INFO").upper()
            
        # Validate log level
        numeric_level = getattr(logging, log_level, logging.INFO)
        
        # Default format
        if log_format is None:
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # Clear any existing handlers to prevent duplicates
        root_logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(log_format)
        
        # Add console handler if requested
        if console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(numeric_level)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
        
        # Add file handler if requested
        if log_file:
            log_file = Path(log_file)
            log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(numeric_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        cls._initialized = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        Get a logger instance with the given name.
        
        Args:
            name: Logger name (typically __name__)
            
        Returns:
            Configured logger instance
        """
        # Initialize with defaults if not already done
        if not cls._initialized:
            cls.initialize()
        
        # Return cached logger or create new one
        if name not in cls._loggers:
            logger = logging.getLogger(name)
            cls._loggers[name] = logger
        
        return cls._loggers[name]
    
    @classmethod
    def set_level(cls, level: str) -> None:
        """
        Set the logging level for all loggers.
        
        Args:
            level: New logging level
        """
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        
        # Update root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(numeric_level)
        
        # Update all handlers
        for handler in root_logger.handlers:
            handler.setLevel(numeric_level)
    
    @classmethod
    def is_initialized(cls) -> bool:
        """Check if logging has been initialized."""
        return cls._initialized
    
    @classmethod
    def reset(cls) -> None:
        """Reset the logging configuration (mainly for testing)."""
        cls._initialized = False
        cls._loggers.clear()
        
        # Clear root logger handlers
        root_logger = logging.getLogger()
        root_logger.handlers.clear()


def get_logger(name: str) -> logging.Logger:
    """
    Convenience function to get a logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Configured logger instance
    """
    return VinagentLogger.get_logger(name)


def initialize_logging(
    log_level: Optional[str] = None,
    log_format: Optional[str] = None,
    log_file: Optional[Path] = None,
    console_output: bool = True
) -> None:
    """
    Convenience function to initialize logging.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Custom log format string
        log_file: Optional file to write logs to
        console_output: Whether to output logs to console
    """
    VinagentLogger.initialize(
        log_level=log_level,
        log_format=log_format,
        log_file=log_file,
        console_output=console_output
    )


def set_log_level(level: str) -> None:
    """
    Convenience function to set logging level.
    
    Args:
        level: New logging level
    """
    VinagentLogger.set_level(level)


# Auto-initialize with environment variables if not already done
def _auto_initialize():
    """Auto-initialize logging with environment variables."""
    if not VinagentLogger.is_initialized():
        log_level = os.getenv("LOG_LEVEL", "INFO")
        log_file_path = os.getenv("LOG_FILE")
        log_file = Path(log_file_path) if log_file_path else None
        
        VinagentLogger.initialize(
            log_level=log_level,
            log_file=log_file,
            console_output=True
        )


# Auto-initialize when module is imported
_auto_initialize()
