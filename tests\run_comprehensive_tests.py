#!/usr/bin/env python3
"""
Comprehensive Test Runner for vinagent project.

This script runs all tests and generates a comprehensive report including:
- Test coverage analysis
- Performance benchmarks
- Error detection and reporting
- Integration test results
- Summary of findings and recommendations
"""

import subprocess
import sys
import json
import time
from pathlib import Path
from datetime import datetime
import argparse


class TestRunner:
    """Comprehensive test runner for vinagent project."""
    
    def __init__(self, output_dir: Path = None):
        """Initialize test runner."""
        self.output_dir = output_dir or Path("test_reports")
        self.output_dir.mkdir(exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results = {
            "timestamp": self.timestamp,
            "test_suites": {},
            "summary": {},
            "recommendations": []
        }
    
    def run_test_suite(self, suite_name: str, test_pattern: str, timeout: int = 300) -> dict:
        """Run a specific test suite and capture results."""
        print(f"\n{'='*60}")
        print(f"Running {suite_name}")
        print(f"{'='*60}")
        
        start_time = time.time()
        
        # Construct pytest command
        cmd = [
            sys.executable, "-m", "pytest",
            test_pattern,
            "-v",
            "--tb=short",
            "--json-report",
            f"--json-report-file={self.output_dir}/{suite_name}_{self.timestamp}.json",
            "--html", f"{self.output_dir}/{suite_name}_{self.timestamp}.html",
            "--self-contained-html"
        ]
        
        try:
            # Run the test suite
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=Path.cwd()
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Parse results
            suite_results = {
                "suite_name": suite_name,
                "duration": duration,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "success": result.returncode == 0,
                "test_count": self._extract_test_count(result.stdout),
                "passed": self._extract_passed_count(result.stdout),
                "failed": self._extract_failed_count(result.stdout),
                "errors": self._extract_error_count(result.stdout),
                "skipped": self._extract_skipped_count(result.stdout)
            }
            
            # Load JSON report if available
            json_report_path = self.output_dir / f"{suite_name}_{self.timestamp}.json"
            if json_report_path.exists():
                try:
                    with open(json_report_path, 'r') as f:
                        json_data = json.load(f)
                        suite_results["detailed_results"] = json_data
                except Exception as e:
                    print(f"Warning: Could not load JSON report: {e}")
            
            print(f"✅ {suite_name} completed in {duration:.2f}s")
            print(f"   Tests: {suite_results['test_count']}, "
                  f"Passed: {suite_results['passed']}, "
                  f"Failed: {suite_results['failed']}, "
                  f"Errors: {suite_results['errors']}, "
                  f"Skipped: {suite_results['skipped']}")
            
        except subprocess.TimeoutExpired:
            end_time = time.time()
            duration = end_time - start_time
            
            suite_results = {
                "suite_name": suite_name,
                "duration": duration,
                "return_code": -1,
                "success": False,
                "error": "Test suite timed out",
                "timeout": True
            }
            
            print(f"❌ {suite_name} timed out after {timeout}s")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            suite_results = {
                "suite_name": suite_name,
                "duration": duration,
                "return_code": -1,
                "success": False,
                "error": str(e),
                "exception": True
            }
            
            print(f"❌ {suite_name} failed with exception: {e}")
        
        self.results["test_suites"][suite_name] = suite_results
        return suite_results
    
    def _extract_test_count(self, output: str) -> int:
        """Extract total test count from pytest output."""
        try:
            # Look for pattern like "collected 25 items"
            import re
            match = re.search(r'collected (\d+) items?', output)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def _extract_passed_count(self, output: str) -> int:
        """Extract passed test count from pytest output."""
        try:
            import re
            match = re.search(r'(\d+) passed', output)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def _extract_failed_count(self, output: str) -> int:
        """Extract failed test count from pytest output."""
        try:
            import re
            match = re.search(r'(\d+) failed', output)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def _extract_error_count(self, output: str) -> int:
        """Extract error count from pytest output."""
        try:
            import re
            match = re.search(r'(\d+) error', output)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def _extract_skipped_count(self, output: str) -> int:
        """Extract skipped test count from pytest output."""
        try:
            import re
            match = re.search(r'(\d+) skipped', output)
            if match:
                return int(match.group(1))
        except:
            pass
        return 0
    
    def run_all_tests(self):
        """Run all test suites."""
        print("🚀 Starting Comprehensive Test Execution")
        print(f"📊 Results will be saved to: {self.output_dir}")
        
        # Define test suites
        test_suites = [
            ("Existing_Tests", "tests/test_*.py", 180),
            ("End_to_End_Scenarios", "tests/test_end_to_end_scenarios.py", 300),
            ("Integration_Tests", "tests/test_integration_components.py", 240),
            ("Error_Handling", "tests/test_error_handling_edge_cases.py", 180),
            ("Performance_Tests", "tests/test_performance_load.py", 300),
            ("Vinagent_Unit_Tests", "tests/test_vinagent_*.py", 240)
        ]
        
        # Run each test suite
        for suite_name, pattern, timeout in test_suites:
            self.run_test_suite(suite_name, pattern, timeout)
        
        # Generate summary
        self.generate_summary()
        
        # Generate recommendations
        self.generate_recommendations()
        
        # Save comprehensive report
        self.save_report()
        
        # Print final summary
        self.print_final_summary()
    
    def generate_summary(self):
        """Generate test execution summary."""
        total_tests = 0
        total_passed = 0
        total_failed = 0
        total_errors = 0
        total_skipped = 0
        total_duration = 0
        successful_suites = 0
        
        for suite_name, results in self.results["test_suites"].items():
            if results.get("success", False):
                successful_suites += 1
            
            total_tests += results.get("test_count", 0)
            total_passed += results.get("passed", 0)
            total_failed += results.get("failed", 0)
            total_errors += results.get("errors", 0)
            total_skipped += results.get("skipped", 0)
            total_duration += results.get("duration", 0)
        
        self.results["summary"] = {
            "total_suites": len(self.results["test_suites"]),
            "successful_suites": successful_suites,
            "total_tests": total_tests,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "total_errors": total_errors,
            "total_skipped": total_skipped,
            "total_duration": total_duration,
            "success_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0,
            "suite_success_rate": (successful_suites / len(self.results["test_suites"]) * 100) if self.results["test_suites"] else 0
        }
    
    def generate_recommendations(self):
        """Generate recommendations based on test results."""
        recommendations = []
        summary = self.results["summary"]
        
        # Overall health assessment
        if summary["success_rate"] >= 90:
            recommendations.append("✅ Excellent test coverage and stability")
        elif summary["success_rate"] >= 75:
            recommendations.append("⚠️ Good test coverage but some issues need attention")
        else:
            recommendations.append("❌ Significant testing issues require immediate attention")
        
        # Specific recommendations
        if summary["total_failed"] > 0:
            recommendations.append(f"🔧 Fix {summary['total_failed']} failing tests")
        
        if summary["total_errors"] > 0:
            recommendations.append(f"🐛 Resolve {summary['total_errors']} test errors")
        
        if summary["total_skipped"] > summary["total_tests"] * 0.1:
            recommendations.append("📝 Review skipped tests - high skip rate detected")
        
        # Performance recommendations
        if summary["total_duration"] > 600:  # 10 minutes
            recommendations.append("⚡ Consider optimizing test execution time")
        
        # Suite-specific recommendations
        for suite_name, results in self.results["test_suites"].items():
            if not results.get("success", False):
                if results.get("timeout", False):
                    recommendations.append(f"⏱️ {suite_name} timed out - investigate performance issues")
                elif results.get("exception", False):
                    recommendations.append(f"💥 {suite_name} crashed - check test environment")
                else:
                    recommendations.append(f"🔍 Investigate failures in {suite_name}")
        
        self.results["recommendations"] = recommendations
    
    def save_report(self):
        """Save comprehensive test report."""
        report_file = self.output_dir / f"comprehensive_report_{self.timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"📄 Comprehensive report saved to: {report_file}")
    
    def print_final_summary(self):
        """Print final test execution summary."""
        print(f"\n{'='*80}")
        print("🎯 COMPREHENSIVE TEST EXECUTION SUMMARY")
        print(f"{'='*80}")
        
        summary = self.results["summary"]
        
        print(f"📊 Test Statistics:")
        print(f"   Total Test Suites: {summary['total_suites']}")
        print(f"   Successful Suites: {summary['successful_suites']}")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['total_passed']}")
        print(f"   Failed: {summary['total_failed']}")
        print(f"   Errors: {summary['total_errors']}")
        print(f"   Skipped: {summary['total_skipped']}")
        print(f"   Success Rate: {summary['success_rate']:.1f}%")
        print(f"   Total Duration: {summary['total_duration']:.1f}s")
        
        print(f"\n🎯 Recommendations:")
        for rec in self.results["recommendations"]:
            print(f"   {rec}")
        
        print(f"\n📁 Reports Location: {self.output_dir}")
        print(f"{'='*80}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run comprehensive tests for vinagent project")
    parser.add_argument("--output-dir", type=Path, help="Output directory for test reports")
    parser.add_argument("--suite", type=str, help="Run specific test suite only")
    
    args = parser.parse_args()
    
    runner = TestRunner(output_dir=args.output_dir)
    
    if args.suite:
        # Run specific suite
        runner.run_test_suite(args.suite, f"tests/test_{args.suite}*.py")
    else:
        # Run all tests
        runner.run_all_tests()


if __name__ == "__main__":
    main()
