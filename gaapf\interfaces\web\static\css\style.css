:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --text-color: #333;
    --light-bg: #f5f5f5;
    --dark-bg: #333;
    --border-color: #ddd;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-bg);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 3rem;
    color: var(--primary-color);
}

header h2 {
    font-size: 1.5rem;
    color: var(--text-color);
    opacity: 0.8;
}

.panel {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

input[type="text"],
select {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 5px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn.primary {
    background-color: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background-color: #2980b9;
}

.btn.danger {
    background-color: var(--accent-color);
    color: white;
}

.btn.danger:hover {
    background-color: #c0392b;
}

.hidden {
    display: none;
}

#chat-panel {
    display: flex;
    height: 80vh;
    gap: 20px;
}

.sidebar {
    width: 200px;
    background-color: var(--light-bg);
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
}

.sidebar h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.sidebar ul {
    list-style: none;
    margin-bottom: 20px;
    flex-grow: 1;
}

.sidebar li {
    padding: 10px;
    margin-bottom: 5px;
    background-color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.sidebar li:hover {
    background-color: #e0e0e0;
}

.sidebar li.active {
    background-color: var(--primary-color);
    color: white;
}

.chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    margin-bottom: 15px;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 8px;
    max-width: 80%;
}

.message.user {
    background-color: var(--primary-color);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.message.agent {
    background-color: var(--light-bg);
    align-self: flex-start;
}

.message .sender {
    font-weight: bold;
    margin-bottom: 5px;
}

.message-input {
    display: flex;
    gap: 10px;
}

.message-input input {
    flex-grow: 1;
}

.error-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: var(--accent-color);
    color: white;
    padding: 15px 20px;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
}

.error-message.show {
    opacity: 1;
    transform: translateY(0);
}

@media (max-width: 768px) {
    #chat-panel {
        flex-direction: column;
        height: auto;
    }
    
    .sidebar {
        width: 100%;
        margin-bottom: 15px;
    }
} 