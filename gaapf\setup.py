#!/usr/bin/env python3
"""
Setup script for GAAPF.

This script helps users set up the GAAPF environment and dependencies.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 10):
        print("❌ Error: Python 3.10 or higher is required.")
        print(f"   Current version: {sys.version}")
        print("   Please upgrade Python and try again.")
        return False
    
    print(f"✅ Python version: {sys.version.split()[0]} (compatible)")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def check_api_keys():
    """Check if API keys are configured."""
    print("🔑 Checking API key configuration...")

    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ No .env file found")
        print("💡 Copy .env.example to .env and configure your API keys:")
        print("   cp .env.example .env")
        print("   # Then edit .env with your API keys")
        return False

    # Load environment variables from .env
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ .env file found and loaded")
    except ImportError:
        print("❌ python-dotenv not installed. Run: pip install python-dotenv")
        return False

    api_keys = {
        "OPENAI_API_KEY": "OpenAI GPT",
        "GOOGLE_API_KEY": "Google Gemini",
        "ANTHROPIC_API_KEY": "Anthropic Claude",
        "TAVILY_API_KEY": "Tavily Search (Optional)"
    }

    configured_keys = []
    missing_keys = []

    for key, service in api_keys.items():
        if os.getenv(key):
            configured_keys.append(f"✅ {service}")
        else:
            missing_keys.append(f"❌ {service} ({key})")

    if configured_keys:
        print("Configured API keys:")
        for key in configured_keys:
            print(f"  {key}")

    if missing_keys:
        print("\nMissing API keys:")
        for key in missing_keys:
            print(f"  {key}")

        print("\n💡 To configure API keys, edit your .env file:")
        print("   # Add your API keys to .env file")
        print("   OPENAI_API_KEY=your_key_here")
        print("   GOOGLE_API_KEY=your_key_here")
        print("   ANTHROPIC_API_KEY=your_key_here")
        print("   TAVILY_API_KEY=your_key_here  # Optional")

        if not configured_keys:
            print("\n⚠️  Warning: No API keys configured. You'll need at least one to use GAAPF.")
            return False

    return True

def create_data_directory():
    """Create data directory structure."""
    print("📁 Creating data directory structure...")
    
    data_dirs = [
        "data",
        "data/memory",
        "data/sessions", 
        "data/profiles",
        "data/analytics"
    ]
    
    for dir_path in data_dirs:
        Path(dir_path).mkdir(exist_ok=True, parents=True)
    
    print("✅ Data directories created")
    return True

def run_basic_test():
    """Run basic functionality test."""
    print("🧪 Running basic functionality test...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_basic.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Basic test passed")
            return True
        else:
            print("❌ Basic test failed")
            print("Error output:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("❌ Basic test timed out")
        return False
    except Exception as e:
        print(f"❌ Error running basic test: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 GAAPF Setup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed: Could not install dependencies")
        sys.exit(1)
    
    # Create data directories
    if not create_data_directory():
        print("\n❌ Setup failed: Could not create data directories")
        sys.exit(1)
    
    # Check API keys
    api_keys_ok = check_api_keys()
    
    # Run basic test
    test_passed = run_basic_test()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Setup Summary:")
    print("✅ Python version: Compatible")
    print("✅ Dependencies: Installed")
    print("✅ Data directories: Created")
    print(f"{'✅' if api_keys_ok else '⚠️ '} API keys: {'Configured' if api_keys_ok else 'Needs configuration'}")
    print(f"{'✅' if test_passed else '❌'} Basic test: {'Passed' if test_passed else 'Failed'}")
    
    if api_keys_ok and test_passed:
        print("\n🎉 Setup completed successfully!")
        print("\n🚀 Quick Start:")
        print("   python -m gaapf                      # CLI interface")
        print("   python -m gaapf --help               # Show help")
    elif not api_keys_ok:
        print("\n⚠️  Setup completed with warnings.")
        print("   Please configure at least one API key in your .env file to use GAAPF.")
        print("   Copy .env.example to .env and add your API keys.")
    else:
        print("\n❌ Setup completed with errors.")
        print("   Please check the error messages above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
