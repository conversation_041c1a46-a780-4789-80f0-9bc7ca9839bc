#!/usr/bin/env python3
"""
Streamlit demo interface for GAAPF.

This provides a visual demonstration of the GAAPF system capabilities
with mock responses for demonstration purposes.
"""

import streamlit as st
import asyncio
import json
import time
from pathlib import Path
from typing import Dict, List, Optional

# Configure Streamlit page
st.set_page_config(
    page_title="GAAPF - Guidance AI Agent for Python Framework",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
        border-left: 4px solid #1f77b4;
    }
    .constellation-info {
        background-color: #e8f4fd;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .metrics-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'messages' not in st.session_state:
    st.session_state.messages = []
if 'current_agent' not in st.session_state:
    st.session_state.current_agent = "instructor"
if 'constellation_type' not in st.session_state:
    st.session_state.constellation_type = "Theory-Practice Balanced"
if 'user_profile' not in st.session_state:
    st.session_state.user_profile = {
        "skill_level": "Intermediate",
        "learning_style": "Hands-On",
        "framework": "LangChain"
    }

# Mock agent responses
MOCK_RESPONSES = {
    "instructor": {
        "greeting": "Hello! I'm your AI instructor. I specialize in explaining concepts and providing theoretical foundations. What would you like to learn about?",
        "responses": [
            "Let me explain the core concepts behind this framework...",
            "This is an important theoretical foundation that will help you understand...",
            "The key principle here is that we need to understand the underlying architecture...",
            "From a conceptual perspective, this works by..."
        ]
    },
    "code_assistant": {
        "greeting": "Hi! I'm your code assistant. I can help you with implementation, examples, and coding best practices. What would you like to build?",
        "responses": [
            "Here's a practical code example that demonstrates this concept:\n```python\nfrom langchain import LLMChain\n# Your code here\n```",
            "Let me show you the implementation step by step...",
            "Here's how you would structure this in your code...",
            "The best practice for this scenario would be..."
        ]
    },
    "documentation_expert": {
        "greeting": "Welcome! I'm your documentation expert. I can help you find official documentation, API references, and detailed specifications.",
        "responses": [
            "According to the official documentation, this feature works as follows...",
            "You can find more details in the API reference at...",
            "The official documentation states that...",
            "Here are the relevant documentation links for this topic..."
        ]
    },
    "practice_facilitator": {
        "greeting": "Hello! I'm your practice facilitator. I create hands-on exercises and guide you through practical learning experiences.",
        "responses": [
            "Let's try a hands-on exercise to practice this concept...",
            "Here's a step-by-step tutorial you can follow...",
            "I've prepared a practical exercise for you to try...",
            "Let's build something together to reinforce your learning..."
        ]
    },
    "mentor": {
        "greeting": "Hi there! I'm your learning mentor. I'm here to provide guidance, support, and help you navigate your learning journey.",
        "responses": [
            "Based on your progress, I recommend focusing on...",
            "You're doing great! Here's what I suggest for your next steps...",
            "Don't worry if this seems challenging - let's break it down...",
            "I can see you're making good progress. Let me help you with..."
        ]
    }
}

# Constellation configurations
CONSTELLATION_TYPES = {
    "Knowledge Intensive": {
        "description": "Focus on theoretical understanding and concepts",
        "primary_agents": ["instructor", "documentation_expert", "research_assistant"],
        "support_agents": ["mentor", "progress_tracker"],
        "color": "#ff6b6b"
    },
    "Hands-On Focused": {
        "description": "Practical implementation and coding skills",
        "primary_agents": ["code_assistant", "practice_facilitator", "project_guide"],
        "support_agents": ["troubleshooter", "mentor"],
        "color": "#4ecdc4"
    },
    "Theory-Practice Balanced": {
        "description": "Balanced approach between theory and practice",
        "primary_agents": ["instructor", "code_assistant", "practice_facilitator"],
        "support_agents": ["documentation_expert", "mentor"],
        "color": "#45b7d1"
    },
    "Basic Learning": {
        "description": "Gentle introduction for beginners",
        "primary_agents": ["instructor", "code_assistant"],
        "support_agents": ["mentor", "practice_facilitator"],
        "color": "#96ceb4"
    },
    "Guided Learning": {
        "description": "Structured guidance with personalized support",
        "primary_agents": ["instructor", "mentor"],
        "support_agents": ["code_assistant", "practice_facilitator"],
        "color": "#feca57"
    }
}

def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🤖 GAAPF Demo - Adaptive Learning Constellation</h1>', unsafe_allow_html=True)
    
    # Sidebar for configuration
    with st.sidebar:
        st.header("🎛️ Configuration")
        
        # User Profile
        st.subheader("👤 User Profile")
        skill_level = st.selectbox(
            "Skill Level",
            ["Beginner", "Intermediate", "Advanced", "Expert"],
            index=1
        )
        
        learning_style = st.selectbox(
            "Learning Style",
            ["Visual", "Hands-On", "Theoretical", "Mixed"],
            index=1
        )
        
        framework = st.selectbox(
            "Framework",
            ["LangChain", "LangGraph", "CrewAI", "AutoGen"],
            index=0
        )
        
        # Update session state
        st.session_state.user_profile = {
            "skill_level": skill_level,
            "learning_style": learning_style,
            "framework": framework
        }
        
        # Constellation Selection
        st.subheader("⭐ Constellation Type")
        constellation_type = st.selectbox(
            "Select Constellation",
            list(CONSTELLATION_TYPES.keys()),
            index=2
        )
        st.session_state.constellation_type = constellation_type
        
        # Display constellation info
        constellation_info = CONSTELLATION_TYPES[constellation_type]
        st.markdown(f"""
        <div class="constellation-info">
            <strong>Description:</strong> {constellation_info['description']}<br>
            <strong>Primary Agents:</strong> {', '.join(constellation_info['primary_agents'])}<br>
            <strong>Support Agents:</strong> {', '.join(constellation_info['support_agents'])}
        </div>
        """, unsafe_allow_html=True)
        
        # Reset conversation
        if st.button("🔄 Reset Conversation"):
            st.session_state.messages = []
            st.rerun()
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("💬 Learning Conversation")
        
        # Display conversation history
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                if message["role"] == "assistant":
                    st.markdown(f"**{message['agent']}**: {message['content']}")
                else:
                    st.markdown(message["content"])
        
        # Chat input
        if prompt := st.chat_input("Ask me anything about your chosen framework..."):
            # Add user message
            st.session_state.messages.append({"role": "user", "content": prompt})
            
            # Determine which agent should respond (simple logic for demo)
            agent = determine_responding_agent(prompt, constellation_type)
            
            # Generate mock response
            response = generate_mock_response(prompt, agent)
            
            # Add assistant message
            st.session_state.messages.append({
                "role": "assistant", 
                "content": response,
                "agent": agent.replace("_", " ").title()
            })
            
            # Update current agent
            st.session_state.current_agent = agent
            
            st.rerun()
    
    with col2:
        st.header("📊 System Status")
        
        # Current agent
        st.subheader("🤖 Active Agent")
        agent_name = st.session_state.current_agent.replace("_", " ").title()
        st.markdown(f"""
        <div class="agent-card">
            <strong>{agent_name}</strong><br>
            <small>{MOCK_RESPONSES.get(st.session_state.current_agent, {}).get('greeting', 'Ready to help!')}</small>
        </div>
        """, unsafe_allow_html=True)
        
        # Metrics
        st.subheader("📈 Learning Metrics")
        
        col_a, col_b = st.columns(2)
        with col_a:
            st.markdown("""
            <div class="metrics-card">
                <h3>85%</h3>
                <p>Engagement</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col_b:
            st.markdown("""
            <div class="metrics-card">
                <h3>92%</h3>
                <p>Comprehension</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Available agents
        st.subheader("👥 Available Agents")
        constellation_info = CONSTELLATION_TYPES[constellation_type]
        
        for agent in constellation_info["primary_agents"]:
            if agent in MOCK_RESPONSES:
                st.markdown(f"🟢 **{agent.replace('_', ' ').title()}** (Primary)")
        
        for agent in constellation_info["support_agents"]:
            st.markdown(f"🔵 **{agent.replace('_', ' ').title()}** (Support)")

def determine_responding_agent(prompt: str, constellation_type: str) -> str:
    """Determine which agent should respond based on the prompt."""
    prompt_lower = prompt.lower()
    
    # Simple keyword-based routing for demo
    if any(word in prompt_lower for word in ["code", "implement", "example", "build"]):
        return "code_assistant"
    elif any(word in prompt_lower for word in ["practice", "exercise", "tutorial", "hands-on"]):
        return "practice_facilitator"
    elif any(word in prompt_lower for word in ["documentation", "docs", "reference", "api"]):
        return "documentation_expert"
    elif any(word in prompt_lower for word in ["help", "guidance", "stuck", "confused"]):
        return "mentor"
    else:
        return "instructor"

def generate_mock_response(prompt: str, agent: str) -> str:
    """Generate a mock response from the specified agent."""
    if agent not in MOCK_RESPONSES:
        return f"I'm a {agent.replace('_', ' ')} agent. I received your message: {prompt}"
    
    responses = MOCK_RESPONSES[agent]["responses"]
    # Simple response selection based on prompt length
    response_index = len(prompt) % len(responses)
    
    base_response = responses[response_index]
    
    # Add some context based on the prompt
    if "langchain" in prompt.lower():
        base_response += "\n\nThis is particularly relevant for LangChain development."
    elif "langgraph" in prompt.lower():
        base_response += "\n\nThis applies well to LangGraph workflows."
    
    return base_response

if __name__ == "__main__":
    main()
