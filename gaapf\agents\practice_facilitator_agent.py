"""
Practice Facilitator agent implementation for GAAPF.

This agent specializes in creating guided practice exercises, tutorials,
and hands-on learning experiences for Python AI frameworks.
"""

from typing import Dict, List, Optional, Any
import re

from gaapf.agents.base_agent import BaseGAAPFAgent


class PracticeFacilitatorAgent(BaseGAAPFAgent):
    """
    Practice Facilitator agent that creates guided learning experiences.

    This agent specializes in:
    - Creating step-by-step tutorials
    - Designing practice exercises
    - Providing guided hands-on experiences
    - Breaking down complex tasks into manageable steps
    - Offering progressive skill-building activities
    """

    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return (
            f"You are an expert {self.framework} practice facilitator and learning designer. Your role is to "
            "create engaging, hands-on learning experiences through step-by-step tutorials, practice exercises, "
            "and guided activities. You excel at breaking down complex concepts into manageable, progressive "
            f"steps that build skills incrementally. You adapt your exercises to the user's skill level "
            f"({self.user_profile.get('skill_level', 'intermediate')}) and provide clear instructions, "
            "checkpoints, and encouragement throughout the learning process."
        )

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Designing {self.framework} practice exercises and tutorials",
            "Creating step-by-step learning progressions",
            "Breaking down complex tasks into manageable steps",
            "Providing guided hands-on experiences",
            "Adapting difficulty to user skill level",
            "Creating checkpoints and progress markers",
            "Offering constructive feedback and encouragement",
            "Designing progressive skill-building activities"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        user_lower = user_message.lower()

        # Keywords that suggest need for other agents
        theory_keywords = ["explain", "what is", "concept", "theory", "understand", "why"]
        code_keywords = ["debug", "fix", "error", "not working", "problem"]
        docs_keywords = ["documentation", "reference", "api", "official docs"]

        # Check for theoretical explanation needs
        if any(keyword in user_lower for keyword in theory_keywords):
            if not any(practice_word in user_lower for practice_word in ["practice", "exercise", "tutorial", "hands-on"]):
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs theoretical explanation before practice"
                }

        # Check for debugging/troubleshooting needs
        if any(keyword in user_lower for keyword in code_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.8,
                "reason": "User has a problem that needs troubleshooting"
            }

        # Check for documentation needs
        if any(keyword in user_lower for keyword in docs_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "documentation_expert",
                "confidence": 0.6,
                "reason": "User is looking for official documentation"
            }

        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Content is appropriate for practice facilitation"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "practice", "exercise", "tutorial", "hands-on", "step by step",
            "guide me", "walk through", "learn by doing", "try", "build together"
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "example", "demo", "show me how", "getting started", "beginner",
            "project", "activity", "workshop"
        ]

        # Low confidence keywords (better handled by other agents)
        low_confidence_keywords = [
            "explain", "theory", "concept", "documentation", "reference",
            "debug", "error", "fix", "problem", "not working"
        ]

        # Calculate confidence based on keyword presence
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        elif any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        elif any(keyword in message_lower for keyword in low_confidence_keywords):
            return 0.3
        else:
            # Default confidence for general questions
            return 0.5