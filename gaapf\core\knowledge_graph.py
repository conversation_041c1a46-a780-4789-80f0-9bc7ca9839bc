"""
Knowledge Graph Manager for GAAPF.

This module implements the knowledge graph system for concept relationships,
learning path optimization, and knowledge synthesis as specified in the methodology.
"""

import json
import networkx as nx
from typing import Dict, List, Optional, Any, Set, Tuple
from pathlib import Path
from collections import defaultdict, deque
import pickle
import hashlib


class ConceptNode:
    """Represents a concept in the knowledge graph."""
    
    def __init__(
        self,
        concept_id: str,
        name: str,
        framework: str,
        module_id: str = None,
        description: str = "",
        difficulty_level: str = "intermediate",
        prerequisites: List[str] = None,
        learning_objectives: List[str] = None
    ):
        self.concept_id = concept_id
        self.name = name
        self.framework = framework
        self.module_id = module_id
        self.description = description
        self.difficulty_level = difficulty_level
        self.prerequisites = prerequisites or []
        self.learning_objectives = learning_objectives or []
        self.mastery_indicators = []
        self.related_concepts = []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert concept to dictionary representation."""
        return {
            "concept_id": self.concept_id,
            "name": self.name,
            "framework": self.framework,
            "module_id": self.module_id,
            "description": self.description,
            "difficulty_level": self.difficulty_level,
            "prerequisites": self.prerequisites,
            "learning_objectives": self.learning_objectives,
            "mastery_indicators": self.mastery_indicators,
            "related_concepts": self.related_concepts
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConceptNode':
        """Create concept from dictionary representation."""
        concept = cls(
            concept_id=data["concept_id"],
            name=data["name"],
            framework=data["framework"],
            module_id=data.get("module_id"),
            description=data.get("description", ""),
            difficulty_level=data.get("difficulty_level", "intermediate"),
            prerequisites=data.get("prerequisites", []),
            learning_objectives=data.get("learning_objectives", [])
        )
        concept.mastery_indicators = data.get("mastery_indicators", [])
        concept.related_concepts = data.get("related_concepts", [])
        return concept


class RelationshipType:
    """Types of relationships between concepts."""
    
    PREREQUISITE = "prerequisite"
    BUILDS_ON = "builds_on"
    RELATED_TO = "related_to"
    PART_OF = "part_of"
    ENABLES = "enables"
    SIMILAR_TO = "similar_to"
    CONTRASTS_WITH = "contrasts_with"


class KnowledgeGraphManager:
    """
    Manages the knowledge graph for concept relationships and learning paths.
    
    This class provides:
    - Concept relationship modeling
    - Learning path optimization
    - Knowledge gap identification
    - Prerequisite tracking
    - Concept mastery assessment
    """
    
    def __init__(self, data_path: Path = Path("data/knowledge_graph")):
        """
        Initialize the Knowledge Graph Manager.
        
        Args:
            data_path: Path to store knowledge graph data
        """
        self.data_path = data_path
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # NetworkX graph for concept relationships
        self.graph = nx.DiGraph()
        
        # Concept storage
        self.concepts = {}  # concept_id -> ConceptNode
        
        # User progress tracking
        self.user_progress = defaultdict(dict)  # user_id -> concept_id -> progress_data
        
        # Learning paths cache
        self.learning_paths_cache = {}
        
        # Load existing data
        self._load_knowledge_graph()
    
    def add_concept(self, concept: ConceptNode) -> bool:
        """
        Add a concept to the knowledge graph.
        
        Args:
            concept: ConceptNode to add
            
        Returns:
            True if concept was added successfully
        """
        try:
            # Add to concept storage
            self.concepts[concept.concept_id] = concept
            
            # Add to graph
            self.graph.add_node(
                concept.concept_id,
                name=concept.name,
                framework=concept.framework,
                module_id=concept.module_id,
                difficulty_level=concept.difficulty_level,
                description=concept.description
            )
            
            # Add prerequisite relationships
            for prereq_id in concept.prerequisites:
                if prereq_id in self.concepts:
                    self.add_relationship(
                        prereq_id, 
                        concept.concept_id, 
                        RelationshipType.PREREQUISITE
                    )
            
            # Clear cache
            self.learning_paths_cache.clear()
            
            return True
        except Exception as e:
            print(f"Error adding concept {concept.concept_id}: {e}")
            return False
    
    def add_relationship(
        self, 
        from_concept: str, 
        to_concept: str, 
        relationship_type: str,
        weight: float = 1.0,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """
        Add a relationship between two concepts.
        
        Args:
            from_concept: Source concept ID
            to_concept: Target concept ID
            relationship_type: Type of relationship
            weight: Relationship strength (0.0 to 1.0)
            metadata: Additional relationship metadata
            
        Returns:
            True if relationship was added successfully
        """
        try:
            if from_concept not in self.concepts or to_concept not in self.concepts:
                return False
            
            self.graph.add_edge(
                from_concept,
                to_concept,
                relationship_type=relationship_type,
                weight=weight,
                metadata=metadata or {}
            )
            
            # Clear cache
            self.learning_paths_cache.clear()
            
            return True
        except Exception as e:
            print(f"Error adding relationship {from_concept} -> {to_concept}: {e}")
            return False
    
    def get_concept(self, concept_id: str) -> Optional[ConceptNode]:
        """Get a concept by ID."""
        return self.concepts.get(concept_id)
    
    def get_prerequisites(self, concept_id: str) -> List[str]:
        """
        Get all prerequisites for a concept.
        
        Args:
            concept_id: Concept identifier
            
        Returns:
            List of prerequisite concept IDs
        """
        if concept_id not in self.graph:
            return []
        
        prerequisites = []
        for pred in self.graph.predecessors(concept_id):
            edge_data = self.graph[pred][concept_id]
            if edge_data.get("relationship_type") == RelationshipType.PREREQUISITE:
                prerequisites.append(pred)
        
        return prerequisites
    
    def get_dependent_concepts(self, concept_id: str) -> List[str]:
        """
        Get concepts that depend on this concept.
        
        Args:
            concept_id: Concept identifier
            
        Returns:
            List of dependent concept IDs
        """
        if concept_id not in self.graph:
            return []
        
        dependents = []
        for succ in self.graph.successors(concept_id):
            edge_data = self.graph[concept_id][succ]
            if edge_data.get("relationship_type") == RelationshipType.PREREQUISITE:
                dependents.append(succ)
        
        return dependents
    
    def find_learning_path(
        self, 
        start_concepts: List[str], 
        target_concept: str,
        user_id: str = None
    ) -> List[str]:
        """
        Find optimal learning path from start concepts to target concept.
        
        Args:
            start_concepts: List of concepts the user already knows
            target_concept: Target concept to learn
            user_id: Optional user ID for personalized paths
            
        Returns:
            Ordered list of concepts to learn
        """
        # Create cache key
        cache_key = hashlib.md5(
            f"{sorted(start_concepts)}_{target_concept}_{user_id}".encode()
        ).hexdigest()
        
        if cache_key in self.learning_paths_cache:
            return self.learning_paths_cache[cache_key]
        
        if target_concept not in self.graph:
            return []
        
        # Find all prerequisites for the target concept
        all_prerequisites = self._get_all_prerequisites(target_concept)
        
        # Remove concepts the user already knows
        missing_prerequisites = [
            concept for concept in all_prerequisites 
            if concept not in start_concepts
        ]
        
        # Create subgraph with missing prerequisites
        if missing_prerequisites:
            subgraph = self.graph.subgraph(missing_prerequisites + [target_concept])
            
            # Find topological order
            try:
                learning_path = list(nx.topological_sort(subgraph))
                # Remove target concept from path (it's the goal)
                if target_concept in learning_path:
                    learning_path.remove(target_concept)
            except nx.NetworkXError:
                # Handle cycles by using approximate topological sort
                learning_path = self._approximate_topological_sort(missing_prerequisites)
        else:
            learning_path = []
        
        # Add target concept at the end
        learning_path.append(target_concept)
        
        # Cache the result
        self.learning_paths_cache[cache_key] = learning_path
        
        return learning_path
    
    def _get_all_prerequisites(self, concept_id: str) -> List[str]:
        """Get all prerequisites recursively."""
        visited = set()
        prerequisites = []
        
        def dfs(current_concept):
            if current_concept in visited:
                return
            visited.add(current_concept)
            
            for prereq in self.get_prerequisites(current_concept):
                dfs(prereq)
                if prereq not in prerequisites:
                    prerequisites.append(prereq)
        
        dfs(concept_id)
        return prerequisites
    
    def _approximate_topological_sort(self, concepts: List[str]) -> List[str]:
        """Approximate topological sort for graphs with cycles."""
        # Use Kahn's algorithm with cycle detection
        in_degree = defaultdict(int)
        graph_subset = {}
        
        # Build subset graph
        for concept in concepts:
            if concept in self.graph:
                graph_subset[concept] = []
                for succ in self.graph.successors(concept):
                    if succ in concepts:
                        graph_subset[concept].append(succ)
                        in_degree[succ] += 1
        
        # Find nodes with no incoming edges
        queue = deque([concept for concept in concepts if in_degree[concept] == 0])
        result = []
        
        while queue:
            current = queue.popleft()
            result.append(current)
            
            for neighbor in graph_subset.get(current, []):
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        return result

    def identify_knowledge_gaps(self, user_id: str, target_concepts: List[str]) -> Dict[str, Any]:
        """
        Identify knowledge gaps for a user based on target concepts.

        Args:
            user_id: User identifier
            target_concepts: List of concepts the user wants to learn

        Returns:
            Dictionary with gap analysis
        """
        user_knowledge = self.get_user_knowledge(user_id)
        mastered_concepts = [
            concept_id for concept_id, progress in user_knowledge.items()
            if progress.get("mastery_level", 0) >= 0.8
        ]

        gaps = {
            "missing_prerequisites": {},
            "recommended_learning_paths": {},
            "difficulty_analysis": {},
            "estimated_learning_time": {}
        }

        for target_concept in target_concepts:
            if target_concept not in self.concepts:
                continue

            # Find learning path
            learning_path = self.find_learning_path(mastered_concepts, target_concept, user_id)

            # Identify missing prerequisites
            missing = [concept for concept in learning_path if concept not in mastered_concepts]
            gaps["missing_prerequisites"][target_concept] = missing
            gaps["recommended_learning_paths"][target_concept] = learning_path

            # Analyze difficulty progression
            difficulty_progression = []
            for concept_id in learning_path:
                concept = self.concepts.get(concept_id)
                if concept:
                    difficulty_progression.append({
                        "concept_id": concept_id,
                        "name": concept.name,
                        "difficulty": concept.difficulty_level
                    })
            gaps["difficulty_analysis"][target_concept] = difficulty_progression

            # Estimate learning time (simplified)
            estimated_time = len(missing) * 2  # 2 hours per concept (rough estimate)
            gaps["estimated_learning_time"][target_concept] = estimated_time

        return gaps

    def get_user_knowledge(self, user_id: str) -> Dict[str, Dict[str, Any]]:
        """Get user's knowledge state."""
        return self.user_progress.get(user_id, {})

    def update_user_progress(
        self,
        user_id: str,
        concept_id: str,
        mastery_level: float,
        assessment_data: Dict[str, Any] = None
    ) -> bool:
        """
        Update user's progress on a concept.

        Args:
            user_id: User identifier
            concept_id: Concept identifier
            mastery_level: Mastery level (0.0 to 1.0)
            assessment_data: Additional assessment information

        Returns:
            True if update was successful
        """
        try:
            if concept_id not in self.concepts:
                return False

            self.user_progress[user_id][concept_id] = {
                "mastery_level": mastery_level,
                "last_updated": json.dumps({"timestamp": "now"}),  # Simplified
                "assessment_data": assessment_data or {}
            }

            return True
        except Exception as e:
            print(f"Error updating user progress: {e}")
            return False

    def get_related_concepts(
        self,
        concept_id: str,
        relationship_types: List[str] = None,
        max_distance: int = 2
    ) -> List[Dict[str, Any]]:
        """
        Get concepts related to the given concept.

        Args:
            concept_id: Source concept ID
            relationship_types: Types of relationships to consider
            max_distance: Maximum distance in the graph

        Returns:
            List of related concepts with relationship information
        """
        if concept_id not in self.graph:
            return []

        if relationship_types is None:
            relationship_types = [
                RelationshipType.RELATED_TO,
                RelationshipType.SIMILAR_TO,
                RelationshipType.BUILDS_ON
            ]

        related = []
        visited = set()

        def explore(current_concept, distance):
            if distance > max_distance or current_concept in visited:
                return
            visited.add(current_concept)

            # Explore outgoing edges
            for neighbor in self.graph.successors(current_concept):
                edge_data = self.graph[current_concept][neighbor]
                rel_type = edge_data.get("relationship_type")

                if rel_type in relationship_types:
                    concept = self.concepts.get(neighbor)
                    if concept:
                        related.append({
                            "concept": concept.to_dict(),
                            "relationship_type": rel_type,
                            "distance": distance + 1,
                            "weight": edge_data.get("weight", 1.0)
                        })

                    if distance < max_distance:
                        explore(neighbor, distance + 1)

            # Explore incoming edges
            for neighbor in self.graph.predecessors(current_concept):
                edge_data = self.graph[neighbor][current_concept]
                rel_type = edge_data.get("relationship_type")

                if rel_type in relationship_types:
                    concept = self.concepts.get(neighbor)
                    if concept:
                        related.append({
                            "concept": concept.to_dict(),
                            "relationship_type": rel_type,
                            "distance": distance + 1,
                            "weight": edge_data.get("weight", 1.0)
                        })

                    if distance < max_distance:
                        explore(neighbor, distance + 1)

        explore(concept_id, 0)

        # Remove duplicates and sort by relevance
        unique_related = {}
        for item in related:
            concept_id_key = item["concept"]["concept_id"]
            if concept_id_key not in unique_related:
                unique_related[concept_id_key] = item
            elif item["distance"] < unique_related[concept_id_key]["distance"]:
                unique_related[concept_id_key] = item

        # Sort by weight and distance
        sorted_related = sorted(
            unique_related.values(),
            key=lambda x: (x["distance"], -x["weight"])
        )

        return sorted_related

    def suggest_next_concepts(self, user_id: str, framework: str = None) -> List[Dict[str, Any]]:
        """
        Suggest next concepts for a user to learn.

        Args:
            user_id: User identifier
            framework: Optional framework filter

        Returns:
            List of suggested concepts with reasoning
        """
        user_knowledge = self.get_user_knowledge(user_id)
        mastered_concepts = [
            concept_id for concept_id, progress in user_knowledge.items()
            if progress.get("mastery_level", 0) >= 0.8
        ]

        suggestions = []

        # Find concepts that are ready to learn (prerequisites met)
        for concept_id, concept in self.concepts.items():
            if framework and concept.framework != framework:
                continue

            if concept_id in mastered_concepts:
                continue

            # Check if prerequisites are met
            prerequisites = self.get_prerequisites(concept_id)
            prerequisites_met = all(
                prereq in mastered_concepts for prereq in prerequisites
            )

            if prerequisites_met:
                # Calculate suggestion score
                score = self._calculate_suggestion_score(
                    concept_id, user_knowledge, mastered_concepts
                )

                suggestions.append({
                    "concept": concept.to_dict(),
                    "score": score,
                    "reasoning": self._generate_suggestion_reasoning(
                        concept_id, prerequisites, mastered_concepts
                    )
                })

        # Sort by score
        suggestions.sort(key=lambda x: x["score"], reverse=True)

        return suggestions[:10]  # Return top 10 suggestions

    def _calculate_suggestion_score(
        self,
        concept_id: str,
        user_knowledge: Dict[str, Dict],
        mastered_concepts: List[str]
    ) -> float:
        """Calculate suggestion score for a concept."""
        concept = self.concepts[concept_id]
        score = 0.0

        # Base score based on difficulty
        difficulty_scores = {
            "beginner": 1.0,
            "intermediate": 0.8,
            "advanced": 0.6
        }
        score += difficulty_scores.get(concept.difficulty_level, 0.7)

        # Boost score if user has partial knowledge
        if concept_id in user_knowledge:
            partial_mastery = user_knowledge[concept_id].get("mastery_level", 0)
            score += partial_mastery * 0.5

        # Boost score based on how many dependent concepts it unlocks
        dependents = self.get_dependent_concepts(concept_id)
        score += len(dependents) * 0.1

        return score

    def _generate_suggestion_reasoning(
        self,
        concept_id: str,
        prerequisites: List[str],
        mastered_concepts: List[str]
    ) -> str:
        """Generate reasoning for why a concept is suggested."""
        concept = self.concepts[concept_id]

        if not prerequisites:
            return f"Ready to learn {concept.name} - no prerequisites required"

        return f"Ready to learn {concept.name} - all prerequisites mastered"

    def _save_knowledge_graph(self):
        """Save knowledge graph to disk."""
        try:
            # Save concepts
            concepts_file = self.data_path / "concepts.json"
            with open(concepts_file, 'w') as f:
                concepts_data = {
                    concept_id: concept.to_dict()
                    for concept_id, concept in self.concepts.items()
                }
                json.dump(concepts_data, f, indent=2)

            # Save graph
            graph_file = self.data_path / "graph.pickle"
            with open(graph_file, 'wb') as f:
                pickle.dump(self.graph, f)

            # Save user progress
            progress_file = self.data_path / "user_progress.json"
            with open(progress_file, 'w') as f:
                json.dump(dict(self.user_progress), f, indent=2)

        except Exception as e:
            print(f"Error saving knowledge graph: {e}")

    def _load_knowledge_graph(self):
        """Load knowledge graph from disk."""
        try:
            # Load concepts
            concepts_file = self.data_path / "concepts.json"
            if concepts_file.exists():
                with open(concepts_file, 'r') as f:
                    concepts_data = json.load(f)
                    for concept_id, concept_dict in concepts_data.items():
                        concept = ConceptNode.from_dict(concept_dict)
                        self.concepts[concept_id] = concept

            # Load graph
            graph_file = self.data_path / "graph.pickle"
            if graph_file.exists():
                with open(graph_file, 'rb') as f:
                    self.graph = pickle.load(f)

            # Load user progress
            progress_file = self.data_path / "user_progress.json"
            if progress_file.exists():
                with open(progress_file, 'r') as f:
                    progress_data = json.load(f)
                    self.user_progress = defaultdict(dict, progress_data)

        except Exception as e:
            print(f"Error loading knowledge graph: {e}")

    def save(self):
        """Public method to save knowledge graph."""
        self._save_knowledge_graph()

    def get_graph_statistics(self) -> Dict[str, Any]:
        """Get statistics about the knowledge graph."""
        return {
            "total_concepts": len(self.concepts),
            "total_relationships": self.graph.number_of_edges(),
            "frameworks": list(set(concept.framework for concept in self.concepts.values())),
            "difficulty_distribution": {
                level: sum(1 for concept in self.concepts.values() if concept.difficulty_level == level)
                for level in ["beginner", "intermediate", "advanced"]
            },
            "average_prerequisites": sum(len(self.get_prerequisites(cid)) for cid in self.concepts) / len(self.concepts) if self.concepts else 0,
            "users_tracked": len(self.user_progress)
        }
