"""
Documentation Expert agent implementation for GAAPF.

This agent specializes in finding, interpreting, and explaining official
documentation for Python AI frameworks.
"""

from typing import Dict, List, Optional, Any
import re

from gaapf.agents.base_agent import BaseGAAPFAgent


class DocumentationExpertAgent(BaseGAAPFAgent):
    """
    Documentation Expert agent that provides accurate framework documentation.

    This agent specializes in:
    - Finding and interpreting official documentation
    - Explaining API references and function signatures
    - Providing accurate parameter and usage information
    - Linking to relevant documentation sources
    - Clarifying version-specific differences
    """

    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return (
            f"You are an expert {self.framework} documentation specialist. Your role is to provide "
            "accurate, up-to-date information from official documentation sources. You excel at "
            "finding relevant documentation, explaining API references, function signatures, and "
            "usage patterns. You always cite official sources and provide links when possible. "
            f"You're familiar with {self.framework} documentation structure and can quickly locate "
            "specific information about classes, methods, parameters, and configuration options. "
            "Always verify information against official sources and mention version compatibility."
        )

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Expert knowledge of {self.framework} official documentation",
            "Finding and interpreting API references",
            "Explaining function signatures and parameters",
            "Providing accurate usage examples from docs",
            "Identifying version-specific differences",
            "Linking to relevant documentation sources",
            "Clarifying configuration options and settings",
            "Understanding documentation structure and organization"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        user_lower = user_message.lower()
        content_lower = content.lower()

        # Keywords that suggest need for other agents
        code_keywords = ["implement", "write code", "build", "create", "example code"]
        theory_keywords = ["explain concept", "why", "how does it work", "theory", "understand"]
        practice_keywords = ["practice", "exercise", "tutorial", "hands-on", "guide me"]
        troubleshoot_keywords = ["error", "not working", "bug", "issue", "problem", "debug"]

        # Check for code implementation requests
        if any(keyword in user_lower for keyword in code_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "code_assistant",
                "confidence": 0.8,
                "reason": "User wants code implementation, not just documentation"
            }

        # Check for theoretical explanation needs
        if any(keyword in user_lower for keyword in theory_keywords):
            if not any(doc_word in user_lower for doc_word in ["docs", "documentation", "reference", "api"]):
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs conceptual explanation rather than documentation"
                }

        # Check for practice/tutorial needs
        if any(keyword in user_lower for keyword in practice_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "practice_facilitator",
                "confidence": 0.8,
                "reason": "User wants guided practice or tutorials"
            }

        # Check for troubleshooting needs
        if any(keyword in user_lower for keyword in troubleshoot_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "troubleshooter",
                "confidence": 0.9,
                "reason": "User has a problem that needs troubleshooting"
            }

        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Content is appropriate for documentation assistance"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "documentation", "docs", "reference", "api", "official", "parameters",
            "function signature", "method", "class", "module", "library", "version"
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "usage", "syntax", "configuration", "settings", "options", "arguments",
            "import", "installation", "setup"
        ]

        # Low confidence keywords (better handled by other agents)
        low_confidence_keywords = [
            "implement", "code", "build", "create", "example", "tutorial",
            "practice", "exercise", "explain concept", "theory", "why"
        ]

        # Calculate confidence based on keyword presence
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        elif any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        elif any(keyword in message_lower for keyword in low_confidence_keywords):
            return 0.3
        else:
            # Default confidence for general questions
            return 0.5