"""
File tools for GAAPF.

This module implements file tools for code generation, execution, and project management
as specified in the methodology for hands-on learning experiences.
"""

import os
import subprocess
import tempfile
import json
import shutil
from typing import Dict, List, Optional, Any
from pathlib import Path

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


class CodeGenerationInput(BaseModel):
    """Input schema for code generation tool."""
    framework: str = Field(description="Target framework (e.g., 'langchain', 'langgraph')")
    code_type: str = Field(description="Type of code to generate (e.g., 'basic_example', 'agent', 'chain')")
    description: str = Field(description="Description of what the code should do")
    complexity: str = Field(default="beginner", description="Complexity level: beginner, intermediate, advanced")


class CodeExecutionInput(BaseModel):
    """Input schema for code execution tool."""
    code: str = Field(description="Python code to execute")
    framework: str = Field(description="Framework context for execution")
    timeout: int = Field(default=30, description="Execution timeout in seconds")


class ProjectStructureInput(BaseModel):
    """Input schema for project structure creation tool."""
    project_name: str = Field(description="Name of the project")
    framework: str = Field(description="Target framework")
    project_type: str = Field(description="Type of project (e.g., 'basic_app', 'agent_system', 'chatbot')")


class CodeGenerationTool(BaseTool):
    """Tool for generating framework-specific code examples."""

    name: str = "generate_code"
    description: str = """Generate framework-specific code examples and templates.
    This tool creates practical code examples based on the framework, complexity level, and requirements.
    Use this when users need concrete code examples to learn from or build upon."""
    args_schema: type = CodeGenerationInput

    def _run(self, framework: str, code_type: str, description: str, complexity: str = "beginner") -> str:
        """Generate code based on framework and requirements."""
        try:
            # Code templates for different frameworks and types
            templates = self._get_code_templates()
            
            framework_key = framework.lower()
            if framework_key not in templates:
                return f"Code generation not supported for framework: {framework}"
            
            if code_type not in templates[framework_key]:
                available_types = list(templates[framework_key].keys())
                return f"Code type '{code_type}' not available for {framework}. Available types: {available_types}"
            
            # Get the appropriate template
            template = templates[framework_key][code_type][complexity]
            
            # Customize the template based on description
            customized_code = self._customize_template(template, description, framework)
            
            result = {
                "framework": framework,
                "code_type": code_type,
                "complexity": complexity,
                "description": description,
                "generated_code": customized_code,
                "instructions": self._get_usage_instructions(framework, code_type)
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error generating code: {str(e)}"

    def _get_code_templates(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """Get code templates for different frameworks."""
        return {
            "langchain": {
                "basic_example": {
                    "beginner": '''from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Initialize the LLM
llm = OpenAI(temperature=0.7)

# Create a prompt template
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Explain {topic} in simple terms:"
)

# Create a chain
chain = LLMChain(llm=llm, prompt=prompt)

# Run the chain
result = chain.run(topic="machine learning")
print(result)''',
                    "intermediate": '''from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain, SequentialChain
from langchain.memory import ConversationBufferMemory

# Initialize components
llm = OpenAI(temperature=0.7)
memory = ConversationBufferMemory()

# Create multiple chains
first_chain = LLMChain(
    llm=llm,
    prompt=PromptTemplate(
        input_variables=["topic"],
        template="Generate 3 key points about {topic}:"
    ),
    output_key="key_points"
)

second_chain = LLMChain(
    llm=llm,
    prompt=PromptTemplate(
        input_variables=["key_points"],
        template="Create a summary based on these points: {key_points}"
    ),
    output_key="summary"
)

# Create sequential chain
overall_chain = SequentialChain(
    chains=[first_chain, second_chain],
    input_variables=["topic"],
    output_variables=["key_points", "summary"]
)

# Execute
result = overall_chain({"topic": "artificial intelligence"})
print(result)''',
                    "advanced": '''from langchain.llms import OpenAI
from langchain.agents import initialize_agent, Tool, AgentType
from langchain.memory import ConversationBufferWindowMemory
from langchain.tools import DuckDuckGoSearchRun

# Initialize LLM and tools
llm = OpenAI(temperature=0)
search = DuckDuckGoSearchRun()

# Define custom tools
def calculator(expression):
    """Calculate mathematical expressions"""
    try:
        return str(eval(expression))
    except:
        return "Invalid expression"

tools = [
    Tool(
        name="Search",
        func=search.run,
        description="Search for current information"
    ),
    Tool(
        name="Calculator",
        func=calculator,
        description="Calculate mathematical expressions"
    )
]

# Initialize agent with memory
memory = ConversationBufferWindowMemory(k=5)
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
    memory=memory,
    verbose=True
)

# Run agent
response = agent.run("What's the current population of Tokyo and calculate 10% of that number?")
print(response)'''
                },
                "agent": {
                    "beginner": '''from langchain.agents import initialize_agent, Tool, AgentType
from langchain.llms import OpenAI

# Initialize LLM
llm = OpenAI(temperature=0)

# Define a simple tool
def get_current_time():
    """Get the current time"""
    from datetime import datetime
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

tools = [
    Tool(
        name="CurrentTime",
        func=get_current_time,
        description="Get the current date and time"
    )
]

# Create agent
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
    verbose=True
)

# Use the agent
response = agent.run("What time is it?")
print(response)''',
                    "intermediate": '''from langchain.agents import initialize_agent, Tool, AgentType
from langchain.llms import OpenAI
from langchain.memory import ConversationBufferMemory
import requests

# Initialize LLM and memory
llm = OpenAI(temperature=0)
memory = ConversationBufferMemory()

# Define multiple tools
def weather_tool(city):
    """Get weather information for a city"""
    # This is a mock implementation
    return f"The weather in {city} is sunny with 22°C"

def news_tool(topic):
    """Get news about a topic"""
    # This is a mock implementation
    return f"Latest news about {topic}: Technology advances continue..."

tools = [
    Tool(
        name="Weather",
        func=weather_tool,
        description="Get weather information for a city"
    ),
    Tool(
        name="News",
        func=news_tool,
        description="Get news about a specific topic"
    )
]

# Create conversational agent
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
    memory=memory,
    verbose=True
)

# Multi-turn conversation
print(agent.run("What's the weather like in Paris?"))
print(agent.run("What about the latest tech news?"))''',
                    "advanced": '''from langchain.agents import initialize_agent, Tool, AgentType
from langchain.llms import OpenAI
from langchain.memory import ConversationSummaryBufferMemory
from langchain.callbacks import get_openai_callback
import asyncio

class AdvancedAgent:
    def __init__(self):
        self.llm = OpenAI(temperature=0)
        self.memory = ConversationSummaryBufferMemory(
            llm=self.llm,
            max_token_limit=1000
        )
        self.tools = self._create_tools()
        self.agent = initialize_agent(
            tools=self.tools,
            llm=self.llm,
            agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
            memory=self.memory,
            verbose=True,
            max_iterations=5
        )
    
    def _create_tools(self):
        def research_tool(query):
            """Research information about a topic"""
            # Mock research implementation
            return f"Research results for '{query}': Comprehensive analysis available..."
        
        def analysis_tool(data):
            """Analyze data and provide insights"""
            return f"Analysis of '{data}': Key insights and patterns identified..."
        
        return [
            Tool(name="Research", func=research_tool, description="Research topics"),
            Tool(name="Analysis", func=analysis_tool, description="Analyze data")
        ]
    
    async def run_async(self, query):
        """Run agent asynchronously"""
        with get_openai_callback() as cb:
            result = self.agent.run(query)
            return {
                "result": result,
                "tokens_used": cb.total_tokens,
                "cost": cb.total_cost
            }

# Usage
agent = AdvancedAgent()
result = asyncio.run(agent.run_async("Research AI trends and analyze the findings"))
print(result)'''
                }
            },
            "langgraph": {
                "basic_example": {
                    "beginner": '''from langgraph.graph import StateGraph, END
from typing import TypedDict

# Define state
class State(TypedDict):
    messages: list
    current_step: str

# Define nodes
def start_node(state: State):
    return {
        "messages": state["messages"] + ["Starting process"],
        "current_step": "started"
    }

def process_node(state: State):
    return {
        "messages": state["messages"] + ["Processing data"],
        "current_step": "processing"
    }

def end_node(state: State):
    return {
        "messages": state["messages"] + ["Process complete"],
        "current_step": "completed"
    }

# Create graph
workflow = StateGraph(State)
workflow.add_node("start", start_node)
workflow.add_node("process", process_node)
workflow.add_node("end", end_node)

# Add edges
workflow.add_edge("start", "process")
workflow.add_edge("process", "end")
workflow.add_edge("end", END)

# Set entry point
workflow.set_entry_point("start")

# Compile and run
app = workflow.compile()
result = app.invoke({"messages": [], "current_step": "init"})
print(result)''',
                    "intermediate": '''from langgraph.graph import StateGraph, END
from typing import TypedDict, Literal

class State(TypedDict):
    messages: list
    user_input: str
    analysis_result: str
    decision: str

def analyze_input(state: State):
    """Analyze user input"""
    user_input = state["user_input"]
    # Simple analysis logic
    if "question" in user_input.lower():
        analysis = "question_detected"
    elif "help" in user_input.lower():
        analysis = "help_requested"
    else:
        analysis = "general_input"
    
    return {
        **state,
        "analysis_result": analysis,
        "messages": state["messages"] + [f"Analyzed: {analysis}"]
    }

def make_decision(state: State):
    """Make decision based on analysis"""
    analysis = state["analysis_result"]
    if analysis == "question_detected":
        decision = "answer_question"
    elif analysis == "help_requested":
        decision = "provide_help"
    else:
        decision = "general_response"
    
    return {
        **state,
        "decision": decision,
        "messages": state["messages"] + [f"Decision: {decision}"]
    }

def route_decision(state: State) -> Literal["answer", "help", "general"]:
    """Route based on decision"""
    decision = state["decision"]
    if decision == "answer_question":
        return "answer"
    elif decision == "provide_help":
        return "help"
    else:
        return "general"

# Create workflow
workflow = StateGraph(State)
workflow.add_node("analyze", analyze_input)
workflow.add_node("decide", make_decision)
workflow.add_node("answer", lambda s: {**s, "messages": s["messages"] + ["Providing answer"]})
workflow.add_node("help", lambda s: {**s, "messages": s["messages"] + ["Providing help"]})
workflow.add_node("general", lambda s: {**s, "messages": s["messages"] + ["General response"]})

# Add edges
workflow.add_edge("analyze", "decide")
workflow.add_conditional_edges("decide", route_decision)
workflow.add_edge("answer", END)
workflow.add_edge("help", END)
workflow.add_edge("general", END)

workflow.set_entry_point("analyze")

# Run
app = workflow.compile()
result = app.invoke({
    "messages": [],
    "user_input": "I have a question about Python",
    "analysis_result": "",
    "decision": ""
})
print(result)''',
                    "advanced": '''from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from typing import TypedDict, Annotated, Literal
import operator

class AgentState(TypedDict):
    messages: Annotated[list, operator.add]
    current_agent: str
    task_queue: list
    results: dict
    iteration: int

class MultiAgentSystem:
    def __init__(self):
        self.memory = MemorySaver()
        self.workflow = self._create_workflow()
        
    def _create_workflow(self):
        workflow = StateGraph(AgentState)
        
        # Add agent nodes
        workflow.add_node("coordinator", self.coordinator_agent)
        workflow.add_node("researcher", self.researcher_agent)
        workflow.add_node("analyzer", self.analyzer_agent)
        workflow.add_node("synthesizer", self.synthesizer_agent)
        
        # Add routing logic
        workflow.add_conditional_edges(
            "coordinator",
            self.route_to_agent,
            {
                "research": "researcher",
                "analyze": "analyzer",
                "synthesize": "synthesizer",
                "end": END
            }
        )
        
        workflow.add_edge("researcher", "coordinator")
        workflow.add_edge("analyzer", "coordinator")
        workflow.add_edge("synthesizer", "coordinator")
        
        workflow.set_entry_point("coordinator")
        return workflow.compile(checkpointer=self.memory)
    
    def coordinator_agent(self, state: AgentState):
        """Coordinate the multi-agent workflow"""
        if not state["task_queue"]:
            return {
                **state,
                "messages": ["All tasks completed"],
                "current_agent": "coordinator"
            }
        
        next_task = state["task_queue"][0]
        remaining_tasks = state["task_queue"][1:]
        
        return {
            **state,
            "messages": [f"Coordinating task: {next_task}"],
            "task_queue": remaining_tasks,
            "current_agent": "coordinator"
        }
    
    def researcher_agent(self, state: AgentState):
        """Research agent implementation"""
        return {
            **state,
            "messages": ["Research completed"],
            "results": {**state["results"], "research": "Research data collected"},
            "current_agent": "researcher"
        }
    
    def analyzer_agent(self, state: AgentState):
        """Analyzer agent implementation"""
        return {
            **state,
            "messages": ["Analysis completed"],
            "results": {**state["results"], "analysis": "Data analyzed"},
            "current_agent": "analyzer"
        }
    
    def synthesizer_agent(self, state: AgentState):
        """Synthesizer agent implementation"""
        return {
            **state,
            "messages": ["Synthesis completed"],
            "results": {**state["results"], "synthesis": "Results synthesized"},
            "current_agent": "synthesizer"
        }
    
    def route_to_agent(self, state: AgentState) -> Literal["research", "analyze", "synthesize", "end"]:
        """Route to appropriate agent"""
        if not state["task_queue"]:
            return "end"
        
        task = state["task_queue"][0]
        if "research" in task:
            return "research"
        elif "analyze" in task:
            return "analyze"
        elif "synthesize" in task:
            return "synthesize"
        else:
            return "end"

# Usage
system = MultiAgentSystem()
initial_state = {
    "messages": [],
    "current_agent": "",
    "task_queue": ["research topic", "analyze data", "synthesize results"],
    "results": {},
    "iteration": 0
}

config = {"configurable": {"thread_id": "example"}}
result = system.workflow.invoke(initial_state, config)
print(result)'''
                }
            }
        }

    def _customize_template(self, template: str, description: str, framework: str) -> str:
        """Customize template based on user description."""
        # Simple customization - in a real implementation, this could use LLM
        customized = template
        
        # Add description as a comment
        customized = f'"""\n{description}\n\nGenerated for {framework} framework\n"""\n\n' + customized
        
        return customized

    def _get_usage_instructions(self, framework: str, code_type: str) -> str:
        """Get usage instructions for the generated code."""
        instructions = {
            "langchain": {
                "basic_example": "Install: pip install langchain openai. Set OPENAI_API_KEY environment variable.",
                "agent": "Install: pip install langchain openai. Requires API key configuration.",
            },
            "langgraph": {
                "basic_example": "Install: pip install langgraph. No API key required for basic examples.",
            }
        }
        
        return instructions.get(framework, {}).get(code_type, "Follow framework installation instructions.")


class CodeExecutionTool(BaseTool):
    """Tool for safe code execution in isolated environment."""

    name: str = "execute_code"
    description: str = """Execute Python code in a safe, isolated environment.
    Use this to test code examples, verify functionality, or demonstrate code execution.
    The execution is sandboxed for safety."""
    args_schema: type = CodeExecutionInput

    def _run(self, code: str, framework: str, timeout: int = 30) -> str:
        """Execute code safely."""
        try:
            # Create temporary directory for execution
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_file = Path(temp_dir) / "code_to_execute.py"
                
                # Write code to temporary file
                with open(temp_file, 'w') as f:
                    f.write(code)
                
                # Execute with timeout
                result = subprocess.run(
                    ["python", str(temp_file)],
                    capture_output=True,
                    text=True,
                    timeout=timeout,
                    cwd=temp_dir
                )
                
                execution_result = {
                    "framework": framework,
                    "exit_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "execution_time": "< 30s",
                    "status": "success" if result.returncode == 0 else "error"
                }
                
                return json.dumps(execution_result, indent=2)
                
        except subprocess.TimeoutExpired:
            return json.dumps({
                "framework": framework,
                "status": "timeout",
                "error": f"Code execution timed out after {timeout} seconds"
            }, indent=2)
        except Exception as e:
            return json.dumps({
                "framework": framework,
                "status": "error",
                "error": f"Execution error: {str(e)}"
            }, indent=2)


def create_file_tools() -> List[BaseTool]:
    """Create all file-related tools."""
    return [
        CodeGenerationTool(),
        CodeExecutionTool()
    ]
