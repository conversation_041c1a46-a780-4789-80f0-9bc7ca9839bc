"""
CLI application for GAAPF.

This module implements a command-line interface for interacting
with the GAAPF system.
"""

import asyncio
import os
import sys
import json
import uuid
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

import questionary
from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.tree import Tree
from rich.layout import Layout
from rich.live import Live
from rich.align import Align
from rich.columns import Columns

# Import GAAPF components
from gaapf.core.learning_hub import LearningHubCore
from gaapf.config.framework_configs import SupportedFrameworks
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from gaapf.config.env_config import get_config


class GAAPFCli:
    """
    Command-line interface for GAAPF.
    
    Provides a text-based interface for interacting with the GAAPF system.
    """
    
    def __init__(self, data_path: Path = Path("data")):
        """
        Initialize the CLI application.
        
        Args:
            data_path: Path to store data
        """
        self.console = Console()
        self.data_path = data_path
        self.learning_hub = None
        self.current_user_id = None
        self.current_session_id = None
        self.session_history = []
        self.analytics_enabled = True
        
    async def initialize(self):
        """Initialize the CLI application."""
        # Print welcome message
        self.console.print(Panel.fit(
            "Welcome to GAAPF - Guidance AI Agent for Python Framework",
            title="GAAPF CLI",
            border_style="blue"
        ))

        # Load and validate configuration
        config = get_config()
        config.print_configuration_status()

        validation = config.validate_configuration()
        if not validation["valid"]:
            self.console.print("\n❌ Configuration errors found. Please check your .env file.")
            self.console.print("Copy .env.example to .env and configure your API keys.")
            return False

        # Initialize the learning hub
        self.learning_hub = LearningHubCore(data_path=config.data_path)
        await self.learning_hub.initialize()

        # Initialize LLM using configuration
        try:
            self.llm = config.create_llm()
            # Determine which provider was actually used
            available_providers = config.get_available_llm_providers()
            if available_providers.get(config.default_llm_provider, False):
                actual_provider = config.default_llm_provider
            else:
                # Find the first available provider
                actual_provider = next((p for p, available in available_providers.items() if available), "unknown")

            self.console.print(f"✅ Using {actual_provider} LLM provider")
        except Exception as e:
            self.console.print(f"❌ Error creating LLM: {e}")
            return False

        self.learning_hub.llm = self.llm
        return True
        
    async def run(self):
        """Run the CLI application."""
        if not await self.initialize():
            return

        # Main menu loop
        while True:
            action = await self._show_main_menu()
            
            if action == "login":
                await self._login()
            elif action == "register":
                await self._register()
            elif action == "start_session":
                await self._start_session()
            elif action == "continue_session":
                await self._continue_session()
            elif action == "view_progress":
                await self._view_progress()
            elif action == "recommendations":
                await self._show_recommendations()
            elif action == "analytics":
                await self._show_analytics_dashboard()
            elif action == "goals":
                await self._manage_goals()
            elif action == "export":
                await self._export_data()
            elif action == "settings":
                await self._show_settings()
            elif action == "exit":
                self.console.print("Thank you for using GAAPF. Goodbye!")
                break
                
    async def _show_main_menu(self) -> str:
        """
        Show the main menu.
        
        Returns:
            Selected action
        """
        self.console.print("\n")
        
        # Show different options based on login state
        if self.current_user_id:
            result = await questionary.select(
                "What would you like to do?",
                choices=[
                    "Start a new learning session",
                    "Continue a previous session",
                    "View learning progress",
                    "Get learning recommendations",
                    "View analytics dashboard",
                    "Manage learning goals",
                    "Export learning data",
                    "Settings",
                    "Logout",
                    "Exit"
                ]
            ).ask_async()

            action_map = {
                "Start a new learning session": "start_session",
                "Continue a previous session": "continue_session",
                "View learning progress": "view_progress",
                "Get learning recommendations": "recommendations",
                "View analytics dashboard": "analytics",
                "Manage learning goals": "goals",
                "Export learning data": "export",
                "Settings": "settings",
                "Logout": "logout",
                "Exit": "exit"
            }
            
            if result == "Logout":
                self.current_user_id = None
                self.console.print("Logged out successfully.")
                return await self._show_main_menu()
                
            return action_map.get(result, "exit")
        else:
            result = await questionary.select(
                "What would you like to do?",
                choices=[
                    "Login",
                    "Register",
                    "Exit"
                ]
            ).ask_async()
            
            action_map = {
                "Login": "login",
                "Register": "register",
                "Exit": "exit"
            }
            
            return action_map.get(result, "exit")
            
    async def _login(self):
        """Handle user login."""
        self.console.print("\n[bold]User Login[/bold]")
        
        # Get user profiles
        profiles_path = self.data_path / "user_profiles"
        if not profiles_path.exists():
            self.console.print("No users found. Please register first.")
            return
            
        # List available users
        user_files = list(profiles_path.glob("*.json"))
        if not user_files:
            self.console.print("No users found. Please register first.")
            return
            
        user_ids = [file.stem for file in user_files]
        
        # Let user select a profile
        user_id = await questionary.select(
            "Select a user profile:",
            choices=user_ids + ["Cancel"]
        ).ask_async()
        
        if user_id == "Cancel":
            return
            
        # Load the profile
        profile = await self.learning_hub.get_user_profile(user_id)
        if profile:
            self.current_user_id = user_id
            self.console.print(f"Welcome back, {user_id}!")
        else:
            self.console.print(f"Error loading user profile for {user_id}.")
            
    async def _register(self):
        """Handle user registration."""
        self.console.print("\n[bold]User Registration[/bold]")
        
        # Get user information
        user_id = await questionary.text("Enter a username:").ask_async()
        
        # Check if user already exists
        profiles_path = self.data_path / "user_profiles"
        if profiles_path.exists() and (profiles_path / f"{user_id}.json").exists():
            self.console.print(f"User {user_id} already exists. Please login instead.")
            return
            
        # Get programming experience
        experience_years = await questionary.select(
            "Years of programming experience:",
            choices=["0", "1", "2-3", "4-5", "6-10", "10+"]
        ).ask_async()
        
        # Convert to numeric value
        if experience_years == "0":
            experience_numeric = 0
        elif experience_years == "1":
            experience_numeric = 1
        elif experience_years == "2-3":
            experience_numeric = 2
        elif experience_years == "4-5":
            experience_numeric = 4
        elif experience_years == "6-10":
            experience_numeric = 6
        else:  # 10+
            experience_numeric = 10
            
        # Get Python skill level
        skill_level = await questionary.select(
            "Python skill level:",
            choices=[level.value for level in SkillLevel]
        ).ask_async()
        
        # Get learning preferences
        learning_pace = await questionary.select(
            "Preferred learning pace:",
            choices=[pace.value for pace in LearningPace]
        ).ask_async()
        
        learning_style = await questionary.select(
            "Preferred learning style:",
            choices=[style.value for style in LearningStyle]
        ).ask_async()
        
        # Create user profile
        profile = UserProfile(
            user_id=user_id,
            programming_experience_years=experience_numeric,
            python_skill_level=skill_level,
            learning_pace=learning_pace,
            preferred_learning_style=learning_style
        )
        
        # Save profile
        await self.learning_hub.save_user_profile(profile)
        
        self.current_user_id = user_id
        self.console.print(f"Welcome, {user_id}! Your profile has been created.")
        
    async def _start_session(self):
        """Start a new learning session."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Start New Learning Session[/bold]")
        
        # Select framework
        framework = await questionary.select(
            "Select a framework to learn:",
            choices=[fw.value for fw in SupportedFrameworks]
        ).ask_async()
        
        # Get framework config
        framework_enum = SupportedFrameworks(framework)
        framework_config = self.learning_hub.constellation_manager.framework_configs.get(framework_enum)
        
        if not framework_config:
            self.console.print(f"Framework {framework} is not configured yet.")
            return
            
        # Select module
        module_choices = [
            f"{module.module_id}: {module.title} ({module.difficulty.value})"
            for module in framework_config.modules
        ]
        
        if not module_choices:
            self.console.print(f"No modules available for {framework}.")
            return
            
        module_selection = await questionary.select(
            "Select a module to learn:",
            choices=module_choices + ["Cancel"]
        ).ask_async()
        
        if module_selection == "Cancel":
            return
            
        module_id = module_selection.split(":")[0].strip()
        
        # Create session
        try:
            session_id = await self.learning_hub.create_session(
                user_id=self.current_user_id,
                framework=framework,
                module_id=module_id,
                llm=self.llm
            )
            
            self.current_session_id = session_id
            self.console.print(f"Session created: {session_id}")
            
            # Start conversation
            await self._run_conversation()
            
        except Exception as e:
            self.console.print(f"Error creating session: {e}")
            
    async def _continue_session(self):
        """Continue a previous session."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Continue Previous Session[/bold]")
        self.console.print("This feature is not implemented yet.")
        
    async def _view_progress(self):
        """View learning progress."""
        if not self.current_user_id:
            self.console.print("Please login first.")
            return
            
        self.console.print("\n[bold]Learning Progress[/bold]")
        
        try:
            # Get progress data
            progress = await self.learning_hub.get_learning_progress(self.current_user_id)
            
            # Display progress
            table = Table(title=f"Learning Progress for {self.current_user_id}")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Sessions", str(progress["total_sessions"]))
            table.add_row("Frameworks Studied", ", ".join(progress["frameworks_studied"]))
            table.add_row("Modules Completed", str(progress["modules_completed"]))
            table.add_row("Average Effectiveness", f"{progress['average_effectiveness']:.2f}")
            
            self.console.print(table)
            
            # Display recommendations
            if progress.get("recommendations"):
                self.console.print("\n[bold]Recommendations:[/bold]")
                for rec in progress["recommendations"]:
                    self.console.print(f"- {rec['message']}")
                    
        except Exception as e:
            self.console.print(f"Error retrieving progress: {e}")
            
    async def _run_conversation(self):
        """Run a conversation session."""
        if not self.current_session_id:
            self.console.print("No active session.")
            return
            
        self.console.print("\n[bold]Learning Session[/bold]")
        self.console.print("Type 'exit' to end the session.")
        
        while True:
            try:
                # Get user input
                user_input = await questionary.text("You:").ask_async()
                
                # Handle cancellation or exit
                if user_input is None:
                    self.console.print("\nCancelled by user")
                    break
                    
                if user_input.lower() in ["exit", "quit", "bye"]:
                    # End session
                    await self.learning_hub.end_session(self.current_session_id)
                    self.current_session_id = None
                    self.console.print("Session ended.")
                    break
                    
                try:
                    # Process message
                    result = await self.learning_hub.process_message(
                        user_id=self.current_user_id,
                        message=user_input,
                        session_id=self.current_session_id
                    )
                    
                    # Display response
                    self.console.print("\n[bold]GAAPF:[/bold]")
                    self.console.print(Markdown(result["response"]))
                    self.console.print(f"[dim](via {', '.join(result['agent_path'])})[/dim]")
                    
                except Exception as e:
                    self.console.print(f"Error: {e}")
                    
            except KeyboardInterrupt:
                self.console.print("\nCancelled by user")
                break
            except Exception as e:
                self.console.print(f"\nError: {e}")
                break


    async def _show_recommendations(self):
        """Show personalized learning recommendations."""
        try:
            self.console.print("\n[bold blue]🎯 Learning Recommendations[/bold blue]")

            if not self.learning_hub:
                self.console.print("[red]Learning hub not initialized[/red]")
                return

            # Get user's current knowledge state
            with self.console.status("[bold green]Analyzing your learning progress..."):
                # This would integrate with the knowledge graph and analytics
                recommendations = await self._get_learning_recommendations()

            if not recommendations:
                self.console.print("[yellow]No recommendations available at this time.[/yellow]")
                return

            # Display recommendations in a nice format
            for i, rec in enumerate(recommendations[:5], 1):
                panel_content = f"""
**Framework:** {rec.get('framework', 'Unknown')}
**Difficulty:** {rec.get('difficulty', 'Unknown')}
**Estimated Time:** {rec.get('estimated_time', 'Unknown')} minutes
**Reason:** {rec.get('reason', 'Recommended for you')}

{rec.get('description', '')}
                """.strip()

                self.console.print(Panel(
                    panel_content,
                    title=f"#{i} {rec.get('title', 'Recommendation')}",
                    border_style="blue"
                ))

            # Ask if user wants to start one of the recommendations
            if Confirm.ask("\nWould you like to start one of these recommendations?"):
                choice = await questionary.select(
                    "Which recommendation would you like to start?",
                    choices=[f"#{i+1} {rec.get('title', 'Recommendation')}"
                            for i, rec in enumerate(recommendations[:5])]
                ).ask_async()

                if choice:
                    # Extract the recommendation index
                    rec_index = int(choice.split('#')[1].split(' ')[0]) - 1
                    selected_rec = recommendations[rec_index]

                    # Start a session with the recommended content
                    await self._start_recommended_session(selected_rec)

        except Exception as e:
            self.console.print(f"[red]Error showing recommendations: {e}[/red]")

    async def _show_analytics_dashboard(self):
        """Show comprehensive analytics dashboard."""
        try:
            self.console.print("\n[bold blue]📊 Learning Analytics Dashboard[/bold blue]")

            if not self.analytics_enabled:
                self.console.print("[yellow]Analytics are disabled. Enable in settings to view dashboard.[/yellow]")
                return

            with self.console.status("[bold green]Loading analytics data..."):
                analytics_data = await self._get_analytics_data()

            # Create progress visualization
            progress_table = Table(title="Learning Progress")
            progress_table.add_column("Metric", style="cyan")
            progress_table.add_column("Value", style="green")

            progress_data = analytics_data.get("progress", {})
            progress_table.add_row("Total Sessions", str(progress_data.get("total_sessions", 0)))
            progress_table.add_row("Total Time (hours)", f"{progress_data.get('total_hours', 0):.1f}")
            progress_table.add_row("Frameworks Studied", str(progress_data.get("frameworks_count", 0)))
            progress_table.add_row("Concepts Mastered", str(progress_data.get("concepts_mastered", 0)))
            progress_table.add_row("Average Score", f"{progress_data.get('avg_score', 0):.1f}%")

            self.console.print(progress_table)

            # Show recent activity
            self.console.print("\n[bold]Recent Activity:[/bold]")
            recent_sessions = analytics_data.get("recent_sessions", [])

            for session in recent_sessions[-5:]:  # Last 5 sessions
                self.console.print(f"• {session.get('framework', 'Unknown')} - "
                                 f"{session.get('date', 'Unknown date')} - "
                                 f"Score: {session.get('score', 0):.1f}%")

        except Exception as e:
            self.console.print(f"[red]Error showing analytics dashboard: {e}[/red]")

    async def _manage_goals(self):
        """Manage learning goals."""
        try:
            self.console.print("\n[bold blue]🎯 Learning Goals Management[/bold blue]")

            # Get current goals
            current_goals = await self._get_user_goals()

            if current_goals:
                self.console.print("\n[bold]Current Goals:[/bold]")
                goals_table = Table()
                goals_table.add_column("Goal", style="cyan")
                goals_table.add_column("Framework", style="blue")
                goals_table.add_column("Target Date", style="yellow")
                goals_table.add_column("Progress", style="green")

                for goal in current_goals:
                    progress_percent = goal.get("progress", 0)
                    progress_bar = f"{'█' * int(progress_percent/10)}{'░' * (10-int(progress_percent/10))} {progress_percent}%"
                    goals_table.add_row(
                        goal.get("description", ""),
                        goal.get("framework", "General"),
                        goal.get("target_date", "No deadline"),
                        progress_bar
                    )

                self.console.print(goals_table)
            else:
                self.console.print("[yellow]No learning goals set yet.[/yellow]")

            # Goal management options
            action = await questionary.select(
                "What would you like to do?",
                choices=[
                    "Add new goal",
                    "Update existing goal",
                    "Remove goal",
                    "Back to main menu"
                ]
            ).ask_async()

            if action == "Add new goal":
                await self._add_learning_goal()

        except Exception as e:
            self.console.print(f"[red]Error managing goals: {e}[/red]")

    async def _export_data(self):
        """Export learning data."""
        try:
            self.console.print("\n[bold blue]📤 Export Learning Data[/bold blue]")

            export_type = await questionary.select(
                "What would you like to export?",
                choices=[
                    "Complete learning history",
                    "Progress summary",
                    "Analytics report",
                    "Session transcripts"
                ]
            ).ask_async()

            if not export_type:
                return

            # Get export format
            export_format = await questionary.select(
                "Choose export format:",
                choices=["JSON", "CSV", "Markdown"]
            ).ask_async()

            if not export_format:
                return

            # Get export path
            default_filename = f"gaapf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            filename = Prompt.ask(
                "Enter filename (without extension)",
                default=default_filename
            )

            with self.console.status(f"[bold green]Exporting {export_type.lower()}..."):
                export_path = await self._perform_export(export_type, export_format, filename)

            if export_path:
                self.console.print(f"[green]✅ Export completed: {export_path}[/green]")
            else:
                self.console.print("[red]❌ Export failed[/red]")

        except Exception as e:
            self.console.print(f"[red]Error exporting data: {e}[/red]")

    async def _show_settings(self):
        """Show and manage settings."""
        try:
            self.console.print("\n[bold blue]⚙️ Settings[/bold blue]")

            current_settings = await self._get_user_settings()

            # Display current settings
            settings_table = Table(title="Current Settings")
            settings_table.add_column("Setting", style="cyan")
            settings_table.add_column("Value", style="green")

            settings_table.add_row("Analytics Enabled", "✅" if current_settings.get("analytics_enabled", True) else "❌")
            settings_table.add_row("Auto-save Sessions", "✅" if current_settings.get("auto_save", True) else "❌")
            settings_table.add_row("Preferred Learning Style", current_settings.get("learning_style", "Mixed"))
            settings_table.add_row("Session Timeout (min)", str(current_settings.get("session_timeout", 60)))

            self.console.print(settings_table)

            # Settings management
            action = await questionary.select(
                "What would you like to change?",
                choices=[
                    "Toggle analytics",
                    "Change learning style preference",
                    "Adjust session timeout",
                    "Reset to defaults",
                    "Back to main menu"
                ]
            ).ask_async()

            if action == "Toggle analytics":
                self.analytics_enabled = not current_settings.get("analytics_enabled", True)
                await self._save_user_setting("analytics_enabled", self.analytics_enabled)
                status = "enabled" if self.analytics_enabled else "disabled"
                self.console.print(f"[green]Analytics {status}[/green]")

            elif action == "Change learning style preference":
                new_style = await questionary.select(
                    "Select your preferred learning style:",
                    choices=["Visual", "Auditory", "Kinesthetic", "Reading/Writing", "Mixed"]
                ).ask_async()

                if new_style:
                    await self._save_user_setting("learning_style", new_style)
                    self.console.print(f"[green]Learning style updated to {new_style}[/green]")

        except Exception as e:
            self.console.print(f"[red]Error managing settings: {e}[/red]")

    # Helper methods for the new features
    async def _get_learning_recommendations(self):
        """Get personalized learning recommendations."""
        # This would integrate with the knowledge graph and analytics system
        return [
            {
                "title": "Advanced LangChain Chains",
                "framework": "langchain",
                "difficulty": "intermediate",
                "estimated_time": 45,
                "reason": "Based on your recent progress with basic chains",
                "description": "Learn to build complex multi-step chains for advanced applications."
            },
            {
                "title": "LangGraph State Management",
                "framework": "langgraph",
                "difficulty": "advanced",
                "estimated_time": 60,
                "reason": "Natural progression from your LangChain knowledge",
                "description": "Master stateful workflows and complex agent coordination."
            }
        ]

    async def _get_analytics_data(self):
        """Get analytics data for dashboard."""
        # This would integrate with the analytics system
        return {
            "progress": {
                "total_sessions": 15,
                "total_hours": 12.5,
                "frameworks_count": 3,
                "concepts_mastered": 25,
                "avg_score": 78.5
            },
            "recent_sessions": [
                {"framework": "langchain", "date": "2024-01-15", "score": 85.0},
                {"framework": "langgraph", "date": "2024-01-14", "score": 72.0},
                {"framework": "langchain", "date": "2024-01-13", "score": 90.0}
            ]
        }

    async def _get_user_goals(self):
        """Get user's learning goals."""
        # This would integrate with the user memory system
        return [
            {
                "description": "Master LangChain fundamentals",
                "framework": "langchain",
                "target_date": "2024-02-01",
                "progress": 75
            },
            {
                "description": "Build a multi-agent system",
                "framework": "langgraph",
                "target_date": "2024-03-01",
                "progress": 30
            }
        ]

    async def _get_user_settings(self):
        """Get user settings."""
        return {
            "analytics_enabled": self.analytics_enabled,
            "auto_save": True,
            "learning_style": "Mixed",
            "session_timeout": 60
        }

    async def _save_user_setting(self, key: str, value: Any):
        """Save a user setting."""
        # This would integrate with the user memory system
        pass

    async def _perform_export(self, export_type: str, export_format: str, filename: str):
        """Perform data export."""
        # This would implement the actual export functionality
        export_path = f"{filename}.{export_format.lower()}"
        # Simulate export
        return export_path

    async def _add_learning_goal(self):
        """Add a new learning goal."""
        goal_description = Prompt.ask("Enter goal description")
        framework = await questionary.select(
            "Select framework:",
            choices=["langchain", "langgraph", "crewai", "autogen", "general"]
        ).ask_async()

        target_date = Prompt.ask("Enter target date (YYYY-MM-DD)", default="")

        # This would save to user memory system
        self.console.print(f"[green]✅ Goal added: {goal_description}[/green]")


async def main():
    """Main entry point for the CLI application."""
    parser = argparse.ArgumentParser(description="GAAPF CLI")
    parser.add_argument("--data-path", type=str, default="data", help="Path to data directory")
    args = parser.parse_args()

    app = GAAPFCli(data_path=Path(args.data_path))
    await app.run()


if __name__ == "__main__":
    asyncio.run(main()) 