"""
Error Handling and Edge Case Tests for vinagent project.

This module tests:
- Error conditions and exception handling
- Invalid inputs and malformed data
- Network failures and timeouts
- Boundary conditions and limits
- Resource exhaustion scenarios
- Concurrent access issues
- Data corruption handling
"""

import pytest
import tempfile
import json
import os
import time
import threading
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from concurrent.futures import ThreadPoolExecutor

# Import vinagent components
from vinagent.agent.agent import Agent
from vinagent.memory.memory import Memory
from vinagent.register.tool import ToolManager


class TestAgentErrorHandling:
    """Test Agent error handling scenarios."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def failing_llm(self):
        """Mock LLM that fails in various ways."""
        llm = Mock()
        llm.model_name = "failing-llm"
        llm.failure_mode = "none"  # Can be set to different failure modes
        
        def mock_invoke(messages):
            if llm.failure_mode == "timeout":
                time.sleep(10)  # Simulate timeout
            elif llm.failure_mode == "connection_error":
                raise ConnectionError("Failed to connect to LLM service")
            elif llm.failure_mode == "invalid_response":
                return None  # Invalid response
            elif llm.failure_mode == "malformed_response":
                mock_response = Mock()
                mock_response.content = None  # Malformed response
                return mock_response
            elif llm.failure_mode == "rate_limit":
                raise Exception("Rate limit exceeded")
            else:
                mock_response = Mock()
                mock_response.content = "Normal response"
                return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    def test_agent_with_connection_error(self, failing_llm, temp_data_path):
        """Test agent behavior when LLM connection fails."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        failing_llm.failure_mode = "connection_error"
        
        agent = Agent(
            llm=failing_llm,
            tools=[],
            tools_path=tools_file,
            description="Connection error test agent"
        )
        
        # Should raise ConnectionError
        with pytest.raises(ConnectionError):
            agent.invoke("Test connection error")
    
    def test_agent_with_invalid_llm_response(self, failing_llm, temp_data_path):
        """Test agent behavior with invalid LLM response."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        failing_llm.failure_mode = "invalid_response"
        
        agent = Agent(
            llm=failing_llm,
            tools=[],
            tools_path=tools_file,
            description="Invalid response test agent"
        )
        
        # Should handle invalid response gracefully or raise appropriate error
        try:
            result = agent.invoke("Test invalid response")
            # If it doesn't raise an error, result should be handled gracefully
            assert result is None or hasattr(result, 'content')
        except Exception as e:
            # If it raises an error, it should be a meaningful one
            assert isinstance(e, (AttributeError, ValueError, TypeError))
    
    def test_agent_with_malformed_response(self, failing_llm, temp_data_path):
        """Test agent behavior with malformed LLM response."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        failing_llm.failure_mode = "malformed_response"
        
        agent = Agent(
            llm=failing_llm,
            tools=[],
            tools_path=tools_file,
            description="Malformed response test agent"
        )
        
        # Should handle malformed response gracefully
        try:
            result = agent.invoke("Test malformed response")
            # Should either handle gracefully or raise meaningful error
            assert result is not None
        except Exception as e:
            assert isinstance(e, (AttributeError, ValueError, TypeError))
    
    def test_agent_with_rate_limit_error(self, failing_llm, temp_data_path):
        """Test agent behavior when rate limited."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        failing_llm.failure_mode = "rate_limit"
        
        agent = Agent(
            llm=failing_llm,
            tools=[],
            tools_path=tools_file,
            description="Rate limit test agent"
        )
        
        # Should raise rate limit exception
        with pytest.raises(Exception, match="Rate limit exceeded"):
            agent.invoke("Test rate limit")
    
    def test_agent_with_corrupted_tools_file(self, temp_data_path):
        """Test agent behavior with corrupted tools file."""
        mock_llm = Mock()
        mock_llm.model_name = "test-llm"
        mock_llm.invoke.return_value = Mock(content="Test response")
        
        # Create corrupted tools file
        tools_file = temp_data_path / "corrupted_tools.json"
        tools_file.write_text("{ invalid json content }")
        
        # Agent should handle corrupted tools file gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Corrupted tools test agent"
        )
        
        # Should still be able to function for basic operations
        response = agent.invoke("Test with corrupted tools")
        assert response is not None
    
    def test_agent_with_missing_permissions(self, temp_data_path):
        """Test agent behavior with insufficient file permissions."""
        mock_llm = Mock()
        mock_llm.model_name = "test-llm"
        mock_llm.invoke.return_value = Mock(content="Test response")
        
        # Create a directory structure that might cause permission issues
        restricted_dir = temp_data_path / "restricted"
        restricted_dir.mkdir()
        tools_file = restricted_dir / "tools.json"
        
        try:
            # Try to make directory read-only (may not work on all systems)
            restricted_dir.chmod(0o444)
            
            # Agent should handle permission issues gracefully
            agent = Agent(
                llm=mock_llm,
                tools=[],
                tools_path=tools_file,
                description="Permission test agent"
            )
            
            # Should still function for basic operations
            response = agent.invoke("Test with permission issues")
            assert response is not None
            
        except (OSError, PermissionError):
            # Skip test if permission changes don't work on this system
            pytest.skip("Cannot modify permissions on this system")
        finally:
            # Restore permissions for cleanup
            try:
                restricted_dir.chmod(0o755)
            except:
                pass


class TestMemoryErrorHandling:
    """Test Memory system error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_with_corrupted_file(self, temp_data_path):
        """Test memory behavior with corrupted memory file."""
        memory_path = temp_data_path / "corrupted_memory.jsonl"
        
        # Create corrupted memory file
        memory_path.write_text("{ invalid json content that cannot be parsed }")
        
        # Memory should handle corrupted file gracefully
        memory = Memory(memory_path=memory_path)
        
        # Should be able to load (might return empty or handle gracefully)
        user_id = "test_user"
        loaded_memory = memory.load_memory(user_id=user_id, load_type='list')
        
        # Should return empty list or handle gracefully
        assert loaded_memory == [] or loaded_memory is None
    
    def test_memory_with_disk_full_simulation(self, temp_data_path):
        """Test memory behavior when disk is full (simulated)."""
        memory_path = temp_data_path / "disk_full_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Mock the file write to simulate disk full error
        with patch('pathlib.Path.write_text') as mock_write:
            mock_write.side_effect = OSError("No space left on device")
            
            # Should handle disk full error gracefully
            try:
                memory.save_memory([], memory_path, "test_user")
            except OSError:
                # It's acceptable to raise OSError for disk full
                pass
    
    def test_memory_with_concurrent_access(self, temp_data_path):
        """Test memory behavior with concurrent access."""
        memory_path = temp_data_path / "concurrent_memory.jsonl"
        
        def write_memory(user_id, content):
            memory = Memory(memory_path=memory_path)
            test_data = [{"role": "user", "content": f"{content} from {user_id}"}]
            memory.save_memory(test_data, memory_path, user_id)
        
        # Create multiple threads writing to the same memory file
        threads = []
        for i in range(5):
            thread = threading.Thread(
                target=write_memory, 
                args=(f"user_{i}", f"message_{i}")
            )
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Memory file should exist and be readable
        assert memory_path.exists()
        
        # Should be able to create new memory instance and read
        memory = Memory(memory_path=memory_path)
        loaded_memory = memory.load_memory(user_id="user_0", load_type='list')
        
        # Should have some data (exact content may vary due to concurrency)
        assert loaded_memory is not None
    
    def test_memory_with_extremely_large_data(self, temp_data_path):
        """Test memory behavior with extremely large data."""
        memory_path = temp_data_path / "large_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Create very large memory data
        large_content = "x" * 10000  # 10KB string
        large_data = [{"role": "user", "content": large_content} for _ in range(100)]  # ~1MB
        
        user_id = "large_data_user"
        
        # Should handle large data gracefully
        try:
            memory.save_memory(large_data, memory_path, user_id)
            
            # Should be able to load large data
            loaded_memory = memory.load_memory(user_id=user_id, load_type='list')
            assert loaded_memory is not None
            
        except (MemoryError, OSError):
            # Acceptable to fail with memory/disk errors for very large data
            pytest.skip("System cannot handle large memory test data")
    
    def test_memory_with_invalid_user_ids(self, temp_data_path):
        """Test memory behavior with invalid user IDs."""
        memory_path = temp_data_path / "invalid_user_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Test with various invalid user IDs
        invalid_user_ids = [None, "", "   ", "\n\t", "user/with/slashes", "user\\with\\backslashes"]
        
        for invalid_id in invalid_user_ids:
            # Should handle invalid user IDs gracefully
            try:
                loaded_memory = memory.load_memory(user_id=invalid_id, load_type='list')
                assert loaded_memory == [] or loaded_memory is None
            except (ValueError, TypeError):
                # Acceptable to raise validation errors for invalid IDs
                pass


class TestToolManagerErrorHandling:
    """Test ToolManager error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_tool_manager_with_missing_tools_file(self, temp_data_path):
        """Test ToolManager with completely missing tools file."""
        missing_file = temp_data_path / "nonexistent_tools.json"
        
        # Should handle missing file gracefully
        tool_manager = ToolManager(tools_path=missing_file)
        
        assert tool_manager is not None
        assert tool_manager.tools_path == missing_file
    
    def test_tool_manager_with_malformed_tools_config(self, temp_data_path):
        """Test ToolManager with malformed tools configuration."""
        tools_file = temp_data_path / "malformed_tools.json"
        
        # Create malformed JSON
        tools_file.write_text('{"tool1": {"name": "incomplete_tool"')  # Missing closing braces
        
        # Should handle malformed JSON gracefully
        tool_manager = ToolManager(tools_path=tools_file)
        
        assert tool_manager is not None
    
    def test_tool_manager_with_invalid_tool_definitions(self, temp_data_path):
        """Test ToolManager with invalid tool definitions."""
        tools_file = temp_data_path / "invalid_tools.json"
        
        # Create tools with invalid definitions
        invalid_tools = {
            "missing_name": {
                "description": "Tool without name"
            },
            "missing_description": {
                "name": "tool_without_description"
            },
            "invalid_module": {
                "name": "invalid_module_tool",
                "description": "Tool with invalid module",
                "module_path": "nonexistent.module.path",
                "class_name": "NonexistentClass"
            }
        }
        
        tools_file.write_text(json.dumps(invalid_tools, indent=2))
        
        # Should handle invalid tool definitions gracefully
        tool_manager = ToolManager(tools_path=tools_file)
        
        assert tool_manager is not None
    
    @pytest.mark.asyncio
    async def test_tool_execution_timeout(self, temp_data_path):
        """Test tool execution timeout handling."""
        tools_file = temp_data_path / "timeout_tools.json"
        tools_file.write_text(json.dumps({}))
        
        tool_manager = ToolManager(tools_path=tools_file)
        
        # Mock a tool execution that times out
        with patch.object(tool_manager, '_execute_tool') as mock_execute:
            mock_execute.side_effect = TimeoutError("Tool execution timed out")
            
            # Should handle timeout gracefully
            with pytest.raises(TimeoutError):
                await tool_manager._execute_tool(
                    tool_name="timeout_tool",
                    tool_type="function",
                    arguments={"query": "test"},
                    module_path="test_module"
                )
    
    def test_tool_manager_with_circular_dependencies(self, temp_data_path):
        """Test ToolManager with circular tool dependencies."""
        tools_file = temp_data_path / "circular_tools.json"
        
        # Create tools with circular dependencies
        circular_tools = {
            "tool_a": {
                "name": "tool_a",
                "description": "Tool A that depends on Tool B",
                "dependencies": ["tool_b"]
            },
            "tool_b": {
                "name": "tool_b", 
                "description": "Tool B that depends on Tool A",
                "dependencies": ["tool_a"]
            }
        }
        
        tools_file.write_text(json.dumps(circular_tools, indent=2))
        
        # Should handle circular dependencies gracefully
        tool_manager = ToolManager(tools_path=tools_file)
        
        assert tool_manager is not None


class TestBoundaryConditions:
    """Test boundary conditions and limits."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_agent_with_empty_input(self, temp_data_path):
        """Test agent behavior with empty input."""
        mock_llm = Mock()
        mock_llm.model_name = "boundary-test-llm"
        mock_llm.invoke.return_value = Mock(content="Empty input response")
        
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Boundary test agent"
        )
        
        # Test with empty string
        response = agent.invoke("")
        assert response is not None
        
        # Test with whitespace only
        response = agent.invoke("   \n\t   ")
        assert response is not None
    
    def test_agent_with_extremely_long_input(self, temp_data_path):
        """Test agent behavior with extremely long input."""
        mock_llm = Mock()
        mock_llm.model_name = "long-input-test-llm"
        mock_llm.invoke.return_value = Mock(content="Long input response")
        
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Long input test agent"
        )
        
        # Create extremely long input (100KB)
        long_input = "x" * 100000
        
        # Should handle long input gracefully
        try:
            response = agent.invoke(long_input)
            assert response is not None
        except (MemoryError, ValueError):
            # Acceptable to fail with memory errors for extremely long input
            pytest.skip("System cannot handle extremely long input")
    
    def test_memory_with_zero_entries(self, temp_data_path):
        """Test memory behavior with zero entries."""
        memory_path = temp_data_path / "zero_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Load from empty memory
        loaded_memory = memory.load_memory(user_id="test_user", load_type='list')
        
        # Should return empty list
        assert loaded_memory == []
    
    def test_memory_with_maximum_entries(self, temp_data_path):
        """Test memory behavior at maximum capacity."""
        memory_path = temp_data_path / "max_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        user_id = "max_test_user"
        
        # Add many entries to test limits
        for i in range(1000):  # 1000 entries
            test_data = [{"role": "user", "content": f"Message {i}"}]
            memory.save_memory(test_data, memory_path, user_id)
        
        # Should still be able to load
        loaded_memory = memory.load_memory(user_id=user_id, load_type='list')
        assert loaded_memory is not None
