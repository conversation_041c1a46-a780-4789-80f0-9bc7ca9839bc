"""
Temporal State Manager for GAAPF.

This module implements the Temporal Learning Optimization system,
which continuously monitors learning outcomes and adapts constellation
selection based on historical performance patterns.
"""

from typing import Dict, Tuple, List, Optional, Any
import json
import os
import time
import statistics
import math
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, deque

from gaapf.core.constellation import ConstellationType


class EffectivenessMetrics:
    """Metrics for measuring learning effectiveness."""
    
    def __init__(self):
        self.comprehension_score = 0.0  # Understanding level
        self.engagement_score = 0.0     # Interaction and interest
        self.completion_rate = 0.0      # Task completion
        self.satisfaction_score = 0.0   # User satisfaction
        self.efficiency_score = 0.0     # Time efficiency
        self.retention_estimate = 0.0   # Knowledge retention
        
    def calculate_overall_score(self) -> float:
        """Calculate the overall effectiveness score."""
        # Weighted average of all metrics
        weights = {
            "comprehension": 0.25,
            "engagement": 0.20,
            "completion": 0.20,
            "satisfaction": 0.15,
            "efficiency": 0.10,
            "retention": 0.10
        }
        
        overall_score = (
            weights["comprehension"] * self.comprehension_score +
            weights["engagement"] * self.engagement_score +
            weights["completion"] * self.completion_rate +
            weights["satisfaction"] * self.satisfaction_score +
            weights["efficiency"] * self.efficiency_score +
            weights["retention"] * self.retention_estimate
        )
        
        return overall_score
    
    def to_dict(self) -> Dict[str, float]:
        """Convert metrics to dictionary."""
        return {
            "comprehension_score": self.comprehension_score,
            "engagement_score": self.engagement_score,
            "completion_rate": self.completion_rate,
            "satisfaction_score": self.satisfaction_score,
            "efficiency_score": self.efficiency_score,
            "retention_estimate": self.retention_estimate,
            "overall_score": self.calculate_overall_score()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, float]) -> 'EffectivenessMetrics':
        """Create metrics from dictionary."""
        metrics = cls()
        metrics.comprehension_score = data.get("comprehension_score", 0.0)
        metrics.engagement_score = data.get("engagement_score", 0.0)
        metrics.completion_rate = data.get("completion_rate", 0.0)
        metrics.satisfaction_score = data.get("satisfaction_score", 0.0)
        metrics.efficiency_score = data.get("efficiency_score", 0.0)
        metrics.retention_estimate = data.get("retention_estimate", 0.0)
        return metrics


class LearningEffectivenessTracker:
    """Advanced learning effectiveness tracking and analysis."""

    def __init__(self):
        self.session_data = deque(maxlen=1000)  # Keep last 1000 sessions
        self.user_patterns = defaultdict(dict)

    def track_session(self, session_data: Dict[str, Any]):
        """Track a learning session for analysis."""
        session_data['timestamp'] = time.time()
        self.session_data.append(session_data)

        user_id = session_data.get('user_id')
        if user_id:
            if user_id not in self.user_patterns:
                self.user_patterns[user_id] = {
                    'sessions': deque(maxlen=100),
                    'learning_velocity': [],
                    'engagement_patterns': [],
                    'difficulty_progression': []
                }
            self.user_patterns[user_id]['sessions'].append(session_data)

    def analyze_learning_velocity(self, user_id: str) -> Dict[str, float]:
        """Analyze user's learning velocity over time."""
        if user_id not in self.user_patterns:
            return {'velocity': 0.0, 'trend': 0.0, 'consistency': 0.0}

        sessions = list(self.user_patterns[user_id]['sessions'])
        if len(sessions) < 3:
            return {'velocity': 0.0, 'trend': 0.0, 'consistency': 0.0}

        # Calculate learning velocity (concepts mastered per unit time)
        velocities = []
        for i in range(1, len(sessions)):
            time_diff = sessions[i]['timestamp'] - sessions[i-1]['timestamp']
            if time_diff > 0:
                progress_diff = sessions[i].get('progress_score', 0) - sessions[i-1].get('progress_score', 0)
                velocity = progress_diff / (time_diff / 3600)  # per hour
                velocities.append(velocity)

        if not velocities:
            return {'velocity': 0.0, 'trend': 0.0, 'consistency': 0.0}

        avg_velocity = statistics.mean(velocities)

        # Calculate trend using linear regression
        x = list(range(len(velocities)))
        if len(x) >= 2:
            trend = self._calculate_trend(x, velocities)
        else:
            trend = 0.0

        # Calculate consistency (inverse of standard deviation)
        consistency = 1.0 / (statistics.stdev(velocities) + 0.1) if len(velocities) > 1 else 1.0

        return {
            'velocity': avg_velocity,
            'trend': trend,
            'consistency': min(consistency, 1.0)
        }

    def analyze_engagement_patterns(self, user_id: str) -> Dict[str, Any]:
        """Analyze user engagement patterns."""
        if user_id not in self.user_patterns:
            return {'peak_hours': [], 'session_length_preference': 0, 'interaction_density': 0.0}

        sessions = list(self.user_patterns[user_id]['sessions'])
        if not sessions:
            return {'peak_hours': [], 'session_length_preference': 0, 'interaction_density': 0.0}

        # Analyze peak learning hours
        hour_engagement = defaultdict(list)
        session_lengths = []
        interaction_densities = []

        for session in sessions:
            timestamp = session.get('timestamp', time.time())
            hour = datetime.fromtimestamp(timestamp).hour
            engagement = session.get('engagement_score', 0.0)
            hour_engagement[hour].append(engagement)

            duration = session.get('duration', 0)
            if duration > 0:
                session_lengths.append(duration)
                interactions = session.get('interaction_count', 0)
                interaction_densities.append(interactions / duration)

        # Find peak hours (top 3 hours with highest average engagement)
        peak_hours = sorted(
            hour_engagement.items(),
            key=lambda x: statistics.mean(x[1]),
            reverse=True
        )[:3]
        peak_hours = [hour for hour, _ in peak_hours]

        # Calculate preferred session length
        session_length_preference = statistics.median(session_lengths) if session_lengths else 0

        # Calculate average interaction density
        avg_interaction_density = statistics.mean(interaction_densities) if interaction_densities else 0.0

        return {
            'peak_hours': peak_hours,
            'session_length_preference': session_length_preference,
            'interaction_density': avg_interaction_density
        }

    def predict_optimal_constellation(self, user_id: str, context: Dict[str, Any]) -> Tuple[ConstellationType, float]:
        """Predict optimal constellation based on advanced pattern analysis."""
        if user_id not in self.user_patterns:
            return ConstellationType.THEORY_PRACTICE_BALANCED, 0.5

        sessions = list(self.user_patterns[user_id]['sessions'])
        if len(sessions) < 3:
            return ConstellationType.THEORY_PRACTICE_BALANCED, 0.5

        # Analyze constellation performance
        constellation_performance = defaultdict(list)
        for session in sessions:
            constellation = session.get('constellation_type')
            effectiveness = session.get('effectiveness_score', 0.0)
            if constellation:
                constellation_performance[constellation].append(effectiveness)

        # Calculate weighted scores considering recency
        constellation_scores = {}
        current_time = time.time()

        for constellation, scores in constellation_performance.items():
            if not scores:
                continue

            # Weight recent sessions more heavily
            weighted_score = 0.0
            total_weight = 0.0

            for i, score in enumerate(scores):
                # More recent sessions get higher weight
                recency_weight = math.exp(-0.1 * (len(scores) - i - 1))
                weighted_score += score * recency_weight
                total_weight += recency_weight

            if total_weight > 0:
                constellation_scores[constellation] = weighted_score / total_weight

        if not constellation_scores:
            return ConstellationType.THEORY_PRACTICE_BALANCED, 0.5

        # Find best constellation
        best_constellation = max(constellation_scores.items(), key=lambda x: x[1])
        constellation_name = best_constellation[0]
        confidence = min(best_constellation[1], 1.0)

        # Convert string to enum
        try:
            constellation_enum = ConstellationType(constellation_name)
        except ValueError:
            constellation_enum = ConstellationType.THEORY_PRACTICE_BALANCED

        return constellation_enum, confidence

    def _calculate_trend(self, x: List[float], y: List[float]) -> float:
        """Calculate linear regression trend."""
        if len(x) != len(y) or len(x) < 2:
            return 0.0

        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(xi * xi for xi in x)

        denominator = n * sum_x2 - sum_x * sum_x
        if denominator == 0:
            return 0.0

        slope = (n * sum_xy - sum_x * sum_y) / denominator
        return slope


class TemporalStateManager:
    """
    Manages temporal optimization for constellation selection.
    
    The TemporalStateManager is responsible for:
    1. Tracking learning effectiveness over time
    2. Recognizing patterns in user learning
    3. Optimizing constellation selection based on historical data
    4. Adapting to changing user needs and preferences
    """
    
    def __init__(self, data_path: Path = Path("data/temporal_patterns")):
        """
        Initialize the TemporalStateManager.

        Args:
            data_path: Path to store temporal pattern data
        """
        self.data_path = data_path
        self.patterns = self._load_patterns()
        self.effectiveness_tracker = LearningEffectivenessTracker()
        self.pattern_cache = {}  # Cache for expensive computations
        self.last_cache_update = 0
        
    def _load_patterns(self) -> Dict:
        """
        Load temporal patterns from storage.
        
        Returns:
            Dictionary of user patterns
        """
        if not self.data_path.exists():
            os.makedirs(self.data_path, exist_ok=True)
            return {}
            
        patterns = {}
        for file in self.data_path.glob("*.json"):
            try:
                with open(file, "r") as f:
                    patterns[file.stem] = json.load(f)
            except Exception as e:
                print(f"Error loading pattern file {file}: {e}")
        
        return patterns
    
    def _save_patterns(self):
        """Save temporal patterns to storage."""
        os.makedirs(self.data_path, exist_ok=True)
        
        for user_id, pattern in self.patterns.items():
            try:
                with open(self.data_path / f"{user_id}.json", "w") as f:
                    json.dump(pattern, f, indent=2)
            except Exception as e:
                print(f"Error saving pattern for user {user_id}: {e}")
    
    async def optimize_constellation_selection(
        self,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_context: Dict
    ) -> Tuple[ConstellationType, float]:
        """
        Determine the optimal constellation type based on advanced pattern analysis.

        Args:
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            session_context: Additional context for the session

        Returns:
            Tuple of (optimal constellation type, confidence score)
        """
        user_id = user_profile.get("user_id", "unknown_user")

        # Use advanced analytics if available
        if user_id in self.patterns and self.patterns[user_id].get("effectiveness_history"):
            # Try advanced prediction first
            constellation, confidence = self.effectiveness_tracker.predict_optimal_constellation(
                user_id, session_context
            )

            if confidence > 0.6:  # High confidence threshold
                return constellation, confidence

        # If we have no data for this user, use a heuristic approach
        if user_id not in self.patterns or not self.patterns[user_id].get("effectiveness_history"):
            return await self._heuristic_selection(user_profile, framework, module_id, session_context)

        # Get user's pattern data
        user_patterns = self.patterns[user_id]

        # Find similar contexts in history with enhanced matching
        similar_contexts = self._find_similar_contexts_enhanced(
            user_patterns, framework, module_id, session_context
        )

        if not similar_contexts:
            # Fall back to heuristic if no similar contexts found
            return await self._heuristic_selection(user_profile, framework, module_id, session_context)

        # Calculate effectiveness scores with temporal weighting
        constellation_scores = defaultdict(list)
        current_time = time.time()

        for context in similar_contexts:
            constellation_type = context["constellation_type"]
            effectiveness = context["effectiveness"]
            timestamp = context.get("timestamp", current_time)

            # Apply temporal decay (recent sessions weighted more heavily)
            time_diff = current_time - timestamp
            temporal_weight = math.exp(-time_diff / (30 * 24 * 3600))  # 30-day half-life

            weighted_score = effectiveness["overall_score"] * temporal_weight
            constellation_scores[constellation_type].append(weighted_score)

        # Find the constellation type with the highest weighted average score
        best_constellation = None
        best_score = -1
        confidence = 0.0

        for constellation_type, scores in constellation_scores.items():
            if not scores:
                continue

            avg_score = sum(scores) / len(scores)
            sample_size_factor = min(len(scores) / 5.0, 1.0)
            score_variance = statistics.variance(scores) if len(scores) > 1 else 0
            variance_penalty = max(0, 1.0 - score_variance)  # Penalize high variance

            adjusted_score = avg_score * sample_size_factor * variance_penalty

            if adjusted_score > best_score:
                best_score = adjusted_score
                best_constellation = constellation_type
                confidence = sample_size_factor * variance_penalty

        if best_constellation:
            # Convert string back to enum
            best_constellation_enum = ConstellationType(best_constellation)
            return best_constellation_enum, confidence

        # Final fallback
        return await self._heuristic_selection(user_profile, framework, module_id, session_context)
    
    def _find_similar_contexts(
        self,
        user_patterns: Dict,
        framework: str,
        module_id: str,
        session_context: Dict
    ) -> List[Dict]:
        """
        Find contexts in history similar to the current one.
        
        Args:
            user_patterns: User's pattern data
            framework: Target framework
            module_id: Current learning module
            session_context: Additional context for the session
            
        Returns:
            List of similar historical contexts
        """
        similar_contexts = []
        
        for entry in user_patterns.get("effectiveness_history", []):
            # Check for exact framework and module matches
            if entry["framework"] == framework and entry["module_id"] == module_id:
                similar_contexts.append(entry)
                continue
                
            # Check for same framework but different module
            if entry["framework"] == framework:
                similar_contexts.append(entry)
                continue
        
        return similar_contexts

    def _find_similar_contexts_enhanced(
        self,
        user_patterns: Dict,
        framework: str,
        module_id: str,
        session_context: Dict
    ) -> List[Dict]:
        """
        Enhanced context matching with similarity scoring.

        Args:
            user_patterns: User's pattern data
            framework: Target framework
            module_id: Current learning module
            session_context: Additional context for the session

        Returns:
            List of similar historical contexts with similarity scores
        """
        similar_contexts = []
        current_time = time.time()

        for entry in user_patterns.get("effectiveness_history", []):
            similarity_score = 0.0

            # Exact framework and module match (highest weight)
            if entry["framework"] == framework and entry["module_id"] == module_id:
                similarity_score += 1.0
            # Same framework, different module (medium weight)
            elif entry["framework"] == framework:
                similarity_score += 0.6
            # Different framework (low weight)
            else:
                similarity_score += 0.2

            # Time of day similarity
            entry_time = entry.get("timestamp", current_time)
            entry_hour = datetime.fromtimestamp(entry_time).hour
            current_hour = datetime.fromtimestamp(current_time).hour

            hour_diff = min(abs(entry_hour - current_hour), 24 - abs(entry_hour - current_hour))
            time_similarity = 1.0 - (hour_diff / 12.0)  # Normalize to 0-1
            similarity_score += 0.3 * time_similarity

            # Context similarity (if available)
            if session_context and entry.get("context"):
                context_similarity = self._calculate_context_similarity(
                    session_context, entry["context"]
                )
                similarity_score += 0.4 * context_similarity

            # Only include contexts with reasonable similarity
            if similarity_score > 0.5:
                entry_copy = entry.copy()
                entry_copy["similarity_score"] = similarity_score
                similar_contexts.append(entry_copy)

        # Sort by similarity score (descending)
        similar_contexts.sort(key=lambda x: x["similarity_score"], reverse=True)

        # Return top 20 most similar contexts
        return similar_contexts[:20]

    def _calculate_context_similarity(self, context1: Dict, context2: Dict) -> float:
        """Calculate similarity between two session contexts."""
        if not context1 or not context2:
            return 0.0

        similarity = 0.0
        total_keys = set(context1.keys()) | set(context2.keys())

        if not total_keys:
            return 0.0

        matching_keys = 0
        for key in total_keys:
            if key in context1 and key in context2:
                if context1[key] == context2[key]:
                    matching_keys += 1
                    similarity += 1.0
                elif isinstance(context1[key], (int, float)) and isinstance(context2[key], (int, float)):
                    # Numerical similarity
                    max_val = max(abs(context1[key]), abs(context2[key]), 1)
                    diff = abs(context1[key] - context2[key])
                    numerical_sim = 1.0 - min(diff / max_val, 1.0)
                    similarity += numerical_sim

        return similarity / len(total_keys) if total_keys else 0.0

    async def _heuristic_selection(
        self,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_context: Dict = None
    ) -> Tuple[ConstellationType, float]:
        """
        Select constellation based on heuristics when no history is available.
        
        Args:
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            
        Returns:
            Tuple of (selected constellation type, confidence score)
        """
        # Extract relevant profile information
        experience_level = user_profile.get("programming_experience_years", 0)
        skill_level = user_profile.get("python_skill_level", "beginner")
        learning_style = user_profile.get("preferred_learning_style", "balanced")
        learning_pace = user_profile.get("learning_pace", "moderate")
        
        # Default to balanced approach
        constellation = ConstellationType.THEORY_PRACTICE_BALANCED
        confidence = 0.5  # Medium confidence for heuristic approach
        
        # Adjust based on experience and skill level
        if experience_level < 1 or skill_level == "beginner":
            constellation = ConstellationType.BASIC_LEARNING
            confidence = 0.7
        elif experience_level > 5 and skill_level in ["advanced", "expert"]:
            if learning_style == "theoretical":
                constellation = ConstellationType.KNOWLEDGE_INTENSIVE
                confidence = 0.6
            else:
                constellation = ConstellationType.HANDS_ON_FOCUSED
                confidence = 0.6
                
        # Adjust based on learning style
        if learning_style == "hands_on":
            constellation = ConstellationType.HANDS_ON_FOCUSED
            confidence = 0.7
        elif learning_style == "theoretical":
            constellation = ConstellationType.KNOWLEDGE_INTENSIVE
            confidence = 0.7
        elif learning_style == "guided":
            constellation = ConstellationType.GUIDED_LEARNING
            confidence = 0.7
            
        # Adjust based on learning pace
        if learning_pace == "slow" and constellation not in [ConstellationType.BASIC_LEARNING, ConstellationType.GUIDED_LEARNING]:
            constellation = ConstellationType.GUIDED_LEARNING
            confidence = 0.6
            
        return constellation, confidence
    
    async def update_effectiveness(
        self,
        user_id: str,
        framework: str,
        module_id: str,
        constellation_type: ConstellationType,
        metrics: EffectivenessMetrics,
        session_context: Dict = None
    ):
        """
        Update effectiveness metrics for a constellation.
        
        Args:
            user_id: User identifier
            framework: Target framework
            module_id: Current learning module
            constellation_type: Type of constellation used
            metrics: Effectiveness metrics
            session_context: Additional context for the session
        """
        if user_id not in self.patterns:
            self.patterns[user_id] = {
                "user_id": user_id,
                "effectiveness_history": []
            }
            
        # Create a new effectiveness entry
        entry = {
            "timestamp": time.time(),
            "framework": framework,
            "module_id": module_id,
            "constellation_type": constellation_type.value,
            "effectiveness": metrics.to_dict(),
            "context": session_context or {}
        }
        
        # Add to history
        self.patterns[user_id]["effectiveness_history"].append(entry)

        # Update effectiveness tracker
        session_data = {
            'user_id': user_id,
            'framework': framework,
            'module_id': module_id,
            'constellation_type': constellation_type.value,
            'effectiveness_score': metrics.calculate_overall_score(),
            'engagement_score': metrics.engagement_score,
            'progress_score': metrics.comprehension_score,
            'duration': session_context.get('duration', 0) if session_context else 0,
            'interaction_count': session_context.get('interaction_count', 0) if session_context else 0,
            'timestamp': entry['timestamp']
        }
        self.effectiveness_tracker.track_session(session_data)

        # Invalidate cache
        self.pattern_cache.clear()

        # Save updated patterns
        self._save_patterns()
        
    async def analyze_patterns(self, user_id: str) -> Dict:
        """
        Analyze learning patterns for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Analysis of user's learning patterns
        """
        if user_id not in self.patterns:
            return {"error": "No data available for user"}
            
        user_patterns = self.patterns[user_id]
        history = user_patterns.get("effectiveness_history", [])
        
        if not history:
            return {"error": "No history available for user"}
            
        # Calculate average effectiveness by constellation type
        constellation_effectiveness = {}
        for entry in history:
            constellation_type = entry["constellation_type"]
            if constellation_type not in constellation_effectiveness:
                constellation_effectiveness[constellation_type] = {
                    "count": 0,
                    "total_score": 0,
                    "scores": []
                }
                
            effectiveness = entry["effectiveness"]
            overall_score = effectiveness["overall_score"]
            
            constellation_effectiveness[constellation_type]["count"] += 1
            constellation_effectiveness[constellation_type]["total_score"] += overall_score
            constellation_effectiveness[constellation_type]["scores"].append(overall_score)
            
        # Calculate averages and trends
        analysis = {
            "user_id": user_id,
            "total_sessions": len(history),
            "constellation_performance": {},
            "recommendations": []
        }
        
        for constellation_type, data in constellation_effectiveness.items():
            avg_score = data["total_score"] / data["count"]
            scores = data["scores"]
            
            # Calculate trend (positive if scores are improving)
            trend = 0
            if len(scores) >= 3:
                # Simple linear regression slope calculation
                x = list(range(len(scores)))
                mean_x = sum(x) / len(x)
                mean_y = sum(scores) / len(scores)
                
                numerator = sum((x[i] - mean_x) * (scores[i] - mean_y) for i in range(len(scores)))
                denominator = sum((x[i] - mean_x) ** 2 for i in range(len(scores)))
                
                trend = numerator / denominator if denominator != 0 else 0
                
            analysis["constellation_performance"][constellation_type] = {
                "average_score": avg_score,
                "sessions": data["count"],
                "trend": trend
            }
            
        # Generate recommendations
        best_constellation = max(
            analysis["constellation_performance"].items(),
            key=lambda x: x[1]["average_score"]
        )[0]
        
        analysis["recommendations"].append({
            "type": "constellation_preference",
            "message": f"User performs best with {best_constellation} constellation",
            "confidence": 0.7
        })

        # Add advanced analytics
        learning_velocity = self.effectiveness_tracker.analyze_learning_velocity(user_id)
        engagement_patterns = self.effectiveness_tracker.analyze_engagement_patterns(user_id)

        analysis["learning_velocity"] = learning_velocity
        analysis["engagement_patterns"] = engagement_patterns

        # Generate additional recommendations based on advanced analytics
        if learning_velocity['trend'] < -0.1:
            analysis["recommendations"].append({
                "type": "learning_velocity",
                "message": "Learning velocity is declining. Consider adjusting difficulty or taking breaks.",
                "confidence": 0.8
            })
        elif learning_velocity['trend'] > 0.1:
            analysis["recommendations"].append({
                "type": "learning_velocity",
                "message": "Learning velocity is improving. Consider increasing challenge level.",
                "confidence": 0.8
            })

        if learning_velocity['consistency'] < 0.3:
            analysis["recommendations"].append({
                "type": "consistency",
                "message": "Learning consistency could be improved. Try establishing regular study patterns.",
                "confidence": 0.7
            })

        if engagement_patterns['peak_hours']:
            peak_hours_str = ', '.join(map(str, engagement_patterns['peak_hours']))
            analysis["recommendations"].append({
                "type": "optimal_timing",
                "message": f"You learn best during hours: {peak_hours_str}. Schedule important sessions then.",
                "confidence": 0.6
            })

        if engagement_patterns['session_length_preference'] > 0:
            optimal_length = engagement_patterns['session_length_preference']
            analysis["recommendations"].append({
                "type": "session_length",
                "message": f"Your optimal session length is {optimal_length:.0f} minutes.",
                "confidence": 0.6
            })

        return analysis

    async def get_real_time_recommendations(self, user_id: str, current_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get real-time recommendations based on current learning context.

        Args:
            user_id: User identifier
            current_context: Current learning context

        Returns:
            List of real-time recommendations
        """
        recommendations = []

        if user_id not in self.patterns:
            return recommendations

        # Analyze current session context
        current_time = time.time()
        current_hour = datetime.fromtimestamp(current_time).hour

        # Get engagement patterns
        engagement_patterns = self.effectiveness_tracker.analyze_engagement_patterns(user_id)

        # Check if current time is optimal for learning
        if engagement_patterns['peak_hours'] and current_hour not in engagement_patterns['peak_hours']:
            recommendations.append({
                "type": "timing_optimization",
                "priority": "medium",
                "message": f"You typically learn better at {engagement_patterns['peak_hours']}. Consider scheduling important topics then.",
                "confidence": 0.6
            })

        # Check session length
        session_duration = current_context.get('session_duration', 0)
        optimal_length = engagement_patterns.get('session_length_preference', 0)

        if optimal_length > 0 and session_duration > optimal_length * 1.5:
            recommendations.append({
                "type": "session_management",
                "priority": "high",
                "message": "Consider taking a break. You've exceeded your optimal session length.",
                "confidence": 0.8
            })

        # Check learning velocity
        learning_velocity = self.effectiveness_tracker.analyze_learning_velocity(user_id)

        if learning_velocity['velocity'] < 0.1:
            recommendations.append({
                "type": "difficulty_adjustment",
                "priority": "medium",
                "message": "Learning velocity is low. Consider reviewing fundamentals or adjusting difficulty.",
                "confidence": 0.7
            })

        return recommendations

    async def predict_learning_outcome(self, user_id: str, planned_session: Dict[str, Any]) -> Dict[str, Any]:
        """
        Predict learning outcome for a planned session.

        Args:
            user_id: User identifier
            planned_session: Planned session details

        Returns:
            Predicted outcome metrics
        """
        if user_id not in self.patterns:
            return {
                "predicted_effectiveness": 0.5,
                "confidence": 0.1,
                "recommendations": ["Insufficient data for prediction"]
            }

        # Get historical data for similar sessions
        framework = planned_session.get('framework')
        module_id = planned_session.get('module_id')
        constellation_type = planned_session.get('constellation_type')

        similar_sessions = []
        for entry in self.patterns[user_id].get("effectiveness_history", []):
            if (entry.get('framework') == framework and
                entry.get('module_id') == module_id and
                entry.get('constellation_type') == constellation_type):
                similar_sessions.append(entry)

        if not similar_sessions:
            return {
                "predicted_effectiveness": 0.5,
                "confidence": 0.2,
                "recommendations": ["No similar sessions found for accurate prediction"]
            }

        # Calculate predicted effectiveness
        effectiveness_scores = [s['effectiveness']['overall_score'] for s in similar_sessions]
        predicted_effectiveness = statistics.mean(effectiveness_scores)
        confidence = min(len(similar_sessions) / 5.0, 1.0)

        # Generate recommendations
        recommendations = []
        if predicted_effectiveness < 0.6:
            recommendations.append("Consider using a different constellation type for better results")
        if len(similar_sessions) < 3:
            recommendations.append("Limited historical data - prediction may be less accurate")

        return {
            "predicted_effectiveness": predicted_effectiveness,
            "confidence": confidence,
            "recommendations": recommendations,
            "historical_sessions": len(similar_sessions)
        }