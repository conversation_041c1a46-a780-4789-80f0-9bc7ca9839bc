"""
Integration tests between vinagent and GAAPF components.

This module tests:
- Integration between vinagent Agent and GAAPF systems
- Cross-module interactions and data flow
- Configuration sharing and compatibility
- Memory system integration
- Tool integration across frameworks
- Error propagation and handling
"""

import pytest
import tempfile
import json
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import vinagent components
from vinagent.agent.agent import Agent
from vinagent.memory.memory import Memory
from vinagent.register.tool import ToolManager

# Import GAAPF components with error handling
try:
    from gaapf.config.env_config import EnvironmentConfig
    from gaapf.core.knowledge_graph import KnowledgeGraphManager, ConceptNode
    from gaapf.core.memory_systems import ConversationMemory, KnowledgeMemory, UserMemory
    from gaapf.core.analytics_system import AnalyticsManager
    GAAPF_AVAILABLE = True
except ImportError:
    GAAPF_AVAILABLE = False


class TestVinagentGAAPFIntegration:
    """Test integration between vinagent and GAAPF components."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for integration testing."""
        llm = Mock()
        llm.model_name = "integration-test-llm"
        
        def mock_invoke(messages):
            mock_response = Mock()
            mock_response.content = "Integration test response from mock LLM"
            return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    @pytest.fixture
    def vinagent_agent(self, mock_llm, temp_data_path):
        """Create a vinagent Agent for integration testing."""
        tools_file = temp_data_path / "integration_tools.json"
        tools_file.write_text(json.dumps({}))
        
        return Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            description="Integration test agent"
        )
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_agent_with_gaapf_config(self, vinagent_agent):
        """Test vinagent Agent integration with GAAPF configuration."""
        # Create GAAPF configuration
        gaapf_config = EnvironmentConfig()
        
        # Test that agent can work alongside GAAPF config
        assert vinagent_agent is not None
        assert gaapf_config is not None
        
        # Test basic interaction
        response = vinagent_agent.invoke("Test integration with GAAPF config")
        assert response is not None
        assert hasattr(response, 'content')
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_memory_system_integration(self, temp_data_path, mock_llm):
        """Test integration between vinagent Memory and GAAPF MemorySystem."""
        # Create vinagent memory
        vinagent_memory_path = temp_data_path / "vinagent_memory.jsonl"
        vinagent_memory = Memory(memory_path=vinagent_memory_path)
        
        # Create GAAPF memory systems
        try:
            gaapf_conversation_memory = ConversationMemory("integration_test_session")
            gaapf_user_memory = UserMemory("integration_test_user", temp_data_path)

            # Test that both memory systems can coexist
            assert vinagent_memory is not None
            assert gaapf_conversation_memory is not None
            assert gaapf_user_memory is not None

            # Test data isolation
            user_id = "integration_test_user"

            # Store data in vinagent memory
            vinagent_data = [{"role": "user", "content": "vinagent test"}]
            vinagent_memory.save_memory(vinagent_data, vinagent_memory_path, user_id)

            # Store data in GAAPF memory
            gaapf_conversation_memory.add_message("user", "GAAPF test message")

            # Verify vinagent memory works
            loaded_vinagent = vinagent_memory.load_memory(user_id=user_id, load_type='list')
            assert loaded_vinagent == vinagent_data

            # Verify GAAPF memory works
            recent_messages = gaapf_conversation_memory.get_recent_messages(1)
            assert len(recent_messages) == 1
            assert "GAAPF test message" in recent_messages[0]["content"]

        except Exception as e:
            pytest.skip(f"GAAPF memory systems interface different: {e}")
    
    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_knowledge_graph_integration(self, temp_data_path, vinagent_agent):
        """Test integration with GAAPF Knowledge Graph."""
        try:
            # Create GAAPF knowledge graph
            kg = KnowledgeGraphManager(data_path=temp_data_path)
            
            # Add a concept to the knowledge graph
            test_concept = ConceptNode(
                concept_id="integration_concept",
                name="Integration Test Concept",
                framework="vinagent_integration",
                description="A concept for testing integration"
            )
            
            success = kg.add_concept(test_concept)
            assert success == True
            
            # Verify concept was added
            retrieved_concept = kg.get_concept("integration_concept")
            assert retrieved_concept is not None
            assert retrieved_concept.name == "Integration Test Concept"
            
            # Test that vinagent agent can still function
            response = vinagent_agent.invoke("Test with knowledge graph present")
            assert response is not None
            
        except Exception as e:
            pytest.skip(f"GAAPF KnowledgeGraphManager interface different: {e}")

    @pytest.mark.skipif(not GAAPF_AVAILABLE, reason="GAAPF components not available")
    def test_gaapf_memory_systems_integration(self, temp_data_path):
        """Test GAAPF memory systems integration."""
        try:
            # Initialize GAAPF memory systems
            conversation_memory = ConversationMemory("test_session")
            knowledge_memory = KnowledgeMemory(temp_data_path)
            user_memory = UserMemory("test_user", temp_data_path)

            assert conversation_memory is not None
            assert knowledge_memory is not None
            assert user_memory is not None

            # Test basic memory operations
            conversation_memory.add_message("user", "Test message")
            knowledge_memory.add_insight("Test insight")
            user_memory.add_goal("Test goal")

            # Verify operations worked
            recent_messages = conversation_memory.get_recent_messages(1)
            assert len(recent_messages) == 1
            assert recent_messages[0]["content"] == "Test message"

        except Exception as e:
            # If memory systems have different interface, skip gracefully
            pytest.skip(f"GAAPF memory systems interface different than expected: {e}")

    def test_configuration_compatibility(self, temp_data_path):
        """Test configuration compatibility between systems."""
        # Create configuration files for both systems
        vinagent_config = {
            "agent": {
                "description": "Test agent",
                "skills": ["testing"]
            },
            "memory": {
                "path": str(temp_data_path / "memory.jsonl")
            }
        }
        
        gaapf_config = {
            "framework": {
                "name": "test_framework",
                "version": "1.0.0"
            },
            "data_path": str(temp_data_path)
        }
        
        # Save configurations
        vinagent_config_file = temp_data_path / "vinagent_config.json"
        gaapf_config_file = temp_data_path / "gaapf_config.json"
        
        vinagent_config_file.write_text(json.dumps(vinagent_config, indent=2))
        gaapf_config_file.write_text(json.dumps(gaapf_config, indent=2))
        
        # Verify both configurations can be loaded
        loaded_vinagent = json.loads(vinagent_config_file.read_text())
        loaded_gaapf = json.loads(gaapf_config_file.read_text())
        
        assert loaded_vinagent == vinagent_config
        assert loaded_gaapf == gaapf_config
        
        # Test that configurations don't conflict
        assert loaded_vinagent != loaded_gaapf  # Should be different
        assert "agent" in loaded_vinagent
        assert "framework" in loaded_gaapf


class TestCrossModuleInteractions:
    """Test interactions between different modules within the project."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for cross-module testing."""
        llm = Mock()
        llm.model_name = "cross-module-test-llm"
        
        def mock_invoke(messages):
            mock_response = Mock()
            mock_response.content = "Cross-module test response"
            return mock_response
        
        llm.invoke = mock_invoke
        return llm
    
    def test_agent_memory_tool_integration(self, mock_llm, temp_data_path):
        """Test integration between Agent, Memory, and Tools."""
        # Create tools configuration
        tools_file = temp_data_path / "cross_module_tools.json"
        tools_data = {
            "memory_tool": {
                "name": "memory_tool",
                "description": "Tool that interacts with memory",
                "tool_type": "function"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        
        # Create memory path
        memory_path = temp_data_path / "cross_module_memory.jsonl"
        
        # Create agent with both memory and tools
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=str(memory_path),  # Use string to avoid Path issues
            description="Cross-module integration agent"
        )
        
        # Verify all components are initialized
        assert agent.llm is not None
        assert agent.tools_manager is not None
        assert agent.memory is not None
        
        # Test basic functionality
        response = agent.invoke("Test cross-module integration")
        assert response is not None
        assert hasattr(response, 'content')
    
    def test_memory_persistence_across_agents(self, mock_llm, temp_data_path):
        """Test memory persistence across different agent instances."""
        tools_file = temp_data_path / "persistence_tools.json"
        tools_file.write_text(json.dumps({}))
        memory_path = temp_data_path / "shared_memory.jsonl"
        
        # Create first agent and save memory
        agent1 = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=str(memory_path),
            description="First agent"
        )
        
        user_id = "persistence_test_user"
        
        # Manually save memory (to avoid LLM integration issues)
        if agent1.memory:
            test_memory = [{"role": "user", "content": "Persistent test message"}]
            agent1.memory.save_memory(test_memory, memory_path, user_id)
        
        # Create second agent with same memory path
        agent2 = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=str(memory_path),
            description="Second agent"
        )
        
        # Verify second agent can access first agent's memory
        if agent2.memory:
            loaded_memory = agent2.memory.load_memory(user_id=user_id, load_type='list')
            if loaded_memory:  # If memory was successfully loaded
                assert any("Persistent test message" in str(item) for item in loaded_memory)
    
    def test_tool_manager_isolation(self, temp_data_path):
        """Test that different ToolManager instances are properly isolated."""
        # Create different tool configurations
        tools1_file = temp_data_path / "tools1.json"
        tools2_file = temp_data_path / "tools2.json"
        
        tools1_data = {"tool1": {"name": "tool1", "description": "First tool"}}
        tools2_data = {"tool2": {"name": "tool2", "description": "Second tool"}}
        
        tools1_file.write_text(json.dumps(tools1_data, indent=2))
        tools2_file.write_text(json.dumps(tools2_data, indent=2))
        
        # Create separate tool managers
        tm1 = ToolManager(tools_path=tools1_file)
        tm2 = ToolManager(tools_path=tools2_file)
        
        # Verify isolation
        assert tm1.tools_path != tm2.tools_path
        assert tm1 is not tm2
    
    def test_error_propagation_across_modules(self, mock_llm, temp_data_path):
        """Test error propagation between different modules."""
        # Create agent with invalid memory path
        tools_file = temp_data_path / "error_tools.json"
        tools_file.write_text(json.dumps({}))
        
        # Use a path that will cause issues (read-only directory simulation)
        invalid_memory_path = temp_data_path / "readonly" / "memory.jsonl"
        
        # Agent should handle invalid memory path gracefully
        agent = Agent(
            llm=mock_llm,
            tools=[],
            tools_path=tools_file,
            memory_path=str(invalid_memory_path),
            description="Error handling test agent"
        )
        
        # Agent should still be functional for basic operations
        response = agent.invoke("Test error handling")
        assert response is not None


class TestDataFlowIntegration:
    """Test data flow between different components."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_configuration_data_flow(self, temp_data_path):
        """Test configuration data flow between components."""
        # Create a configuration that affects multiple components
        config_data = {
            "global": {
                "data_path": str(temp_data_path),
                "logging_level": "INFO"
            },
            "agent": {
                "description": "Data flow test agent",
                "skills": ["data_processing"]
            },
            "memory": {
                "max_entries": 1000,
                "compression": True
            },
            "tools": {
                "auto_load": True,
                "timeout": 30
            }
        }
        
        config_file = temp_data_path / "integrated_config.json"
        config_file.write_text(json.dumps(config_data, indent=2))
        
        # Verify configuration can be loaded and parsed
        loaded_config = json.loads(config_file.read_text())
        
        assert loaded_config["global"]["data_path"] == str(temp_data_path)
        assert loaded_config["agent"]["description"] == "Data flow test agent"
        assert loaded_config["memory"]["max_entries"] == 1000
        assert loaded_config["tools"]["auto_load"] == True
    
    def test_memory_data_consistency(self, temp_data_path):
        """Test memory data consistency across operations."""
        memory_path = temp_data_path / "consistency_memory.jsonl"
        
        # Create memory instance
        memory = Memory(memory_path=memory_path)
        
        user_id = "consistency_test_user"
        
        # Perform multiple memory operations
        test_data_1 = [{"role": "user", "content": "First message"}]
        test_data_2 = [{"role": "user", "content": "Second message"}]
        
        # Save first data
        memory.save_memory(test_data_1, memory_path, user_id)
        
        # Load and verify
        loaded_1 = memory.load_memory(user_id=user_id, load_type='list')
        assert loaded_1 == test_data_1
        
        # Save second data (should append or replace based on implementation)
        memory.save_memory(test_data_2, memory_path, user_id)
        
        # Load and verify consistency
        loaded_2 = memory.load_memory(user_id=user_id, load_type='list')
        assert loaded_2 is not None  # Should have some data
    
    def test_component_lifecycle_integration(self, temp_data_path):
        """Test component lifecycle and cleanup integration."""
        # Create temporary files for different components
        memory_file = temp_data_path / "lifecycle_memory.jsonl"
        tools_file = temp_data_path / "lifecycle_tools.json"
        config_file = temp_data_path / "lifecycle_config.json"
        
        # Initialize components
        memory = Memory(memory_path=memory_file)
        tools_manager = ToolManager(tools_path=tools_file)
        
        # Verify files were created
        assert memory_file.exists()
        assert tools_file.exists()
        
        # Test cleanup (components should handle cleanup gracefully)
        del memory
        del tools_manager
        
        # Files should still exist (persistent storage)
        assert memory_file.exists()
        assert tools_file.exists()
