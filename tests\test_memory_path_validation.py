"""
Test cases for memory path validation and error handling improvements.

This module tests the enhanced path validation and error handling
functionality added to the vinagent memory system.
"""

import pytest
import tempfile
import json
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch

from vinagent.memory.memory import (
    Memory, 
    PathValidationError, 
    validate_memory_path, 
    safe_json_load, 
    safe_json_save
)


class TestPathValidation:
    """Test path validation functionality."""
    
    def test_validate_memory_path_valid_json(self):
        """Test validation with valid .json path."""
        path = validate_memory_path("test_memory.json")
        assert path.suffix == ".json"
        assert path.name == "test_memory.json"
    
    def test_validate_memory_path_valid_jsonl(self):
        """Test validation with valid .jsonl path."""
        path = validate_memory_path("test_memory.jsonl")
        assert path.suffix == ".jsonl"
        assert path.name == "test_memory.jsonl"
    
    def test_validate_memory_path_invalid_extension(self):
        """Test validation with invalid file extension."""
        with pytest.raises(PathValidationError, match="valid extension"):
            validate_memory_path("test_memory.txt")
    
    def test_validate_memory_path_empty_string(self):
        """Test validation with empty string."""
        with pytest.raises(PathValidationError, match="cannot be empty"):
            validate_memory_path("")
    
    def test_validate_memory_path_whitespace_only(self):
        """Test validation with whitespace-only string."""
        with pytest.raises(PathValidationError, match="whitespace-only"):
            validate_memory_path("   \n\t   ")
    
    def test_validate_memory_path_none(self):
        """Test validation with None."""
        with pytest.raises(PathValidationError, match="cannot be empty or None"):
            validate_memory_path(None)
    
    def test_validate_memory_path_invalid_characters(self):
        """Test validation with invalid characters."""
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            with pytest.raises(PathValidationError, match="invalid characters"):
                validate_memory_path(f"test{char}memory.json")
    
    def test_validate_memory_path_reserved_names(self):
        """Test validation with Windows reserved names."""
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'LPT1']
        for name in reserved_names:
            with pytest.raises(PathValidationError, match="reserved name"):
                validate_memory_path(f"{name}.json")
    
    def test_validate_memory_path_too_long(self):
        """Test validation with path that's too long."""
        long_name = "a" * 300  # Very long filename
        with pytest.raises(PathValidationError, match="too long"):
            validate_memory_path(f"{long_name}.json")
    
    def test_validate_memory_path_relative_path(self):
        """Test validation with relative path."""
        path = validate_memory_path("data/memory/test.json")
        assert path.is_absolute()
        assert path.name == "test.json"
    
    def test_validate_memory_path_absolute_path(self):
        """Test validation with absolute path."""
        abs_path = Path.cwd() / "test_memory.json"
        path = validate_memory_path(str(abs_path))
        assert path.is_absolute()
        assert path.name == "test_memory.json"
    
    def test_validate_memory_path_path_object(self):
        """Test validation with Path object."""
        input_path = Path("test_memory.json")
        path = validate_memory_path(input_path)
        assert isinstance(path, Path)
        assert path.suffix == ".json"


class TestSafeJsonOperations:
    """Test safe JSON loading and saving operations."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_safe_json_load_valid_file(self, temp_data_path):
        """Test loading valid JSON file."""
        test_file = temp_data_path / "valid.json"
        test_data = {"test": "data", "number": 42}
        
        with open(test_file, 'w') as f:
            json.dump(test_data, f)
        
        loaded_data = safe_json_load(test_file)
        assert loaded_data == test_data
    
    def test_safe_json_load_missing_file(self, temp_data_path):
        """Test loading missing file returns default."""
        missing_file = temp_data_path / "missing.json"
        loaded_data = safe_json_load(missing_file, default_value={"default": True})
        assert loaded_data == {"default": True}
    
    def test_safe_json_load_empty_file(self, temp_data_path):
        """Test loading empty file returns default."""
        empty_file = temp_data_path / "empty.json"
        empty_file.write_text("")
        
        loaded_data = safe_json_load(empty_file, default_value={"empty": True})
        assert loaded_data == {"empty": True}
    
    def test_safe_json_load_corrupted_file(self, temp_data_path):
        """Test loading corrupted JSON file."""
        corrupted_file = temp_data_path / "corrupted.json"
        corrupted_file.write_text("{ invalid json content")
        
        loaded_data = safe_json_load(corrupted_file, default_value={"corrupted": True})
        assert loaded_data == {"corrupted": True}
        
        # Check that backup was created
        backup_files = list(temp_data_path.glob("*.bak"))
        assert len(backup_files) == 1
        assert "corrupted" in backup_files[0].name
    
    def test_safe_json_save_valid_data(self, temp_data_path):
        """Test saving valid data."""
        test_file = temp_data_path / "save_test.json"
        test_data = {"saved": True, "timestamp": time.time()}
        
        safe_json_save(test_data, test_file, "test_user")
        
        # Verify file was created and contains correct data
        assert test_file.exists()
        with open(test_file, 'r') as f:
            loaded_data = json.load(f)
        assert loaded_data == test_data
    
    def test_safe_json_save_creates_directories(self, temp_data_path):
        """Test that save creates parent directories."""
        nested_file = temp_data_path / "nested" / "deep" / "test.json"
        test_data = {"nested": True}
        
        safe_json_save(test_data, nested_file)
        
        assert nested_file.exists()
        assert nested_file.parent.exists()
    
    def test_safe_json_save_atomic_write(self, temp_data_path):
        """Test atomic write behavior."""
        test_file = temp_data_path / "atomic_test.json"
        test_data = {"atomic": True}
        
        # Mock file operations to simulate failure during write
        with patch('builtins.open', side_effect=IOError("Simulated failure")):
            with pytest.raises(IOError):
                safe_json_save(test_data, test_file)
        
        # File should not exist if write failed
        assert not test_file.exists()
        
        # Temp file should be cleaned up
        temp_files = list(temp_data_path.glob("*.tmp_*"))
        assert len(temp_files) == 0


class TestMemoryWithImprovedValidation:
    """Test Memory class with improved validation and error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_init_with_valid_path(self, temp_data_path):
        """Test Memory initialization with valid path."""
        memory_path = temp_data_path / "valid_memory.json"
        memory = Memory(memory_path=memory_path)
        
        assert memory.memory_path == memory_path.resolve()
        assert memory_path.exists()
    
    def test_memory_init_with_invalid_path_fallback(self, temp_data_path):
        """Test Memory initialization with invalid path uses fallback."""
        invalid_path = "invalid<path>.txt"  # Invalid characters and extension
        
        memory = Memory(memory_path=invalid_path)
        
        # Should fallback to default path
        assert memory.memory_path == Path('templates/memory.jsonl').resolve()
    
    def test_memory_load_with_corrupted_file(self, temp_data_path):
        """Test loading memory with corrupted file."""
        memory_path = temp_data_path / "corrupted_memory.json"
        memory_path.write_text("{ invalid json")
        
        memory = Memory(memory_path=memory_path)
        loaded_data = memory.load_memory(user_id="test_user")
        
        # Should return empty list for corrupted data
        assert loaded_data == []
    
    def test_memory_save_with_invalid_user_id(self, temp_data_path):
        """Test saving memory with invalid user ID."""
        memory_path = temp_data_path / "test_memory.json"
        memory = Memory(memory_path=memory_path)
        
        # Test with None user_id
        memory.save_memory([], memory_path, None)
        
        # Test with empty string user_id
        memory.save_memory([], memory_path, "")
        
        # Test with whitespace-only user_id
        memory.save_memory([], memory_path, "   ")
        
        # Memory file should still be valid (empty dict)
        loaded_data = memory.load_memory()
        assert loaded_data == {}
    
    def test_memory_load_with_invalid_user_id(self, temp_data_path):
        """Test loading memory with invalid user ID."""
        memory_path = temp_data_path / "test_memory.json"
        memory = Memory(memory_path=memory_path)
        
        # Should handle invalid user IDs gracefully
        result = memory.load_memory(user_id=None)
        assert result == {}
        
        result = memory.load_memory(user_id="")
        assert result == {}
        
        result = memory.load_memory(user_id="   ")
        assert result == {}
    
    def test_memory_save_with_invalid_data_type(self, temp_data_path):
        """Test saving memory with invalid data type."""
        memory_path = temp_data_path / "test_memory.json"
        memory = Memory(memory_path=memory_path)
        
        # Should handle non-list data gracefully
        memory.save_memory("not a list", memory_path, "test_user")
        memory.save_memory({"not": "a list"}, memory_path, "test_user")
        memory.save_memory(42, memory_path, "test_user")
        
        # Memory should remain valid
        loaded_data = memory.load_memory()
        assert isinstance(loaded_data, dict)


class TestEdgeCasesAndBoundaryConditions:
    """Test edge cases and boundary conditions."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_with_unicode_paths(self, temp_data_path):
        """Test memory with Unicode characters in paths."""
        unicode_path = temp_data_path / "测试_memory_🧠.json"
        memory = Memory(memory_path=unicode_path)
        
        assert memory.memory_path.exists()
        
        # Test saving and loading with Unicode data
        test_data = [{"content": "测试数据", "emoji": "🚀"}]
        memory.save_memory(test_data, memory.memory_path, "unicode_user")
        
        loaded_data = memory.load_memory(user_id="unicode_user")
        assert loaded_data == test_data
    
    def test_memory_with_very_long_user_id(self, temp_data_path):
        """Test memory with very long user ID."""
        memory_path = temp_data_path / "long_user_memory.json"
        memory = Memory(memory_path=memory_path)
        
        long_user_id = "a" * 1000  # Very long user ID
        test_data = [{"test": "data"}]
        
        memory.save_memory(test_data, memory_path, long_user_id)
        loaded_data = memory.load_memory(user_id=long_user_id)
        
        assert loaded_data == test_data
    
    def test_memory_with_special_characters_in_data(self, temp_data_path):
        """Test memory with special characters in data."""
        memory_path = temp_data_path / "special_chars_memory.json"
        memory = Memory(memory_path=memory_path)
        
        special_data = [
            {"content": "Line 1\nLine 2\tTabbed"},
            {"content": "Quotes: \"double\" and 'single'"},
            {"content": "Backslashes: \\ and forward slashes: /"},
            {"content": "Unicode: 你好世界 🌍"},
            {"content": "Control chars: \x00\x01\x02"}
        ]
        
        memory.save_memory(special_data, memory_path, "special_user")
        loaded_data = memory.load_memory(user_id="special_user")
        
        assert loaded_data == special_data
