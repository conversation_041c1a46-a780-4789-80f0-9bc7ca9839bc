# GAAPF Memory Systems Guide

This guide provides comprehensive documentation for GAAPF's three-tier memory architecture and how to effectively use and customize memory systems.

## 🧠 Memory Architecture Overview

GAAPF implements a sophisticated three-tier memory system designed to handle different types of information with appropriate persistence and access patterns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Memory Architecture                       │
├─────────────────────────────────────────────────────────────┤
│  Conversation Memory (Short-term)                           │
│  ├── Session-specific context                               │
│  ├── Agent handoff history                                  │
│  ├── Real-time interactions                                 │
│  └── Temporary conversation state                           │
├─────────────────────────────────────────────────────────────┤
│  Knowledge Memory (Domain-specific)                         │
│  ├── Concept understanding                                  │
│  ├── Learning patterns                                      │
│  ├── Framework-specific knowledge                           │
│  └── Cross-session insights                                 │
├─────────────────────────────────────────────────────────────┤
│  User Memory (Long-term)                                    │
│  ├── User preferences and profile                           │
│  ├── Learning history and progress                          │
│  ├── Personal goals and achievements                        │
│  └── Performance metrics                                    │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 Conversation Memory

**Purpose**: Manages short-term conversation context within learning sessions.

### Key Features

- **Session Context**: Maintains conversation flow and context
- **Agent History**: Tracks which agents have been involved
- **Real-time Updates**: Immediate storage and retrieval
- **Automatic Cleanup**: Manages memory size and cleanup

### Usage Examples

```python
from gaapf.core.memory_systems import ConversationMemory

# Initialize conversation memory
conv_memory = ConversationMemory("session_123")

# Add messages
conv_memory.add_message(
    role="user",
    content="How do I create a LangChain chain?",
    metadata={"timestamp": "2024-01-01T10:00:00Z"}
)

conv_memory.add_message(
    role="assistant",
    content="I'll help you create a LangChain chain...",
    agent_type="instructor",
    metadata={"confidence": 0.9}
)

# Retrieve recent messages
recent_messages = conv_memory.get_recent_messages(count=5)
for message in recent_messages:
    print(f"{message['role']}: {message['content']}")

# Search conversation history
matches = conv_memory.search_messages("LangChain")
print(f"Found {len(matches)} messages about LangChain")

# Get conversation summary
summary = conv_memory.get_context_summary()
print(f"Session summary: {summary}")
```

### Integration with vinagent

```python
# GAAPF integrates with vinagent's memory capabilities
from vinagent.memory.conversation import ConversationMemory as VinagentMemory

# Automatic integration when vinagent is available
conv_memory = ConversationMemory("session_123")
# Automatically uses vinagent memory if available
```

### Configuration Options

```python
# Configure conversation memory
conv_memory = ConversationMemory(
    session_id="session_123",
    max_entries=100,  # Maximum messages to store
    auto_summarize=True,  # Enable automatic summarization
    compression_threshold=50  # Compress when over 50 messages
)
```

## 🎓 Knowledge Memory

**Purpose**: Stores domain-specific knowledge, learning patterns, and insights.

### Key Features

- **Concept Knowledge**: Understanding of framework concepts
- **Learning Patterns**: Identified learning behaviors and preferences
- **Insights**: Generated insights from learning interactions
- **Cross-session Persistence**: Knowledge persists across sessions

### Usage Examples

```python
from gaapf.core.memory_systems import KnowledgeMemory

# Initialize knowledge memory
knowledge_memory = KnowledgeMemory()

# Add concept knowledge
knowledge_memory.add_concept_knowledge(
    concept_id="langchain_chains",
    framework="langchain",
    knowledge_data={
        "definition": "Chains are sequences of calls to components",
        "types": ["SimpleChain", "SequentialChain", "RouterChain"],
        "use_cases": ["Data processing", "Multi-step reasoning"],
        "complexity_level": "intermediate"
    },
    confidence=0.95
)

# Add learning pattern
knowledge_memory.add_learning_pattern(
    pattern_type="difficulty_progression",
    pattern_data={
        "user_preference": "gradual_increase",
        "optimal_step_size": 0.2,
        "success_indicators": ["completion_rate", "engagement"]
    },
    user_context={"user_id": "learner_123", "framework": "langchain"}
)

# Add insight
knowledge_memory.add_insight(
    insight="Users learn LangChain concepts better with visual examples",
    context={
        "framework": "langchain",
        "evidence": "85% higher comprehension with diagrams",
        "sample_size": 150
    }
)

# Retrieve knowledge
concept_knowledge = knowledge_memory.get_concept_knowledge(
    concept_id="langchain_chains",
    framework="langchain"
)

# Get learning patterns
patterns = knowledge_memory.get_patterns("difficulty_progression")
for pattern in patterns:
    print(f"Pattern: {pattern.content['pattern_data']}")

# Search knowledge
search_results = knowledge_memory.search_knowledge(
    query="visual learning",
    framework="langchain"
)
```

### Knowledge Categories

1. **Concept Knowledge**
   - Framework-specific concepts
   - Relationships between concepts
   - Difficulty assessments
   - Learning objectives

2. **Learning Patterns**
   - User behavior patterns
   - Effective learning sequences
   - Common misconceptions
   - Success indicators

3. **Insights**
   - Generated insights from interactions
   - Best practices discovered
   - Common problem solutions
   - Optimization recommendations

## 👤 User Memory

**Purpose**: Manages long-term user-specific information and learning history.

### Key Features

- **User Profiles**: Personal information and preferences
- **Learning History**: Complete learning journey tracking
- **Goals and Achievements**: Personal learning objectives
- **Performance Metrics**: Detailed progress analytics

### Usage Examples

```python
from gaapf.core.memory_systems import UserMemory

# Initialize user memory
user_memory = UserMemory("user_123")

# Update user profile
user_memory.update_profile({
    "name": "Alice Johnson",
    "skill_level": "intermediate",
    "programming_experience": 3,
    "preferred_frameworks": ["langchain", "langgraph"]
})

# Update preferences
user_memory.update_preferences({
    "learning_style": "visual",
    "pace": "moderate",
    "session_length": 45,  # minutes
    "difficulty_preference": "gradual_increase"
})

# Add learning session
user_memory.add_learning_session({
    "session_id": "session_456",
    "framework": "langchain",
    "module_id": "lc_chains",
    "duration": 2700,  # 45 minutes
    "completion_rate": 0.85,
    "comprehension_score": 0.78,
    "engagement_level": 0.92
})

# Add learning goal
user_memory.add_goal(
    goal="Master LangChain agent development",
    target_date="2024-06-01",
    framework="langchain"
)

# Add achievement
user_memory.add_achievement(
    achievement="Completed first LangChain project",
    context={
        "project_type": "chatbot",
        "complexity": "intermediate",
        "completion_date": "2024-01-15"
    }
)

# Record performance metric
user_memory.record_performance_metric(
    metric_type="comprehension_score",
    value=0.85,
    context={"module": "lc_chains", "attempt": 1}
)

# Get learning summary
summary = user_memory.get_learning_summary(days=30)
print(f"Sessions in last 30 days: {summary['sessions_count']}")
print(f"Total learning time: {summary['total_time_hours']} hours")
print(f"Frameworks studied: {summary['frameworks_studied']}")
```

### User Data Categories

1. **Profile Information**
   - Basic demographics
   - Technical background
   - Learning preferences
   - Accessibility needs

2. **Learning History**
   - Session records
   - Module completions
   - Progress tracking
   - Time investments

3. **Goals and Achievements**
   - Learning objectives
   - Milestone tracking
   - Achievement records
   - Certification progress

4. **Performance Metrics**
   - Comprehension scores
   - Engagement levels
   - Completion rates
   - Improvement trends

## 🔗 Integrated Memory Manager

**Purpose**: Provides unified access to all memory tiers with intelligent coordination.

### Usage Examples

```python
from gaapf.core.memory_systems import IntegratedMemoryManager

# Initialize integrated memory manager
memory_manager = IntegratedMemoryManager()

# Get specific memory instances
conv_memory = memory_manager.get_conversation_memory("session_123")
user_memory = memory_manager.get_user_memory("user_456")
knowledge_memory = memory_manager.get_knowledge_memory()

# Search across all memory tiers
search_results = memory_manager.search_all_memories(
    query="LangChain chains",
    user_id="user_456",
    session_id="session_123"
)

print("Conversation results:", search_results["conversation"])
print("Knowledge results:", search_results["knowledge"])
print("User results:", search_results["user"])

# Save all memory tiers
memory_manager.save_all()

# Get memory statistics
stats = memory_manager.get_memory_statistics()
print(f"Active conversations: {stats['active_conversations']}")
print(f"Total users: {stats['total_users']}")
print(f"Knowledge concepts: {stats['knowledge_concepts']}")
```

### Memory Coordination

The integrated manager provides:

1. **Unified Search**: Search across all memory tiers
2. **Automatic Persistence**: Coordinated saving and loading
3. **Memory Cleanup**: Intelligent cleanup of old data
4. **Performance Optimization**: Caching and optimization

## ⚙️ Configuration and Optimization

### Memory Configuration

```python
# Configure memory systems in .env
MEMORY_TTL_HOURS=24              # Time-to-live for cached data
CACHE_SIZE_LIMIT=1000            # Maximum cache entries
AUTO_SAVE_INTERVAL=300           # Auto-save interval in seconds
CONVERSATION_MAX_ENTRIES=100     # Max conversation entries
KNOWLEDGE_COMPRESSION=true       # Enable knowledge compression
USER_DATA_ENCRYPTION=true        # Encrypt sensitive user data
```

### Performance Optimization

```python
# Optimize memory performance
memory_manager = IntegratedMemoryManager()

# Configure cache settings
memory_manager.configure_cache(
    max_size=2000,
    ttl_hours=48,
    compression_enabled=True
)

# Enable batch operations
memory_manager.enable_batch_mode()

# Batch multiple operations
with memory_manager.batch_context():
    conv_memory.add_message(...)
    user_memory.add_learning_session(...)
    knowledge_memory.add_insight(...)
# All operations committed together
```

### Memory Analytics

```python
# Monitor memory usage and performance
from gaapf.core.analytics_system import get_analytics_engine

analytics = get_analytics_engine()

# Track memory metrics
memory_metrics = analytics.get_memory_metrics()
print(f"Memory usage: {memory_metrics['total_size_mb']} MB")
print(f"Cache hit rate: {memory_metrics['cache_hit_rate']}%")
print(f"Average retrieval time: {memory_metrics['avg_retrieval_ms']} ms")

# Memory performance alerts
if memory_metrics['cache_hit_rate'] < 80:
    print("Warning: Low cache hit rate - consider increasing cache size")

if memory_metrics['total_size_mb'] > 500:
    print("Warning: High memory usage - consider cleanup")
```

## 🔒 Privacy and Security

### Data Protection

1. **Encryption**: Sensitive user data is encrypted at rest
2. **Access Control**: Role-based access to memory systems
3. **Data Retention**: Configurable retention policies
4. **Anonymization**: Option to anonymize learning data

### Privacy Configuration

```python
# Configure privacy settings
memory_manager = IntegratedMemoryManager()

memory_manager.configure_privacy(
    encrypt_user_data=True,
    anonymize_conversations=False,
    data_retention_days=365,
    allow_analytics=True
)

# Export user data (GDPR compliance)
user_data = memory_manager.export_user_data("user_123")

# Delete user data
memory_manager.delete_user_data("user_123", confirm=True)
```

## 🛠️ Custom Memory Extensions

### Creating Custom Memory Types

```python
from gaapf.core.memory_systems import MemoryEntry

class ProjectMemory:
    """Custom memory for project-specific information."""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.project_data = {}
        self.milestones = []
        self.resources = []
    
    def add_milestone(self, milestone: str, completion_date: str):
        """Add project milestone."""
        entry = MemoryEntry(
            entry_id=f"milestone_{len(self.milestones)}",
            content={
                "milestone": milestone,
                "completion_date": completion_date,
                "project_id": self.project_id
            },
            metadata={"type": "milestone"}
        )
        self.milestones.append(entry)
    
    def get_project_summary(self) -> Dict[str, Any]:
        """Get project summary."""
        return {
            "project_id": self.project_id,
            "total_milestones": len(self.milestones),
            "completed_milestones": len([m for m in self.milestones if m.content.get("completed", False)]),
            "resources_count": len(self.resources)
        }
```

### Integration with Existing Systems

```python
# Integrate custom memory with GAAPF
from gaapf.core.memory_systems import IntegratedMemoryManager

class ExtendedMemoryManager(IntegratedMemoryManager):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.project_memories = {}
    
    def get_project_memory(self, project_id: str) -> ProjectMemory:
        """Get or create project memory."""
        if project_id not in self.project_memories:
            self.project_memories[project_id] = ProjectMemory(project_id)
        return self.project_memories[project_id]
    
    def search_all_memories(self, query: str, **kwargs) -> Dict[str, List[Any]]:
        """Extended search including project memories."""
        results = super().search_all_memories(query, **kwargs)
        
        # Add project memory search
        project_results = []
        for project_memory in self.project_memories.values():
            # Implement project-specific search logic
            pass
        
        results["projects"] = project_results
        return results
```

## 📊 Memory Monitoring and Debugging

### Debug Tools

```python
# Enable memory debugging
import logging
logging.getLogger('gaapf.memory').setLevel(logging.DEBUG)

# Memory inspection tools
memory_manager = IntegratedMemoryManager()

# Inspect memory state
print("Memory State:")
print(f"Conversations: {len(memory_manager.conversation_memories)}")
print(f"Users: {len(memory_manager.user_memories)}")

# Memory health check
health_status = memory_manager.health_check()
print(f"Memory health: {health_status['status']}")
if health_status['issues']:
    print("Issues found:")
    for issue in health_status['issues']:
        print(f"- {issue}")

# Memory cleanup
cleanup_results = memory_manager.cleanup_old_data(days=30)
print(f"Cleaned up {cleanup_results['entries_removed']} old entries")
```

### Performance Profiling

```python
# Profile memory operations
import time
from contextlib import contextmanager

@contextmanager
def profile_memory_operation(operation_name: str):
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        print(f"{operation_name} took {duration:.3f} seconds")

# Profile memory operations
with profile_memory_operation("Add conversation message"):
    conv_memory.add_message("user", "Test message")

with profile_memory_operation("Search knowledge"):
    results = knowledge_memory.search_knowledge("LangChain")
```

This comprehensive guide provides everything needed to understand, use, and customize GAAPF's memory systems effectively.
