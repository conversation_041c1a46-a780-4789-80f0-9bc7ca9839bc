# GAAPF Installation and Deployment Guide

This comprehensive guide covers installation, configuration, and deployment of GAAPF in various environments.

## 📋 System Requirements

### Minimum Requirements

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 2GB free space for installation and data
- **Network**: Internet connection for LLM API access

### Recommended Requirements

- **Python**: 3.10 or higher
- **Memory**: 16GB RAM for optimal performance
- **Storage**: 10GB free space (includes analytics data and caching)
- **CPU**: Multi-core processor for real-time analytics
- **Network**: Stable broadband connection

### Dependencies

- **Core Dependencies**: Listed in `requirements.txt`
- **Optional Dependencies**: Listed in `requirements-optional.txt`
- **Development Dependencies**: Listed in `requirements-dev.txt`
- **Testing Dependencies**: Listed in `requirements-test.txt`

## 🚀 Quick Installation

### Option 1: Standard Installation

```bash
# Clone the repository
git clone https://github.com/your-org/gaapf.git
cd gaapf

# Create virtual environment
python -m venv gaapf-env
source gaapf-env/bin/activate  # On Windows: gaapf-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Initialize GAAPF
python -m gaapf.interfaces.cli.app
```

### Option 2: Development Installation

```bash
# Clone with development setup
git clone https://github.com/your-org/gaapf.git
cd gaapf

# Create development environment
python -m venv gaapf-dev-env
source gaapf-dev-env/bin/activate

# Install all dependencies including development tools
pip install -r requirements.txt
pip install -r requirements-dev.txt
pip install -r requirements-test.txt

# Install pre-commit hooks
pre-commit install

# Run tests to verify installation
python tests/run_tests.py
```

### Option 3: Docker Installation

```bash
# Using Docker (coming soon)
docker pull gaapf/gaapf:latest
docker run -it --env-file .env gaapf/gaapf:latest
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the project root:

```bash
# LLM Provider Configuration
TOGETHER_API_KEY=your_together_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_LLM_PROVIDER=together

# System Configuration
GAAPF_DATA_PATH=./data
GAAPF_LOG_LEVEL=INFO
GAAPF_MAX_SESSIONS=10
GAAPF_ENV=production

# Analytics Configuration
ANALYTICS_ENABLED=true
METRICS_COLLECTION_INTERVAL=60
DASHBOARD_REFRESH_RATE=30
PERFORMANCE_MONITORING=true

# Memory Configuration
MEMORY_TTL_HOURS=24
CACHE_SIZE_LIMIT=1000
AUTO_SAVE_INTERVAL=300
CONVERSATION_MAX_ENTRIES=100

# Framework Configuration
DEFAULT_FRAMEWORK=langchain
AUTO_DETECT_SKILL_LEVEL=true
PERSONALIZATION_ENABLED=true

# Security Configuration
USER_DATA_ENCRYPTION=true
SESSION_TIMEOUT_MINUTES=60
API_RATE_LIMITING=true

# Database Configuration
DB_CONNECTION_POOL_SIZE=10
DB_QUERY_TIMEOUT=30
DB_BACKUP_ENABLED=true

# Logging Configuration
LOG_FILE_PATH=./logs/gaapf.log
LOG_ROTATION_SIZE=10MB
LOG_RETENTION_DAYS=30
```

### API Key Setup

#### Together AI (Recommended)

1. Visit [Together AI](https://together.ai)
2. Create an account and get your API key
3. Add to `.env`: `TOGETHER_API_KEY=your_key_here`

#### OpenAI (Alternative)

1. Visit [OpenAI Platform](https://platform.openai.com)
2. Create an account and get your API key
3. Add to `.env`: `OPENAI_API_KEY=your_key_here`

### Advanced Configuration

#### Custom Framework Configuration

Edit `gaapf/config/framework_configs.py`:

```python
# Add custom framework
CUSTOM_FRAMEWORK_CONFIG = FrameworkConfig(
    name="Custom Framework",
    description="Your custom AI framework",
    version="1.0.0",
    documentation_url="https://docs.custom-framework.com",
    modules=[
        # Add custom modules
    ]
)

# Register in DEFAULT_FRAMEWORK_CONFIGS
DEFAULT_FRAMEWORK_CONFIGS["custom_framework"] = CUSTOM_FRAMEWORK_CONFIG
```

#### Agent Configuration

Edit `gaapf/config/agent_configs.py`:

```python
AGENT_CONFIGS = {
    "instructor": {
        "confidence_threshold": 0.7,
        "max_response_length": 1000,
        "preferred_style": "educational",
        "enable_examples": True
    },
    "code_assistant": {
        "confidence_threshold": 0.8,
        "include_comments": True,
        "code_style": "pythonic",
        "max_code_length": 500
    }
}
```

## 🐳 Docker Deployment

### Dockerfile

```dockerfile
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY . .

# Create data directory
RUN mkdir -p /app/data

# Set environment variables
ENV PYTHONPATH=/app
ENV GAAPF_DATA_PATH=/app/data

# Expose port (if adding web interface)
EXPOSE 8000

# Run GAAPF
CMD ["python", "-m", "gaapf.interfaces.cli.app"]
```

### Docker Compose

```yaml
version: '3.8'

services:
  gaapf:
    build: .
    environment:
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEFAULT_LLM_PROVIDER=together
      - GAAPF_DATA_PATH=/app/data
      - ANALYTICS_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    
  # Optional: Add database for production
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=gaapf
      - POSTGRES_USER=gaapf
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

## ☁️ Cloud Deployment

### AWS Deployment

#### EC2 Instance Setup

```bash
# Launch EC2 instance (t3.medium or larger recommended)
# Connect to instance

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.10
sudo apt install python3.10 python3.10-venv python3.10-dev -y

# Clone and setup GAAPF
git clone https://github.com/your-org/gaapf.git
cd gaapf
python3.10 -m venv gaapf-env
source gaapf-env/bin/activate
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your configuration

# Setup systemd service
sudo cp deployment/gaapf.service /etc/systemd/system/
sudo systemctl enable gaapf
sudo systemctl start gaapf
```

#### ECS Deployment

```json
{
  "family": "gaapf-task",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "gaapf",
      "image": "your-account.dkr.ecr.region.amazonaws.com/gaapf:latest",
      "essential": true,
      "environment": [
        {"name": "TOGETHER_API_KEY", "value": "your-key"},
        {"name": "DEFAULT_LLM_PROVIDER", "value": "together"},
        {"name": "GAAPF_DATA_PATH", "value": "/app/data"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/gaapf",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Platform

#### Cloud Run Deployment

```yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: gaapf
spec:
  template:
    metadata:
      annotations:
        autoscaling.knative.dev/maxScale: "10"
        run.googleapis.com/memory: "2Gi"
        run.googleapis.com/cpu: "1"
    spec:
      containers:
      - image: gcr.io/your-project/gaapf:latest
        env:
        - name: TOGETHER_API_KEY
          valueFrom:
            secretKeyRef:
              name: gaapf-secrets
              key: together-api-key
        - name: DEFAULT_LLM_PROVIDER
          value: "together"
        resources:
          limits:
            memory: "2Gi"
            cpu: "1"
```

### Azure Deployment

#### Container Instances

```bash
# Create resource group
az group create --name gaapf-rg --location eastus

# Create container instance
az container create \
  --resource-group gaapf-rg \
  --name gaapf-instance \
  --image your-registry/gaapf:latest \
  --cpu 2 \
  --memory 4 \
  --environment-variables \
    TOGETHER_API_KEY=your-key \
    DEFAULT_LLM_PROVIDER=together \
  --restart-policy Always
```

## 🔧 Production Configuration

### Performance Optimization

```bash
# .env for production
GAAPF_ENV=production
ANALYTICS_ENABLED=true
CACHE_SIZE_LIMIT=5000
MEMORY_TTL_HOURS=48
DB_CONNECTION_POOL_SIZE=20
PERFORMANCE_MONITORING=true

# Enable compression
RESPONSE_COMPRESSION=true
STATIC_FILE_CACHING=true

# Optimize logging
LOG_LEVEL=WARNING
LOG_ASYNC=true
```

### Security Hardening

```bash
# Security settings
USER_DATA_ENCRYPTION=true
API_RATE_LIMITING=true
SESSION_TIMEOUT_MINUTES=30
SECURE_COOKIES=true
CSRF_PROTECTION=true

# Network security
ALLOWED_HOSTS=your-domain.com,api.your-domain.com
CORS_ALLOWED_ORIGINS=https://your-frontend.com

# Database security
DB_SSL_MODE=require
DB_CONNECTION_ENCRYPTION=true
```

### Monitoring and Logging

#### Prometheus Metrics

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'gaapf'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

#### Log Aggregation

```bash
# Using ELK Stack
# Logstash configuration for GAAPF logs
input {
  file {
    path => "/app/logs/gaapf.log"
    start_position => "beginning"
  }
}

filter {
  json {
    source => "message"
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "gaapf-logs-%{+YYYY.MM.dd}"
  }
}
```

## 🔍 Troubleshooting

### Common Installation Issues

#### Issue: Python Version Compatibility

```bash
Error: Python 3.7 is not supported

Solution:
# Install Python 3.8+
sudo apt install python3.8 python3.8-venv
python3.8 -m venv gaapf-env
```

#### Issue: Memory Errors

```bash
Error: MemoryError during analytics processing

Solution:
# Reduce cache size in .env
CACHE_SIZE_LIMIT=500
MEMORY_TTL_HOURS=12

# Or increase system memory
# Recommended: 8GB+ RAM
```

#### Issue: API Key Problems

```bash
Error: Invalid API key or quota exceeded

Solution:
1. Verify API key is correct in .env
2. Check API key has sufficient credits
3. Verify network connectivity
4. Try alternative provider
```

### Performance Issues

#### Slow Response Times

```bash
# Check system resources
htop
df -h

# Optimize configuration
CACHE_SIZE_LIMIT=2000
DB_CONNECTION_POOL_SIZE=15
ANALYTICS_PROCESSING_THREADS=4
```

#### High Memory Usage

```bash
# Monitor memory usage
python -c "
import psutil
print(f'Memory usage: {psutil.virtual_memory().percent}%')
"

# Optimize memory settings
MEMORY_TTL_HOURS=6
CONVERSATION_MAX_ENTRIES=50
AUTO_CLEANUP_ENABLED=true
```

### Database Issues

#### Connection Problems

```bash
Error: Database connection failed

Solution:
1. Check database is running
2. Verify connection parameters
3. Check network connectivity
4. Increase connection timeout
```

#### Performance Issues

```bash
# Optimize database
# Add indexes for frequently queried fields
# Increase connection pool size
# Enable query caching
```

## 📊 Health Checks

### System Health Check

```python
# health_check.py
import asyncio
from gaapf.core.learning_hub import LearningHubCore
from gaapf.core.analytics_system import get_analytics_engine

async def health_check():
    try:
        # Check learning hub
        hub = LearningHubCore()
        await hub.initialize()
        
        # Check analytics
        analytics = get_analytics_engine()
        health = analytics.health_check()
        
        # Check database
        from gaapf.core.framework_database import get_framework_database
        db = get_framework_database()
        stats = db.get_database_statistics()
        
        print("✅ All systems healthy")
        return True
        
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(health_check())
```

### Monitoring Script

```bash
#!/bin/bash
# monitor.sh

# Check GAAPF process
if pgrep -f "gaapf" > /dev/null; then
    echo "✅ GAAPF process running"
else
    echo "❌ GAAPF process not found"
    # Restart service
    systemctl restart gaapf
fi

# Check disk space
DISK_USAGE=$(df /app/data | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "⚠️ Disk usage high: ${DISK_USAGE}%"
fi

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEMORY_USAGE -gt 85 ]; then
    echo "⚠️ Memory usage high: ${MEMORY_USAGE}%"
fi
```

## 🔄 Updates and Maintenance

### Updating GAAPF

```bash
# Backup current installation
cp -r gaapf gaapf-backup-$(date +%Y%m%d)

# Pull latest changes
cd gaapf
git pull origin main

# Update dependencies
pip install -r requirements.txt --upgrade

# Run database migrations (if any)
python scripts/migrate_database.py

# Restart service
systemctl restart gaapf
```

### Backup and Recovery

```bash
# Backup script
#!/bin/bash
BACKUP_DIR="/backups/gaapf-$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_DIR

# Backup data
cp -r /app/data $BACKUP_DIR/
cp .env $BACKUP_DIR/

# Backup database
pg_dump gaapf > $BACKUP_DIR/database.sql

echo "Backup completed: $BACKUP_DIR"
```

This comprehensive installation guide covers all aspects of deploying GAAPF from development to production environments.
