# GAAPF Environment Configuration
# Copy this file to .env and fill in your API keys

# =============================================================================
# LLM API Keys (At least one is required)
# =============================================================================

# OpenAI API Key (for GPT models)
# Get your key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Google Gemini API Key (recommended - has free tier)
# Get your key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Anthropic Claude API Key
# Get your key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Together AI API Key (cost-effective option with many open-source models)
# Get your key from: https://api.together.xyz/settings/api-keys
TOGETHER_API_KEY=your_together_api_key_here

# =============================================================================
# Optional Tool API Keys
# =============================================================================

# Tavily Search API Key (for enhanced search capabilities)
# Get your key from: https://tavily.com/
TAVILY_API_KEY=your_tavily_api_key_here

# =============================================================================
# Configuration Options
# =============================================================================

# Default LLM provider to use (openai, google, anthropic, together)
DEFAULT_LLM_PROVIDER=google

# Default model for each provider
OPENAI_MODEL=gpt-4-turbo
GOOGLE_MODEL=gemini-1.5-pro
ANTHROPIC_MODEL=claude-3-sonnet-20240229
TOGETHER_MODEL=meta-llama/Llama-3.3-70B-Instruct-Turbo-Free

# Data directory path (relative to project root)
GAAPF_DATA_PATH=data

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# =============================================================================
# Advanced Configuration (Optional)
# =============================================================================

# Maximum tokens for LLM responses
MAX_TOKENS=4000

# Temperature for LLM responses (0.0 to 1.0)
LLM_TEMPERATURE=0.2

# Timeout for LLM requests (seconds)
LLM_TIMEOUT=60

# Enable debug mode (true/false)
DEBUG_MODE=false
