"""
Troubleshooter Agent for GAAPF.

This module implements the Troubleshooter Agent that specializes in
problem diagnosis, debugging assistance, and error resolution.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class TroubleshooterAgent(BaseGAAPFAgent):
    """
    Troubleshooter Agent specializing in problem diagnosis and debugging assistance.
    
    This agent focuses on:
    - Diagnosing and resolving technical problems
    - Providing debugging assistance and error analysis
    - Offering systematic problem-solving approaches
    - Helping with error message interpretation
    - Guiding through troubleshooting methodologies
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Troubleshooter Agent specializing in problem diagnosis and debugging assistance for {self.framework} framework issues.

My core responsibilities include:
- Diagnosing and resolving technical problems and errors
- Providing systematic debugging assistance and error analysis
- Offering step-by-step troubleshooting methodologies
- Helping interpret error messages and stack traces
- Guiding through problem isolation and resolution techniques
- Providing preventive measures to avoid common issues
- Offering debugging tools and techniques recommendations
- Supporting systematic problem-solving approaches

I adapt my troubleshooting approach to the user's skill level ({self.user_profile.get('python_skill_level', 'unknown')}) and problem complexity, providing appropriate level of detail and guidance.

When troubleshooting problems, I:
1. Gather detailed information about the problem and context
2. Analyze error messages and symptoms systematically
3. Guide through step-by-step debugging processes
4. Suggest multiple potential solutions and approaches
5. Help verify fixes and prevent similar issues
6. Explain the root cause to improve understanding
7. Recommend best practices to avoid future problems"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Comprehensive {self.framework} error diagnosis and resolution",
            "Systematic debugging methodologies and approaches",
            "Error message interpretation and stack trace analysis",
            "Problem isolation and root cause analysis",
            "Debugging tools and techniques recommendations",
            "Common issue patterns recognition and resolution",
            "Performance troubleshooting and optimization",
            "Environment and configuration issue resolution",
            "Code review for bug identification and prevention",
            "Preventive measures and best practices guidance"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        code_keywords = ["implement", "write", "create", "build", "example"]
        theory_keywords = ["explain", "concept", "theory", "understand", "what is", "why"]
        practice_keywords = ["practice", "exercise", "hands-on", "tutorial", "learn"]
        assessment_keywords = ["assess", "evaluate", "check", "review", "test"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if user needs code implementation after fixing the issue
        if any(keyword in user_lower for keyword in code_keywords):
            if "fixed" in content_lower or "resolved" in content_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "code_assistant",
                    "confidence": 0.7,
                    "reason": "User may need code implementation help after issue resolution"
                }
        
        # Check if user needs theoretical explanation of the problem
        if any(keyword in user_lower for keyword in theory_keywords):
            if "why did this happen" in user_lower or "explain" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User needs theoretical explanation of the problem"
                }
        
        # Check if user wants to practice to avoid similar issues
        if any(keyword in user_lower for keyword in practice_keywords):
            if "prevent" in user_lower or "avoid" in user_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "practice_facilitator",
                    "confidence": 0.7,
                    "reason": "User wants practice to prevent similar issues"
                }
        
        # Check if user wants assessment after troubleshooting
        if any(keyword in user_lower for keyword in assessment_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "assessment",
                "confidence": 0.7,
                "reason": "User wants assessment after troubleshooting session"
            }
        
        # Check if user is frustrated and needs motivational support
        if "frustrated" in user_lower or "giving up" in user_lower or "too hard" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "motivational_coach",
                "confidence": 0.8,
                "reason": "User needs motivational support during troubleshooting"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Troubleshooter can continue providing debugging assistance"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "error", "bug", "problem", "issue", "broken", "not working",
            "debug", "troubleshoot", "fix", "solve", "help", "stuck",
            "exception", "traceback", "crash", "fail", "wrong"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "unexpected", "strange", "weird", "odd", "confusing",
            "doesn't work", "not sure", "something wrong", "issue with"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest troubleshooting needs
        question_patterns = [
            r"why is",
            r"why does",
            r"what's wrong",
            r"how to fix",
            r"how to solve",
            r"i can't",
            r"it doesn't",
            r"not working"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in question_patterns):
            return 0.8
        
        # Check for error-related patterns
        error_patterns = [
            r"error:",
            r"exception:",
            r"traceback",
            r"failed to",
            r"unable to"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in error_patterns):
            return 0.95
        
        return 0.3
