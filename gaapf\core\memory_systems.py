"""
Enhanced Memory Systems for GAAPF.

This module implements the three-tier memory system: ConversationMemory, 
KnowledgeMemory, and UserMemory with proper integration to vinagent's memory capabilities.
"""

import json
import time
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from collections import defaultdict, deque
from abc import ABC, abstractmethod
import hashlib

# Import vinagent memory capabilities
try:
    from vinagent.memory.base import BaseMemory
    from vinagent.memory.conversation import ConversationMemory as VinagentConversationMemory
    VINAGENT_AVAILABLE = True
except ImportError:
    VINAGENT_AVAILABLE = False
    BaseMemory = object
    VinagentConversationMemory = object


class MemoryEntry:
    """Base class for memory entries."""
    
    def __init__(
        self,
        entry_id: str,
        content: Any,
        timestamp: float = None,
        metadata: Dict[str, Any] = None
    ):
        self.entry_id = entry_id
        self.content = content
        self.timestamp = timestamp or time.time()
        self.metadata = metadata or {}
        self.access_count = 0
        self.last_accessed = self.timestamp
    
    def access(self):
        """Mark this entry as accessed."""
        self.access_count += 1
        self.last_accessed = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "entry_id": self.entry_id,
            "content": self.content,
            "timestamp": self.timestamp,
            "metadata": self.metadata,
            "access_count": self.access_count,
            "last_accessed": self.last_accessed
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryEntry':
        """Create from dictionary representation."""
        entry = cls(
            entry_id=data["entry_id"],
            content=data["content"],
            timestamp=data.get("timestamp", time.time()),
            metadata=data.get("metadata", {})
        )
        entry.access_count = data.get("access_count", 0)
        entry.last_accessed = data.get("last_accessed", entry.timestamp)
        return entry


class ConversationMemory:
    """
    Manages conversation history and context within sessions.
    
    This memory tier handles:
    - Short-term conversation context
    - Agent handoff history
    - Session-specific interactions
    - Real-time context maintenance
    """
    
    def __init__(self, session_id: str, max_entries: int = 100):
        """
        Initialize conversation memory.
        
        Args:
            session_id: Unique session identifier
            max_entries: Maximum number of entries to keep
        """
        self.session_id = session_id
        self.max_entries = max_entries
        self.entries = deque(maxlen=max_entries)
        self.agent_history = []
        self.context_summary = ""
        
        # Integration with vinagent if available
        self.vinagent_memory = None
        if VINAGENT_AVAILABLE:
            try:
                self.vinagent_memory = VinagentConversationMemory()
            except Exception:
                pass
    
    def add_message(
        self, 
        role: str, 
        content: str, 
        agent_type: str = None,
        metadata: Dict[str, Any] = None
    ):
        """
        Add a message to conversation memory.
        
        Args:
            role: Message role (user, assistant, system)
            content: Message content
            agent_type: Type of agent that generated the message
            metadata: Additional metadata
        """
        entry_id = f"{self.session_id}_{len(self.entries)}"
        
        message_data = {
            "role": role,
            "content": content,
            "agent_type": agent_type,
            "session_id": self.session_id
        }
        
        entry = MemoryEntry(
            entry_id=entry_id,
            content=message_data,
            metadata=metadata or {}
        )
        
        self.entries.append(entry)
        
        # Track agent usage
        if agent_type and agent_type not in self.agent_history:
            self.agent_history.append(agent_type)
        
        # Update vinagent memory if available
        if self.vinagent_memory:
            try:
                self.vinagent_memory.add_message(role, content)
            except Exception:
                pass
    
    def get_recent_messages(self, count: int = 10) -> List[Dict[str, Any]]:
        """Get recent messages from conversation."""
        recent = list(self.entries)[-count:]
        return [entry.content for entry in recent]
    
    def get_context_summary(self) -> str:
        """Get a summary of the conversation context."""
        if not self.entries:
            return "No conversation history"
        
        # Simple summary - in production, this could use LLM summarization
        message_count = len(self.entries)
        agents_used = len(set(self.agent_history))
        
        recent_topics = []
        for entry in list(self.entries)[-5:]:  # Last 5 messages
            content = entry.content.get("content", "")
            # Extract key topics (simplified)
            if len(content) > 50:
                recent_topics.append(content[:50] + "...")
        
        summary = f"Session with {message_count} messages, {agents_used} agents used."
        if recent_topics:
            summary += f" Recent topics: {'; '.join(recent_topics)}"
        
        return summary
    
    def search_messages(self, query: str, limit: int = 5) -> List[MemoryEntry]:
        """Search messages by content."""
        query_lower = query.lower()
        matches = []
        
        for entry in self.entries:
            content = entry.content.get("content", "").lower()
            if query_lower in content:
                entry.access()
                matches.append(entry)
        
        return matches[-limit:]  # Return most recent matches
    
    def clear(self):
        """Clear conversation memory."""
        self.entries.clear()
        self.agent_history.clear()
        self.context_summary = ""


class KnowledgeMemory:
    """
    Manages learned concepts, patterns, and domain knowledge.
    
    This memory tier handles:
    - Concept understanding and relationships
    - Learning patterns and insights
    - Framework-specific knowledge
    - Cross-session knowledge retention
    """
    
    def __init__(self, data_path: Path = Path("data/knowledge_memory")):
        """
        Initialize knowledge memory.
        
        Args:
            data_path: Path to store knowledge memory data
        """
        self.data_path = data_path
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # Knowledge storage
        self.concepts = {}  # concept_id -> knowledge_entry
        self.patterns = defaultdict(list)  # pattern_type -> [pattern_entries]
        self.insights = []  # learning insights
        self.framework_knowledge = defaultdict(dict)  # framework -> knowledge
        
        # Load existing knowledge
        self._load_knowledge()
    
    def add_concept_knowledge(
        self, 
        concept_id: str, 
        framework: str,
        knowledge_data: Dict[str, Any],
        confidence: float = 1.0
    ):
        """
        Add knowledge about a concept.
        
        Args:
            concept_id: Concept identifier
            framework: Framework context
            knowledge_data: Knowledge information
            confidence: Confidence in the knowledge (0.0 to 1.0)
        """
        entry_id = f"concept_{concept_id}_{framework}"
        
        entry = MemoryEntry(
            entry_id=entry_id,
            content={
                "concept_id": concept_id,
                "framework": framework,
                "knowledge": knowledge_data,
                "confidence": confidence
            },
            metadata={"type": "concept_knowledge"}
        )
        
        self.concepts[entry_id] = entry
        
        # Also store in framework-specific knowledge
        if concept_id not in self.framework_knowledge[framework]:
            self.framework_knowledge[framework][concept_id] = []
        self.framework_knowledge[framework][concept_id].append(entry)
    
    def add_learning_pattern(
        self, 
        pattern_type: str, 
        pattern_data: Dict[str, Any],
        user_context: Dict[str, Any] = None
    ):
        """
        Add a learning pattern observation.
        
        Args:
            pattern_type: Type of pattern (e.g., "difficulty_progression", "learning_style")
            pattern_data: Pattern information
            user_context: User context when pattern was observed
        """
        entry_id = f"pattern_{pattern_type}_{int(time.time())}"
        
        entry = MemoryEntry(
            entry_id=entry_id,
            content={
                "pattern_type": pattern_type,
                "pattern_data": pattern_data,
                "user_context": user_context or {}
            },
            metadata={"type": "learning_pattern"}
        )
        
        self.patterns[pattern_type].append(entry)
    
    def add_insight(self, insight: str, context: Dict[str, Any] = None):
        """
        Add a learning insight.
        
        Args:
            insight: Insight description
            context: Context in which insight was discovered
        """
        entry_id = f"insight_{int(time.time())}"
        
        entry = MemoryEntry(
            entry_id=entry_id,
            content={
                "insight": insight,
                "context": context or {}
            },
            metadata={"type": "insight"}
        )
        
        self.insights.append(entry)
    
    def get_concept_knowledge(self, concept_id: str, framework: str = None) -> List[MemoryEntry]:
        """Get knowledge about a specific concept."""
        matches = []
        
        for entry_id, entry in self.concepts.items():
            content = entry.content
            if content["concept_id"] == concept_id:
                if framework is None or content["framework"] == framework:
                    entry.access()
                    matches.append(entry)
        
        return matches
    
    def get_patterns(self, pattern_type: str = None) -> List[MemoryEntry]:
        """Get learning patterns."""
        if pattern_type:
            return self.patterns.get(pattern_type, [])
        
        all_patterns = []
        for pattern_list in self.patterns.values():
            all_patterns.extend(pattern_list)
        return all_patterns
    
    def search_knowledge(self, query: str, framework: str = None) -> List[MemoryEntry]:
        """Search knowledge by query."""
        query_lower = query.lower()
        matches = []
        
        # Search concepts
        for entry in self.concepts.values():
            content_str = json.dumps(entry.content).lower()
            if query_lower in content_str:
                if framework is None or entry.content.get("framework") == framework:
                    entry.access()
                    matches.append(entry)
        
        # Search insights
        for entry in self.insights:
            insight_str = entry.content.get("insight", "").lower()
            if query_lower in insight_str:
                entry.access()
                matches.append(entry)
        
        return matches
    
    def _load_knowledge(self):
        """Load knowledge from disk."""
        try:
            # Load concepts
            concepts_file = self.data_path / "concepts.json"
            if concepts_file.exists():
                with open(concepts_file, 'r') as f:
                    concepts_data = json.load(f)
                    for entry_id, entry_dict in concepts_data.items():
                        self.concepts[entry_id] = MemoryEntry.from_dict(entry_dict)
            
            # Load patterns
            patterns_file = self.data_path / "patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r') as f:
                    patterns_data = json.load(f)
                    for pattern_type, entries_data in patterns_data.items():
                        self.patterns[pattern_type] = [
                            MemoryEntry.from_dict(entry_dict) for entry_dict in entries_data
                        ]
            
            # Load insights
            insights_file = self.data_path / "insights.json"
            if insights_file.exists():
                with open(insights_file, 'r') as f:
                    insights_data = json.load(f)
                    self.insights = [
                        MemoryEntry.from_dict(entry_dict) for entry_dict in insights_data
                    ]
                    
        except Exception as e:
            print(f"Error loading knowledge memory: {e}")
    
    def save(self):
        """Save knowledge to disk."""
        try:
            # Save concepts
            concepts_file = self.data_path / "concepts.json"
            with open(concepts_file, 'w') as f:
                concepts_data = {
                    entry_id: entry.to_dict() 
                    for entry_id, entry in self.concepts.items()
                }
                json.dump(concepts_data, f, indent=2)
            
            # Save patterns
            patterns_file = self.data_path / "patterns.json"
            with open(patterns_file, 'w') as f:
                patterns_data = {
                    pattern_type: [entry.to_dict() for entry in entries]
                    for pattern_type, entries in self.patterns.items()
                }
                json.dump(patterns_data, f, indent=2)
            
            # Save insights
            insights_file = self.data_path / "insights.json"
            with open(insights_file, 'w') as f:
                insights_data = [entry.to_dict() for entry in self.insights]
                json.dump(insights_data, f, indent=2)
                
        except Exception as e:
            print(f"Error saving knowledge memory: {e}")


class UserMemory:
    """
    Manages user-specific information, preferences, and long-term learning history.

    This memory tier handles:
    - User preferences and learning style
    - Long-term learning progress
    - Personal learning goals
    - Historical performance data
    """

    def __init__(self, user_id: str, data_path: Path = Path("data/user_memory")):
        """
        Initialize user memory.

        Args:
            user_id: User identifier
            data_path: Path to store user memory data
        """
        self.user_id = user_id
        self.data_path = data_path / user_id
        self.data_path.mkdir(parents=True, exist_ok=True)

        # User data storage
        self.profile = {}
        self.preferences = {}
        self.learning_history = []
        self.goals = []
        self.achievements = []
        self.performance_metrics = defaultdict(list)

        # Load existing data
        self._load_user_data()

    def update_profile(self, profile_data: Dict[str, Any]):
        """Update user profile information."""
        self.profile.update(profile_data)

    def update_preferences(self, preferences: Dict[str, Any]):
        """Update user learning preferences."""
        self.preferences.update(preferences)

    def add_learning_session(self, session_data: Dict[str, Any]):
        """Add a learning session to history."""
        session_entry = MemoryEntry(
            entry_id=f"session_{session_data.get('session_id', int(time.time()))}",
            content=session_data,
            metadata={"type": "learning_session"}
        )
        self.learning_history.append(session_entry)

    def add_goal(self, goal: str, target_date: str = None, framework: str = None):
        """Add a learning goal."""
        goal_entry = MemoryEntry(
            entry_id=f"goal_{int(time.time())}",
            content={
                "goal": goal,
                "target_date": target_date,
                "framework": framework,
                "status": "active"
            },
            metadata={"type": "goal"}
        )
        self.goals.append(goal_entry)

    def add_achievement(self, achievement: str, context: Dict[str, Any] = None):
        """Add an achievement."""
        achievement_entry = MemoryEntry(
            entry_id=f"achievement_{int(time.time())}",
            content={
                "achievement": achievement,
                "context": context or {}
            },
            metadata={"type": "achievement"}
        )
        self.achievements.append(achievement_entry)

    def record_performance_metric(self, metric_type: str, value: float, context: Dict[str, Any] = None):
        """Record a performance metric."""
        metric_entry = MemoryEntry(
            entry_id=f"metric_{metric_type}_{int(time.time())}",
            content={
                "metric_type": metric_type,
                "value": value,
                "context": context or {}
            },
            metadata={"type": "performance_metric"}
        )
        self.performance_metrics[metric_type].append(metric_entry)

    def get_learning_summary(self, days: int = 30) -> Dict[str, Any]:
        """Get learning summary for the specified period."""
        cutoff_time = time.time() - (days * 24 * 3600)

        recent_sessions = [
            entry for entry in self.learning_history
            if entry.timestamp >= cutoff_time
        ]

        frameworks_studied = set()
        total_time = 0

        for session in recent_sessions:
            content = session.content
            frameworks_studied.add(content.get("framework", "unknown"))
            total_time += content.get("duration", 0)

        return {
            "period_days": days,
            "sessions_count": len(recent_sessions),
            "frameworks_studied": list(frameworks_studied),
            "total_time_hours": total_time / 3600,
            "active_goals": len([g for g in self.goals if g.content.get("status") == "active"]),
            "recent_achievements": len([a for a in self.achievements if a.timestamp >= cutoff_time])
        }

    def _load_user_data(self):
        """Load user data from disk."""
        try:
            # Load profile
            profile_file = self.data_path / "profile.json"
            if profile_file.exists():
                with open(profile_file, 'r') as f:
                    self.profile = json.load(f)

            # Load preferences
            preferences_file = self.data_path / "preferences.json"
            if preferences_file.exists():
                with open(preferences_file, 'r') as f:
                    self.preferences = json.load(f)

            # Load learning history
            history_file = self.data_path / "learning_history.json"
            if history_file.exists():
                with open(history_file, 'r') as f:
                    history_data = json.load(f)
                    self.learning_history = [
                        MemoryEntry.from_dict(entry_dict) for entry_dict in history_data
                    ]

            # Load goals
            goals_file = self.data_path / "goals.json"
            if goals_file.exists():
                with open(goals_file, 'r') as f:
                    goals_data = json.load(f)
                    self.goals = [
                        MemoryEntry.from_dict(entry_dict) for entry_dict in goals_data
                    ]

            # Load achievements
            achievements_file = self.data_path / "achievements.json"
            if achievements_file.exists():
                with open(achievements_file, 'r') as f:
                    achievements_data = json.load(f)
                    self.achievements = [
                        MemoryEntry.from_dict(entry_dict) for entry_dict in achievements_data
                    ]

        except Exception as e:
            print(f"Error loading user memory: {e}")

    def save(self):
        """Save user data to disk."""
        try:
            # Save profile
            profile_file = self.data_path / "profile.json"
            with open(profile_file, 'w') as f:
                json.dump(self.profile, f, indent=2)

            # Save preferences
            preferences_file = self.data_path / "preferences.json"
            with open(preferences_file, 'w') as f:
                json.dump(self.preferences, f, indent=2)

            # Save learning history
            history_file = self.data_path / "learning_history.json"
            with open(history_file, 'w') as f:
                history_data = [entry.to_dict() for entry in self.learning_history]
                json.dump(history_data, f, indent=2)

            # Save goals
            goals_file = self.data_path / "goals.json"
            with open(goals_file, 'w') as f:
                goals_data = [entry.to_dict() for entry in self.goals]
                json.dump(goals_data, f, indent=2)

            # Save achievements
            achievements_file = self.data_path / "achievements.json"
            with open(achievements_file, 'w') as f:
                achievements_data = [entry.to_dict() for entry in self.achievements]
                json.dump(achievements_data, f, indent=2)

        except Exception as e:
            print(f"Error saving user memory: {e}")


class IntegratedMemoryManager:
    """
    Manages all three memory tiers and provides unified access.

    This class coordinates between:
    - ConversationMemory (short-term, session-specific)
    - KnowledgeMemory (domain knowledge, patterns)
    - UserMemory (long-term user data)
    """

    def __init__(self, data_path: Path = Path("data/memory")):
        """
        Initialize the integrated memory manager.

        Args:
            data_path: Base path for memory storage
        """
        self.data_path = data_path
        self.data_path.mkdir(parents=True, exist_ok=True)

        # Memory tiers
        self.conversation_memories = {}  # session_id -> ConversationMemory
        self.knowledge_memory = KnowledgeMemory(data_path / "knowledge")
        self.user_memories = {}  # user_id -> UserMemory

    def get_conversation_memory(self, session_id: str) -> ConversationMemory:
        """Get or create conversation memory for a session."""
        if session_id not in self.conversation_memories:
            self.conversation_memories[session_id] = ConversationMemory(session_id)
        return self.conversation_memories[session_id]

    def get_user_memory(self, user_id: str) -> UserMemory:
        """Get or create user memory for a user."""
        if user_id not in self.user_memories:
            self.user_memories[user_id] = UserMemory(user_id, self.data_path / "users")
        return self.user_memories[user_id]

    def get_knowledge_memory(self) -> KnowledgeMemory:
        """Get the knowledge memory instance."""
        return self.knowledge_memory

    def search_all_memories(
        self,
        query: str,
        user_id: str = None,
        session_id: str = None
    ) -> Dict[str, List[Any]]:
        """
        Search across all memory tiers.

        Args:
            query: Search query
            user_id: Optional user ID to limit search
            session_id: Optional session ID to limit search

        Returns:
            Dictionary with search results from each memory tier
        """
        results = {
            "conversation": [],
            "knowledge": [],
            "user": []
        }

        # Search conversation memory
        if session_id and session_id in self.conversation_memories:
            conv_memory = self.conversation_memories[session_id]
            results["conversation"] = conv_memory.search_messages(query)

        # Search knowledge memory
        results["knowledge"] = self.knowledge_memory.search_knowledge(query)

        # Search user memory
        if user_id and user_id in self.user_memories:
            user_memory = self.user_memories[user_id]
            # Simple search in user data (could be enhanced)
            user_results = []
            for session in user_memory.learning_history:
                content_str = json.dumps(session.content).lower()
                if query.lower() in content_str:
                    user_results.append(session)
            results["user"] = user_results

        return results

    def save_all(self):
        """Save all memory tiers to disk."""
        # Save knowledge memory
        self.knowledge_memory.save()

        # Save user memories
        for user_memory in self.user_memories.values():
            user_memory.save()

        # Conversation memories are typically not persisted long-term
        # but could be saved for session recovery if needed

    def cleanup_old_conversations(self, max_age_hours: int = 24):
        """Clean up old conversation memories."""
        cutoff_time = time.time() - (max_age_hours * 3600)

        sessions_to_remove = []
        for session_id, conv_memory in self.conversation_memories.items():
            # Check if conversation is old (simplified check)
            if len(conv_memory.entries) == 0:
                sessions_to_remove.append(session_id)

        for session_id in sessions_to_remove:
            del self.conversation_memories[session_id]

    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get statistics about memory usage."""
        return {
            "active_conversations": len(self.conversation_memories),
            "total_users": len(self.user_memories),
            "knowledge_concepts": len(self.knowledge_memory.concepts),
            "knowledge_patterns": sum(len(patterns) for patterns in self.knowledge_memory.patterns.values()),
            "knowledge_insights": len(self.knowledge_memory.insights)
        }


# Global memory manager instance
_memory_manager_instance = None

def get_memory_manager(data_path: Path = None) -> IntegratedMemoryManager:
    """
    Get the global memory manager instance.

    Args:
        data_path: Path to memory data (required for first call)

    Returns:
        IntegratedMemoryManager instance
    """
    global _memory_manager_instance

    if _memory_manager_instance is None:
        if data_path is None:
            data_path = Path("data/memory")
        _memory_manager_instance = IntegratedMemoryManager(data_path)

    return _memory_manager_instance
