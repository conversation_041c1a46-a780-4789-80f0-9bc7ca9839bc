"""
Comprehensive tests for vinagent Memory functionality.

This module tests:
- Memory initialization and configuration
- Memory save and load operations
- Memory persistence and data integrity
- Graph-based memory operations
- Error handling and edge cases
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from vinagent.memory.memory import Memory, MemoryMeta


class MockLLM:
    """Mock LLM for memory testing."""
    
    def __init__(self):
        self.model_name = "mock-llm"
    
    def invoke(self, messages):
        """Mock invoke method."""
        class MockResponse:
            def __init__(self, content):
                self.content = content
        return MockResponse("Mock LLM response for memory")


class TestMemoryInitialization:
    """Test Memory initialization and configuration."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_basic_initialization(self, temp_data_path):
        """Test basic memory initialization."""
        memory_path = temp_data_path / "test_memory.jsonl"
        
        memory = Memory(
            memory_path=memory_path,
            is_reset_memory=False,
            is_logging=False
        )
        
        assert memory.memory_path == memory_path
        assert memory.is_reset_memory == False
        assert memory.is_logging == False
        assert memory_path.exists()
    
    def test_memory_with_string_path(self, temp_data_path):
        """Test memory initialization with string path."""
        memory_path_str = str(temp_data_path / "string_memory.jsonl")
        
        memory = Memory(
            memory_path=memory_path_str,
            is_reset_memory=False
        )
        
        assert memory.memory_path == Path(memory_path_str)
        assert memory.memory_path.exists()
    
    def test_memory_with_reset_flag(self, temp_data_path):
        """Test memory initialization with reset flag."""
        memory_path = temp_data_path / "reset_memory.jsonl"
        
        # Create initial memory file with some data
        memory_path.write_text(json.dumps({"user1": ["old_data"]}, indent=2))
        
        memory = Memory(
            memory_path=memory_path,
            is_reset_memory=True
        )
        
        # Memory should be reset (empty)
        content = json.loads(memory_path.read_text())
        assert content == {}
    
    def test_memory_creates_parent_directories(self, temp_data_path):
        """Test that memory creates parent directories if they don't exist."""
        nested_path = temp_data_path / "nested" / "deep" / "memory.jsonl"
        
        memory = Memory(memory_path=nested_path)
        
        assert nested_path.exists()
        assert nested_path.parent.exists()
    
    def test_memory_with_logging_enabled(self, temp_data_path):
        """Test memory initialization with logging enabled."""
        memory_path = temp_data_path / "logged_memory.jsonl"
        
        memory = Memory(
            memory_path=memory_path,
            is_logging=True
        )
        
        assert memory.is_logging == True
    
    def test_memory_default_path(self):
        """Test memory with default path."""
        memory = Memory()
        
        # Should use default path
        assert memory.memory_path == Path('templates/memory.jsonl')


class TestMemoryOperations:
    """Test Memory save and load operations."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def memory(self, temp_data_path):
        """Create a test memory instance."""
        memory_path = temp_data_path / "test_memory.jsonl"
        return Memory(memory_path=memory_path, is_logging=False)
    
    def test_save_and_load_memory_list(self, memory):
        """Test saving and loading memory as list."""
        user_id = "test_user"
        test_data = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        
        # Save memory
        memory.save_memory(test_data, memory.memory_path, user_id)
        
        # Load memory
        loaded_data = memory.load_memory(user_id=user_id, load_type='list')
        
        assert loaded_data == test_data
    
    def test_save_and_load_memory_string(self, memory):
        """Test saving and loading memory as string."""
        user_id = "string_user"
        test_data = [
            {"role": "user", "content": "Test message"},
            {"role": "assistant", "content": "Test response"}
        ]
        
        # Save memory
        memory.save_memory(test_data, memory.memory_path, user_id)
        
        # Load memory as string
        loaded_string = memory.load_memory(user_id=user_id, load_type='string')
        
        assert isinstance(loaded_string, str)
        assert "Test message" in loaded_string
        assert "Test response" in loaded_string
    
    def test_load_nonexistent_user(self, memory):
        """Test loading memory for non-existent user."""
        loaded_data = memory.load_memory(user_id="nonexistent_user", load_type='list')
        
        # Should return empty list for non-existent user
        assert loaded_data == []
    
    def test_multiple_users_memory(self, memory):
        """Test memory operations with multiple users."""
        user1_data = [{"role": "user", "content": "User 1 message"}]
        user2_data = [{"role": "user", "content": "User 2 message"}]
        
        # Save memory for both users
        memory.save_memory(user1_data, memory.memory_path, "user1")
        memory.save_memory(user2_data, memory.memory_path, "user2")
        
        # Load memory for each user
        loaded_user1 = memory.load_memory(user_id="user1", load_type='list')
        loaded_user2 = memory.load_memory(user_id="user2", load_type='list')
        
        assert loaded_user1 == user1_data
        assert loaded_user2 == user2_data
        assert loaded_user1 != loaded_user2
    
    def test_memory_persistence(self, temp_data_path):
        """Test memory persistence across instances."""
        memory_path = temp_data_path / "persistent_memory.jsonl"
        user_id = "persistent_user"
        test_data = [{"role": "user", "content": "Persistent message"}]
        
        # Save with first memory instance
        memory1 = Memory(memory_path=memory_path)
        memory1.save_memory(test_data, memory_path, user_id)
        
        # Load with second memory instance
        memory2 = Memory(memory_path=memory_path)
        loaded_data = memory2.load_memory(user_id=user_id, load_type='list')
        
        assert loaded_data == test_data


class TestMemoryGraphOperations:
    """Test Memory graph-based operations."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def memory(self, temp_data_path):
        """Create a test memory instance."""
        memory_path = temp_data_path / "graph_memory.jsonl"
        return Memory(memory_path=memory_path, is_logging=False)
    
    @pytest.fixture
    def mock_llm(self):
        """Mock LLM for graph operations."""
        return MockLLM()
    
    @patch('vinagent.memory.memory.LLMGraphTransformer')
    def test_save_short_term_memory(self, mock_graph_transformer, memory, mock_llm):
        """Test saving short-term memory with graph transformation."""
        # Mock the graph transformer
        mock_transformer_instance = Mock()
        mock_graph = [{"entity": "test", "relation": "is", "object": "example"}]
        mock_transformer_instance.generate_graph.return_value = mock_graph
        mock_graph_transformer.return_value = mock_transformer_instance
        
        user_id = "graph_user"
        message = "This is a test message for graph transformation"
        
        # Save short-term memory
        result_graph = memory.save_short_term_memory(mock_llm, message, user_id)
        
        # Verify graph transformer was called
        mock_graph_transformer.assert_called_once_with(llm=mock_llm)
        mock_transformer_instance.generate_graph.assert_called_once_with(message)
        
        assert result_graph == mock_graph
    
    def test_update_memory_with_graph(self, memory):
        """Test updating memory with graph data."""
        user_id = "graph_update_user"
        graph_data = [
            {"entity": "Python", "relation": "is", "object": "programming language"},
            {"entity": "LangChain", "relation": "uses", "object": "Python"}
        ]
        
        # Update memory with graph
        memory.update_memory(graph_data, user_id)
        
        # Load and verify
        loaded_data = memory.load_memory(user_id=user_id, load_type='list')
        assert loaded_data == graph_data
    
    def test_revert_object_mess(self, memory):
        """Test reverting object messages to string format."""
        test_data = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"}
        ]
        
        # Convert to string format
        result_string = memory.revert_object_mess(test_data)
        
        assert isinstance(result_string, str)
        assert "Hello" in result_string
        assert "Hi there!" in result_string


class TestMemoryErrorHandling:
    """Test Memory error handling and edge cases."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_with_invalid_json_file(self, temp_data_path):
        """Test memory behavior with invalid JSON file."""
        memory_path = temp_data_path / "invalid_memory.jsonl"
        memory_path.write_text("invalid json content")
        
        memory = Memory(memory_path=memory_path)
        
        # Should handle invalid JSON gracefully
        loaded_data = memory.load_memory(user_id="test_user", load_type='list')
        assert loaded_data == []
    
    def test_memory_with_readonly_file(self, temp_data_path):
        """Test memory behavior with read-only file."""
        memory_path = temp_data_path / "readonly_memory.jsonl"
        memory_path.write_text(json.dumps({}))
        
        # Make file read-only (on Windows, this might not work as expected)
        try:
            memory_path.chmod(0o444)
            
            memory = Memory(memory_path=memory_path)
            
            # Attempt to save should handle permission error
            with pytest.raises((PermissionError, OSError)):
                memory.save_memory([], memory_path, "test_user")
        except OSError:
            # Skip test if chmod doesn't work on this system
            pytest.skip("Cannot set file permissions on this system")
    
    def test_memory_with_none_path(self):
        """Test memory initialization with None path."""
        memory = Memory(memory_path=None)
        
        # Should use default path
        assert memory.memory_path == Path('templates/memory.jsonl')
    
    def test_memory_load_with_invalid_user_id(self, temp_data_path):
        """Test loading memory with invalid user ID types."""
        memory_path = temp_data_path / "test_memory.jsonl"
        memory = Memory(memory_path=memory_path)
        
        # Test with None user_id
        loaded_data = memory.load_memory(user_id=None, load_type='list')
        assert loaded_data == []
        
        # Test with empty string user_id
        loaded_data = memory.load_memory(user_id="", load_type='list')
        assert loaded_data == []
