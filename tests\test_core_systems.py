"""
Comprehensive tests for GAAPF core systems.

This module tests:
- Knowledge Graph Manager
- Memory Systems (ConversationMemory, KnowledgeMemory, UserMemory)
- Analytics System
- Framework Database Manager
- Learning Hub Core
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from gaapf.core.knowledge_graph import (
    KnowledgeGraphManager, ConceptNode, RelationshipType
)
from gaapf.core.memory_systems import (
    ConversationMemory, KnowledgeMemory, UserMemory, 
    IntegratedMemoryManager, MemoryEntry
)
from gaapf.core.analytics_system import (
    RealTimeAnalyticsEngine, LearningMetric, EngagementMetrics,
    LearningEffectivenessMetrics, AdaptiveMetrics
)
from gaapf.core.framework_database import FrameworkDatabaseManager
from gaapf.core.learning_hub import LearningHubCore


class TestKnowledgeGraph:
    """Test Knowledge Graph Manager functionality."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def knowledge_graph(self, temp_data_path):
        """Knowledge graph instance for testing."""
        return KnowledgeGraphManager(temp_data_path)
    
    @pytest.fixture
    def sample_concept(self):
        """Sample concept for testing."""
        return ConceptNode(
            concept_id="test_concept",
            name="Test Concept",
            framework="langchain",
            module_id="test_module",
            description="A test concept",
            difficulty_level="intermediate",
            prerequisites=["basic_concept"],
            learning_objectives=["understand test concept"]
        )
    
    def test_add_concept(self, knowledge_graph, sample_concept):
        """Test adding a concept to the knowledge graph."""
        result = knowledge_graph.add_concept(sample_concept)
        assert result == True
        
        # Verify concept was added
        retrieved_concept = knowledge_graph.get_concept("test_concept")
        assert retrieved_concept is not None
        assert retrieved_concept.name == "Test Concept"
        assert retrieved_concept.framework == "langchain"
    
    def test_add_relationship(self, knowledge_graph, sample_concept):
        """Test adding relationships between concepts."""
        # Add prerequisite concept
        prereq_concept = ConceptNode(
            concept_id="basic_concept",
            name="Basic Concept",
            framework="langchain",
            module_id="test_module"
        )
        
        knowledge_graph.add_concept(prereq_concept)
        knowledge_graph.add_concept(sample_concept)
        
        # Add relationship
        result = knowledge_graph.add_relationship(
            "basic_concept", 
            "test_concept", 
            RelationshipType.PREREQUISITE
        )
        assert result == True
        
        # Verify relationship
        prerequisites = knowledge_graph.get_prerequisites("test_concept")
        assert "basic_concept" in prerequisites
    
    def test_find_learning_path(self, knowledge_graph):
        """Test learning path finding."""
        # Create a chain of concepts
        concepts = [
            ConceptNode("concept_a", "Concept A", "langchain", "test_module"),
            ConceptNode("concept_b", "Concept B", "langchain", "test_module", prerequisites=["concept_a"]),
            ConceptNode("concept_c", "Concept C", "langchain", "test_module", prerequisites=["concept_b"])
        ]
        
        for concept in concepts:
            knowledge_graph.add_concept(concept)
        
        # Find learning path
        path = knowledge_graph.find_learning_path([], "concept_c")
        
        assert len(path) == 3
        assert path == ["concept_a", "concept_b", "concept_c"]
    
    def test_identify_knowledge_gaps(self, knowledge_graph):
        """Test knowledge gap identification."""
        # Add concepts with prerequisites
        concepts = [
            ConceptNode("basic", "Basic", "langchain", "test_module"),
            ConceptNode("intermediate", "Intermediate", "langchain", "test_module", prerequisites=["basic"]),
            ConceptNode("advanced", "Advanced", "langchain", "test_module", prerequisites=["intermediate"])
        ]
        
        for concept in concepts:
            knowledge_graph.add_concept(concept)
        
        # Analyze gaps for user wanting to learn advanced concept
        gaps = knowledge_graph.identify_knowledge_gaps("test_user", ["advanced"])
        
        assert "advanced" in gaps["missing_prerequisites"]
        assert len(gaps["missing_prerequisites"]["advanced"]) > 0
        assert "advanced" in gaps["recommended_learning_paths"]


class TestMemorySystems:
    """Test memory system functionality."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_conversation_memory(self):
        """Test conversation memory functionality."""
        conv_memory = ConversationMemory("test_session")
        
        # Add messages
        conv_memory.add_message("user", "Hello", metadata={"timestamp": "2024-01-01"})
        conv_memory.add_message("assistant", "Hi there!", agent_type="instructor")
        
        # Test retrieval
        recent_messages = conv_memory.get_recent_messages(2)
        assert len(recent_messages) == 2
        assert recent_messages[0]["role"] == "user"
        assert recent_messages[1]["role"] == "assistant"
        
        # Test search
        matches = conv_memory.search_messages("Hello")
        assert len(matches) == 1
        assert matches[0].content["content"] == "Hello"
    
    def test_knowledge_memory(self, temp_data_path):
        """Test knowledge memory functionality."""
        knowledge_memory = KnowledgeMemory(temp_data_path)
        
        # Add concept knowledge
        knowledge_memory.add_concept_knowledge(
            concept_id="test_concept",
            framework="langchain",
            knowledge_data={"key": "value"},
            confidence=0.9
        )
        
        # Add learning pattern
        knowledge_memory.add_learning_pattern(
            pattern_type="difficulty_progression",
            pattern_data={"pattern": "increasing"},
            user_context={"user_id": "test_user"}
        )
        
        # Add insight
        knowledge_memory.add_insight(
            insight="Users learn better with examples",
            context={"framework": "langchain"}
        )
        
        # Test retrieval
        concept_knowledge = knowledge_memory.get_concept_knowledge("test_concept", "langchain")
        assert len(concept_knowledge) == 1
        assert concept_knowledge[0].content["confidence"] == 0.9
        
        patterns = knowledge_memory.get_patterns("difficulty_progression")
        assert len(patterns) == 1
        
        # Test search
        search_results = knowledge_memory.search_knowledge("examples")
        assert len(search_results) >= 1
    
    def test_user_memory(self, temp_data_path):
        """Test user memory functionality."""
        user_memory = UserMemory("test_user", temp_data_path)
        
        # Update profile
        user_memory.update_profile({"name": "Test User", "skill_level": "intermediate"})
        
        # Update preferences
        user_memory.update_preferences({"learning_style": "visual", "pace": "moderate"})
        
        # Add learning session
        user_memory.add_learning_session({
            "session_id": "test_session",
            "framework": "langchain",
            "duration": 3600,
            "score": 85.0
        })
        
        # Add goal
        user_memory.add_goal("Master LangChain", "2024-12-31", "langchain")
        
        # Add achievement
        user_memory.add_achievement("Completed first module", {"framework": "langchain"})
        
        # Record performance metric
        user_memory.record_performance_metric("comprehension_score", 0.85, {"module": "basics"})
        
        # Test learning summary
        summary = user_memory.get_learning_summary(30)
        assert summary["sessions_count"] == 1
        assert summary["total_time_hours"] == 1.0
        assert "langchain" in summary["frameworks_studied"]
    
    def test_integrated_memory_manager(self, temp_data_path):
        """Test integrated memory manager."""
        memory_manager = IntegratedMemoryManager(temp_data_path)
        
        # Get different memory types
        conv_memory = memory_manager.get_conversation_memory("test_session")
        user_memory = memory_manager.get_user_memory("test_user")
        knowledge_memory = memory_manager.get_knowledge_memory()
        
        assert isinstance(conv_memory, ConversationMemory)
        assert isinstance(user_memory, UserMemory)
        assert isinstance(knowledge_memory, KnowledgeMemory)
        
        # Add some data
        conv_memory.add_message("user", "Test message")
        user_memory.update_profile({"name": "Test User"})
        knowledge_memory.add_insight("Test insight")
        
        # Test search across all memories
        search_results = memory_manager.search_all_memories(
            "test", 
            user_id="test_user", 
            session_id="test_session"
        )
        
        assert "conversation" in search_results
        assert "knowledge" in search_results
        assert "user" in search_results


class TestAnalyticsSystem:
    """Test analytics system functionality."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def analytics_engine(self, temp_data_path):
        """Analytics engine instance for testing."""
        return RealTimeAnalyticsEngine(temp_data_path)
    
    def test_record_engagement_metrics(self, analytics_engine):
        """Test recording engagement metrics."""
        engagement_metrics = EngagementMetrics(
            session_duration=3600.0,
            interaction_count=25,
            question_count=5,
            response_time_avg=2.5,
            attention_score=0.85,
            completion_rate=0.9
        )
        
        analytics_engine.record_engagement_metrics(
            user_id="test_user",
            session_id="test_session",
            framework="langchain",
            module_id="test_module",
            metrics=engagement_metrics
        )
        
        # Verify metrics were recorded
        assert len(analytics_engine.live_metrics) > 0
        assert "test_session" in analytics_engine.session_metrics
    
    def test_record_effectiveness_metrics(self, analytics_engine):
        """Test recording effectiveness metrics."""
        effectiveness_metrics = LearningEffectivenessMetrics(
            comprehension_score=0.85,
            retention_score=0.78,
            application_score=0.82,
            progress_velocity=1.2,
            concept_mastery_rate=0.75,
            error_recovery_rate=0.9
        )
        
        analytics_engine.record_effectiveness_metrics(
            user_id="test_user",
            session_id="test_session",
            framework="langchain",
            module_id="test_module",
            metrics=effectiveness_metrics
        )
        
        # Verify metrics were recorded
        effectiveness_metrics_recorded = [
            m for m in analytics_engine.live_metrics.get("effectiveness.comprehension_score", [])
            if m.user_id == "test_user"
        ]
        assert len(effectiveness_metrics_recorded) > 0
    
    def test_real_time_dashboard(self, analytics_engine):
        """Test real-time dashboard generation."""
        # Add some sample metrics
        engagement_metrics = EngagementMetrics(
            session_duration=3600.0,
            interaction_count=25,
            question_count=5,
            response_time_avg=2.5,
            attention_score=0.85,
            completion_rate=0.9
        )
        
        analytics_engine.record_engagement_metrics(
            user_id="test_user",
            session_id="test_session",
            framework="langchain",
            module_id="test_module",
            metrics=engagement_metrics
        )
        
        # Get dashboard
        dashboard = analytics_engine.get_real_time_dashboard("test_user")
        
        assert "timestamp" in dashboard
        assert "engagement_summary" in dashboard
        assert "effectiveness_summary" in dashboard
        assert "adaptive_summary" in dashboard
        assert "trends" in dashboard
        assert "alerts" in dashboard
    
    def test_learning_effectiveness_analysis(self, analytics_engine):
        """Test learning effectiveness analysis."""
        # Add sample metrics
        effectiveness_metrics = LearningEffectivenessMetrics(
            comprehension_score=0.85,
            retention_score=0.78,
            application_score=0.82,
            progress_velocity=1.2,
            concept_mastery_rate=0.75,
            error_recovery_rate=0.9
        )
        
        analytics_engine.record_effectiveness_metrics(
            user_id="test_user",
            session_id="test_session",
            framework="langchain",
            module_id="test_module",
            metrics=effectiveness_metrics
        )
        
        # Analyze effectiveness
        analysis = analytics_engine.analyze_learning_effectiveness("test_user", 24)
        
        assert "user_id" in analysis
        assert "overall_score" in analysis
        assert "recommendations" in analysis
        assert "engagement_analysis" in analysis
        assert "effectiveness_analysis" in analysis
        assert "adaptive_analysis" in analysis


class TestFrameworkDatabase:
    """Test framework database functionality."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def db_manager(self, temp_data_path):
        """Database manager instance for testing."""
        return FrameworkDatabaseManager(temp_data_path)
    
    def test_singleton_pattern(self, temp_data_path):
        """Test that database manager follows singleton pattern."""
        db1 = FrameworkDatabaseManager(temp_data_path)
        db2 = FrameworkDatabaseManager(temp_data_path)
        
        assert db1 is db2
    
    def test_get_module(self, db_manager):
        """Test module retrieval."""
        # This test assumes default modules are loaded
        module = db_manager.get_module("lc_basics")
        
        if module:  # Module exists in default config
            assert module.module_id == "lc_basics"
            assert module.framework.value == "langchain"
    
    def test_cache_functionality(self, db_manager):
        """Test caching functionality."""
        # Get cache statistics
        stats = db_manager.get_cache_statistics()
        
        assert "cache_stats" in stats
        assert "hit_rate_percent" in stats
        assert "cache_sizes" in stats
        assert "total_cached_items" in stats
    
    def test_database_statistics(self, db_manager):
        """Test database statistics."""
        stats = db_manager.get_database_statistics()
        
        assert "total_frameworks" in stats
        assert "total_modules" in stats
        assert "modules_by_framework" in stats
        assert "modules_by_difficulty" in stats
        assert "modules_by_type" in stats
