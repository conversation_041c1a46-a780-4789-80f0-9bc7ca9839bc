"""
Test cases for GAAPF error handling improvements.

This module tests the enhanced error handling functionality
added to the GAAPF memory systems and components.
"""

import pytest
import tempfile
import json
import os
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from gaapf.core.memory_systems import (
    KnowledgeMemory, 
    UserMemory, 
    ConversationMemory,
    IntegratedMemoryManager,
    MemoryEntry
)


class TestKnowledgeMemoryErrorHandling:
    """Test KnowledgeMemory error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_knowledge_memory_with_corrupted_concepts_file(self, temp_data_path):
        """Test KnowledgeMemory with corrupted concepts file."""
        concepts_file = temp_data_path / "concepts.json"
        concepts_file.write_text("{ invalid json content")
        
        # Should handle corrupted file gracefully
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Concepts should be empty due to corruption
        assert len(knowledge_memory.concepts) == 0
        
        # Backup file should be created
        backup_files = list(temp_data_path.glob("*.bak"))
        assert len(backup_files) == 1
    
    def test_knowledge_memory_with_corrupted_patterns_file(self, temp_data_path):
        """Test KnowledgeMemory with corrupted patterns file."""
        patterns_file = temp_data_path / "patterns.json"
        patterns_file.write_text("{ incomplete json")
        
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Patterns should be empty due to corruption
        assert len(knowledge_memory.patterns) == 0
    
    def test_knowledge_memory_with_invalid_data_format(self, temp_data_path):
        """Test KnowledgeMemory with invalid data format."""
        concepts_file = temp_data_path / "concepts.json"
        # Valid JSON but wrong format (list instead of dict)
        concepts_file.write_text('["not", "a", "dict"]')
        
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Should handle invalid format gracefully
        assert len(knowledge_memory.concepts) == 0
    
    def test_knowledge_memory_save_with_permission_error(self, temp_data_path):
        """Test KnowledgeMemory save with permission error."""
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Add some test data
        test_entry = MemoryEntry(
            entry_id="test_concept",
            content={"name": "test", "description": "test concept"},
            metadata={"type": "concept"}
        )
        knowledge_memory.concepts["test_concept"] = test_entry
        
        # Mock file operations to simulate permission error
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            with pytest.raises(PermissionError):
                knowledge_memory.save()
    
    def test_knowledge_memory_with_empty_files(self, temp_data_path):
        """Test KnowledgeMemory with empty files."""
        # Create empty files
        (temp_data_path / "concepts.json").write_text("")
        (temp_data_path / "patterns.json").write_text("")
        (temp_data_path / "insights.json").write_text("")
        
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Should handle empty files gracefully
        assert len(knowledge_memory.concepts) == 0
        assert len(knowledge_memory.patterns) == 0
        assert len(knowledge_memory.insights) == 0
    
    def test_knowledge_memory_with_malformed_entries(self, temp_data_path):
        """Test KnowledgeMemory with malformed memory entries."""
        concepts_file = temp_data_path / "concepts.json"
        # Valid JSON but entries missing required fields
        malformed_data = {
            "concept1": {"missing_required_fields": True},
            "concept2": {"entry_id": "concept2"}  # Missing other required fields
        }
        concepts_file.write_text(json.dumps(malformed_data))
        
        knowledge_memory = KnowledgeMemory(data_path=temp_data_path)
        
        # Should handle malformed entries gracefully
        # Some entries might be skipped due to errors
        assert len(knowledge_memory.concepts) <= 2


class TestUserMemoryErrorHandling:
    """Test UserMemory error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_user_memory_with_invalid_user_id(self, temp_data_path):
        """Test UserMemory with invalid user ID."""
        # Test with various invalid user IDs
        invalid_ids = [None, "", "   ", "\n\t", "user/with/slashes"]
        
        for invalid_id in invalid_ids:
            try:
                user_memory = UserMemory(user_id=invalid_id, data_path=temp_data_path)
                # If it doesn't raise an error, it should handle it gracefully
                assert user_memory.user_id is not None
            except (ValueError, TypeError):
                # It's acceptable to raise validation errors for invalid IDs
                pass
    
    def test_user_memory_with_corrupted_preferences_file(self, temp_data_path):
        """Test UserMemory with corrupted preferences file."""
        user_id = "test_user"
        user_data_path = temp_data_path / user_id
        user_data_path.mkdir()
        
        # Create corrupted preferences file
        preferences_file = user_data_path / "preferences.json"
        preferences_file.write_text("{ corrupted json")
        
        user_memory = UserMemory(user_id=user_id, data_path=temp_data_path)
        
        # Should handle corrupted file gracefully
        assert isinstance(user_memory.preferences, dict)
    
    def test_user_memory_save_with_disk_full_simulation(self, temp_data_path):
        """Test UserMemory save with disk full simulation."""
        user_memory = UserMemory(user_id="test_user", data_path=temp_data_path)
        
        # Add some test data
        test_goal = MemoryEntry(
            entry_id="test_goal",
            content={"goal": "learn Python", "deadline": "2024-12-31"},
            metadata={"priority": "high"}
        )
        user_memory.goals.append(test_goal)
        
        # Mock file operations to simulate disk full error
        with patch('builtins.open', side_effect=OSError("No space left on device")):
            with pytest.raises(OSError):
                user_memory.save()


class TestConversationMemoryErrorHandling:
    """Test ConversationMemory error handling."""
    
    def test_conversation_memory_with_invalid_session_id(self):
        """Test ConversationMemory with invalid session ID."""
        invalid_ids = [None, "", "   ", "\n\t"]
        
        for invalid_id in invalid_ids:
            try:
                conv_memory = ConversationMemory(session_id=invalid_id)
                # Should handle gracefully or raise appropriate error
                assert conv_memory.session_id is not None
            except (ValueError, TypeError):
                # Acceptable to raise validation errors
                pass
    
    def test_conversation_memory_with_excessive_entries(self):
        """Test ConversationMemory with excessive entries."""
        conv_memory = ConversationMemory(session_id="test_session", max_entries=5)
        
        # Add more entries than the limit
        for i in range(10):
            conv_memory.add_message(
                role="user",
                content=f"Message {i}",
                agent_type="test_agent"
            )
        
        # Should respect max_entries limit
        assert len(conv_memory.entries) <= 5
    
    def test_conversation_memory_with_invalid_message_data(self):
        """Test ConversationMemory with invalid message data."""
        conv_memory = ConversationMemory(session_id="test_session")
        
        # Test with None content
        conv_memory.add_message(role="user", content=None)
        
        # Test with empty content
        conv_memory.add_message(role="user", content="")
        
        # Test with invalid role
        conv_memory.add_message(role=None, content="test message")
        
        # Should handle invalid data gracefully
        assert len(conv_memory.entries) >= 0
    
    def test_conversation_memory_search_with_invalid_query(self):
        """Test ConversationMemory search with invalid query."""
        conv_memory = ConversationMemory(session_id="test_session")
        
        # Add some test messages
        conv_memory.add_message(role="user", content="Hello world")
        conv_memory.add_message(role="assistant", content="Hi there!")
        
        # Test search with invalid queries
        result = conv_memory.search_messages(None)
        assert isinstance(result, list)
        
        result = conv_memory.search_messages("")
        assert isinstance(result, list)
        
        result = conv_memory.search_messages("   ")
        assert isinstance(result, list)


class TestIntegratedMemoryManagerErrorHandling:
    """Test IntegratedMemoryManager error handling."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_memory_manager_with_invalid_data_path(self):
        """Test IntegratedMemoryManager with invalid data path."""
        # Test with various invalid paths
        invalid_paths = [None, "", "   "]
        
        for invalid_path in invalid_paths:
            try:
                manager = IntegratedMemoryManager(data_path=invalid_path)
                # Should handle gracefully or use default
                assert manager.data_path is not None
            except (ValueError, TypeError):
                # Acceptable to raise validation errors
                pass
    
    def test_memory_manager_get_conversation_memory_invalid_session(self, temp_data_path):
        """Test getting conversation memory with invalid session ID."""
        manager = IntegratedMemoryManager(data_path=temp_data_path)
        
        # Test with invalid session IDs
        invalid_sessions = [None, "", "   ", "\n\t"]
        
        for invalid_session in invalid_sessions:
            try:
                memory = manager.get_conversation_memory(invalid_session)
                # Should handle gracefully
                assert memory is None or hasattr(memory, 'session_id')
            except (ValueError, TypeError):
                # Acceptable to raise validation errors
                pass
    
    def test_memory_manager_get_user_memory_invalid_user(self, temp_data_path):
        """Test getting user memory with invalid user ID."""
        manager = IntegratedMemoryManager(data_path=temp_data_path)
        
        # Test with invalid user IDs
        invalid_users = [None, "", "   ", "\n\t"]
        
        for invalid_user in invalid_users:
            try:
                memory = manager.get_user_memory(invalid_user)
                # Should handle gracefully
                assert memory is None or hasattr(memory, 'user_id')
            except (ValueError, TypeError):
                # Acceptable to raise validation errors
                pass
    
    def test_memory_manager_with_corrupted_data_directory(self, temp_data_path):
        """Test IntegratedMemoryManager with corrupted data directory."""
        # Create some corrupted files in the data directory
        (temp_data_path / "knowledge" / "concepts.json").parent.mkdir(parents=True)
        (temp_data_path / "knowledge" / "concepts.json").write_text("{ corrupted")
        
        manager = IntegratedMemoryManager(data_path=temp_data_path)
        
        # Should initialize despite corrupted files
        assert manager.knowledge_memory is not None
    
    def test_memory_manager_concurrent_access(self, temp_data_path):
        """Test IntegratedMemoryManager with concurrent access."""
        import threading
        import time
        
        manager = IntegratedMemoryManager(data_path=temp_data_path)
        results = []
        errors = []
        
        def access_memory(thread_id):
            try:
                # Simulate concurrent access
                conv_memory = manager.get_conversation_memory(f"session_{thread_id}")
                conv_memory.add_message(
                    role="user",
                    content=f"Message from thread {thread_id}",
                    agent_type="test_agent"
                )
                results.append(f"thread_{thread_id}_success")
            except Exception as e:
                errors.append(f"thread_{thread_id}_error: {e}")
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=access_memory, args=(i,))
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Should handle concurrent access gracefully
        assert len(results) + len(errors) == 5
        # Most operations should succeed
        assert len(results) >= 3


class TestMemoryEntryErrorHandling:
    """Test MemoryEntry error handling."""
    
    def test_memory_entry_with_invalid_data(self):
        """Test MemoryEntry with invalid data."""
        # Test with None values
        try:
            entry = MemoryEntry(entry_id=None, content=None, metadata=None)
            assert entry is not None
        except (ValueError, TypeError):
            # Acceptable to raise validation errors
            pass
        
        # Test with empty values
        try:
            entry = MemoryEntry(entry_id="", content={}, metadata={})
            assert entry is not None
        except (ValueError, TypeError):
            # Acceptable to raise validation errors
            pass
    
    def test_memory_entry_to_dict_with_invalid_content(self):
        """Test MemoryEntry.to_dict with invalid content."""
        entry = MemoryEntry(
            entry_id="test_entry",
            content={"test": "data"},
            metadata={"type": "test"}
        )
        
        # Modify content to be invalid
        entry.content = None
        
        try:
            result = entry.to_dict()
            assert isinstance(result, dict)
        except Exception:
            # Should handle gracefully or raise appropriate error
            pass
    
    def test_memory_entry_from_dict_with_invalid_data(self):
        """Test MemoryEntry.from_dict with invalid data."""
        invalid_data_sets = [
            None,
            {},
            {"missing_required_fields": True},
            {"entry_id": None, "content": None},
            {"entry_id": "", "content": {}, "metadata": None}
        ]
        
        for invalid_data in invalid_data_sets:
            try:
                entry = MemoryEntry.from_dict(invalid_data)
                assert entry is not None
            except (ValueError, TypeError, KeyError):
                # Acceptable to raise validation errors for invalid data
                pass
