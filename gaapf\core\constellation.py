"""
Constellation Manager for GAAPF.

This module implements the Adaptive Learning Constellation architecture,
which dynamically forms specialized agent teams based on user learning patterns
and contextual requirements.
"""

from enum import Enum
from typing import Dict, List, Optional, Any, Tuple, TypedDict, Annotated, Literal
import asyncio
import json
import os
import operator
from pathlib import Path

from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

# Import from vinagent
from vinagent.agent import Agent
from vinagent.graph.function_graph import FunctionStateGraph

# LangGraph imports for advanced workflow orchestration
try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.memory import MemorySaver
    from langgraph.prebuilt import ToolExecutor
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

# Import GAAPF agents
from gaapf.agents.base_agent import BaseGAAPFAgent

# Import framework configs
from gaapf.config.framework_configs import SupportedFrameworks, get_framework_config, DEFAULT_FRAMEWORK_CONFIGS

# Import tool integration
from gaapf.tools.tool_integration_manager import get_tool_manager


class ConstellationType(Enum):
    """Types of agent constellations available in the system."""

    KNOWLEDGE_INTENSIVE = "knowledge_intensive"
    HANDS_ON_FOCUSED = "hands_on_focused"
    THEORY_PRACTICE_BALANCED = "theory_practice_balanced"
    BASIC_LEARNING = "basic_learning"
    GUIDED_LEARNING = "guided_learning"
    ASSESSMENT_FOCUSED = "assessment_focused"
    PROJECT_ORIENTED = "project_oriented"
    RESEARCH_INTENSIVE = "research_intensive"


class ConstellationState(TypedDict):
    """State structure for LangGraph-based constellation workflows."""

    # Core state
    messages: Annotated[List[Dict], operator.add]
    user_query: str
    current_agent: str
    session_id: str
    user_id: str

    # Context
    framework: str
    module_id: str
    user_profile: Dict[str, Any]
    learning_context: Dict[str, Any]

    # Agent coordination
    agent_responses: Dict[str, Any]
    handoff_history: List[str]
    confidence_scores: Dict[str, float]

    # Tools and resources
    available_tools: List[str]
    tool_results: Dict[str, Any]

    # Learning tracking
    learning_progress: Dict[str, Any]
    assessment_results: Dict[str, Any]

    # Workflow control
    next_action: str
    workflow_complete: bool
    error_state: Optional[str]


class ConstellationConfig:
    """Configuration for a specific constellation type."""

    def __init__(
        self,
        primary_agents: List[str],
        support_agents: List[str],
        description: str,
        best_for: List[str],
        workflow_type: str = "sequential",
        max_iterations: int = 5,
        handoff_threshold: float = 0.6
    ):
        self.primary_agents = primary_agents
        self.support_agents = support_agents
        self.description = description
        self.best_for = best_for
        self.workflow_type = workflow_type  # sequential, parallel, conditional
        self.max_iterations = max_iterations
        self.handoff_threshold = handoff_threshold


class ConstellationWorkflow:
    """LangGraph-based workflow for agent constellation coordination."""

    def __init__(self, constellation_config: ConstellationConfig, agents: Dict[str, BaseGAAPFAgent]):
        """
        Initialize the constellation workflow.

        Args:
            constellation_config: Configuration for the constellation
            agents: Dictionary of available agents
        """
        self.config = constellation_config
        self.agents = agents
        self.workflow = None
        self.memory = MemorySaver() if LANGGRAPH_AVAILABLE else None
        self._build_workflow()

    def _build_workflow(self):
        """Build the LangGraph workflow based on constellation configuration."""
        if not LANGGRAPH_AVAILABLE:
            return

        # Create the state graph
        workflow = StateGraph(ConstellationState)

        # Add agent nodes
        for agent_name in self.config.primary_agents + self.config.support_agents:
            if agent_name in self.agents:
                workflow.add_node(agent_name, self._create_agent_node(agent_name))

        # Add coordination nodes
        workflow.add_node("coordinator", self._coordinator_node)
        workflow.add_node("handoff_analyzer", self._handoff_analyzer_node)
        workflow.add_node("response_synthesizer", self._response_synthesizer_node)

        # Add workflow logic based on type
        if self.config.workflow_type == "sequential":
            self._add_sequential_edges(workflow)
        elif self.config.workflow_type == "conditional":
            self._add_conditional_edges(workflow)
        elif self.config.workflow_type == "parallel":
            self._add_parallel_edges(workflow)

        # Set entry point
        workflow.set_entry_point("coordinator")

        # Compile the workflow
        self.workflow = workflow.compile(checkpointer=self.memory)

    def _create_agent_node(self, agent_name: str):
        """Create a node function for a specific agent."""
        def agent_node(state: ConstellationState):
            agent = self.agents[agent_name]

            # Get the user query
            user_query = state["user_query"]
            user_id = state["user_id"]

            # Process through the agent
            try:
                # This would be the actual agent invocation
                response = asyncio.run(agent.ainvoke(
                    query=user_query,
                    user_id=user_id
                ))

                # Update state
                new_messages = [{
                    "agent": agent_name,
                    "content": response.content if hasattr(response, 'content') else str(response),
                    "timestamp": asyncio.get_event_loop().time()
                }]

                agent_responses = state["agent_responses"].copy()
                agent_responses[agent_name] = response

                return {
                    **state,
                    "messages": new_messages,
                    "current_agent": agent_name,
                    "agent_responses": agent_responses
                }

            except Exception as e:
                return {
                    **state,
                    "error_state": f"Error in {agent_name}: {str(e)}",
                    "workflow_complete": True
                }

        return agent_node

    def _coordinator_node(self, state: ConstellationState):
        """Coordinator node that manages the overall workflow."""
        # Determine the best initial agent based on query analysis
        user_query = state["user_query"]

        # Calculate confidence scores for all agents
        confidence_scores = {}
        for agent_name, agent in self.agents.items():
            confidence = agent.get_confidence_score(user_query)
            confidence_scores[agent_name] = confidence

        # Select the best agent
        best_agent = max(confidence_scores, key=confidence_scores.get)

        return {
            **state,
            "current_agent": best_agent,
            "confidence_scores": confidence_scores,
            "next_action": "process_with_agent"
        }

    def _handoff_analyzer_node(self, state: ConstellationState):
        """Analyze if handoff to another agent is needed."""
        current_agent_name = state["current_agent"]
        current_agent = self.agents[current_agent_name]

        # Get the latest response
        if current_agent_name in state["agent_responses"]:
            response = state["agent_responses"][current_agent_name]
            content = response.content if hasattr(response, 'content') else str(response)

            # Analyze for handoff
            handoff_analysis = current_agent._analyze_content_for_handoff(
                content, state["user_query"]
            )

            if (handoff_analysis.get("needs_handoff", False) and
                handoff_analysis.get("confidence", 0) >= self.config.handoff_threshold):

                suggested_agent = handoff_analysis.get("suggested_agent")
                if suggested_agent in self.agents:
                    handoff_history = state["handoff_history"] + [current_agent_name]

                    # Prevent infinite loops
                    if len(handoff_history) < self.config.max_iterations:
                        return {
                            **state,
                            "current_agent": suggested_agent,
                            "handoff_history": handoff_history,
                            "next_action": "process_with_agent"
                        }

        # No handoff needed or max iterations reached
        return {
            **state,
            "next_action": "synthesize_response"
        }

    def _response_synthesizer_node(self, state: ConstellationState):
        """Synthesize the final response from all agent interactions."""
        # Get all agent responses
        agent_responses = state["agent_responses"]

        if not agent_responses:
            return {
                **state,
                "workflow_complete": True,
                "error_state": "No agent responses available"
            }

        # For now, use the last agent's response
        # In a more sophisticated implementation, this could combine multiple responses
        current_agent = state["current_agent"]
        final_response = agent_responses.get(current_agent)

        return {
            **state,
            "workflow_complete": True,
            "final_response": final_response
        }

    def _add_sequential_edges(self, workflow):
        """Add edges for sequential workflow."""
        # Coordinator -> Agent -> Handoff Analyzer -> Response Synthesizer
        workflow.add_conditional_edges(
            "coordinator",
            self._route_to_agent,
            {agent: agent for agent in self.agents.keys()}
        )

        # All agents -> Handoff Analyzer
        for agent_name in self.agents.keys():
            workflow.add_edge(agent_name, "handoff_analyzer")

        # Handoff Analyzer -> Agent or Response Synthesizer
        workflow.add_conditional_edges(
            "handoff_analyzer",
            self._route_after_handoff_analysis,
            {
                **{agent: agent for agent in self.agents.keys()},
                "synthesize": "response_synthesizer"
            }
        )

        # Response Synthesizer -> END
        workflow.add_edge("response_synthesizer", END)

    def _add_conditional_edges(self, workflow):
        """Add edges for conditional workflow with complex routing."""
        # More sophisticated routing based on query type and context
        workflow.add_conditional_edges(
            "coordinator",
            self._intelligent_route_to_agent,
            {agent: agent for agent in self.agents.keys()}
        )

        # Dynamic routing from agents
        for agent_name in self.agents.keys():
            workflow.add_conditional_edges(
                agent_name,
                self._dynamic_agent_routing,
                {
                    **{agent: agent for agent in self.agents.keys()},
                    "analyze": "handoff_analyzer",
                    "synthesize": "response_synthesizer"
                }
            )

        workflow.add_conditional_edges(
            "handoff_analyzer",
            self._route_after_handoff_analysis,
            {
                **{agent: agent for agent in self.agents.keys()},
                "synthesize": "response_synthesizer"
            }
        )

        workflow.add_edge("response_synthesizer", END)

    def _add_parallel_edges(self, workflow):
        """Add edges for parallel workflow (multiple agents working simultaneously)."""
        # This would implement parallel processing logic
        # For now, fall back to sequential
        self._add_sequential_edges(workflow)

    def _route_to_agent(self, state: ConstellationState) -> str:
        """Route to the selected agent."""
        return state["current_agent"]

    def _route_after_handoff_analysis(self, state: ConstellationState) -> str:
        """Route after handoff analysis."""
        if state["next_action"] == "process_with_agent":
            return state["current_agent"]
        else:
            return "synthesize"

    def _intelligent_route_to_agent(self, state: ConstellationState) -> str:
        """Intelligent routing based on query analysis."""
        # This could implement more sophisticated routing logic
        return state["current_agent"]

    def _dynamic_agent_routing(self, state: ConstellationState) -> str:
        """Dynamic routing from agent nodes."""
        # Check if immediate handoff is needed
        current_agent_name = state["current_agent"]
        if current_agent_name in state["agent_responses"]:
            current_agent = self.agents[current_agent_name]
            response = state["agent_responses"][current_agent_name]
            content = response.content if hasattr(response, 'content') else str(response)

            handoff_analysis = current_agent._analyze_content_for_handoff(
                content, state["user_query"]
            )

            if (handoff_analysis.get("needs_handoff", False) and
                handoff_analysis.get("confidence", 0) >= 0.8):  # Higher threshold for immediate handoff
                suggested_agent = handoff_analysis.get("suggested_agent")
                if suggested_agent in self.agents:
                    return suggested_agent

        return "analyze"

    async def run(self, initial_state: ConstellationState, config: Dict[str, Any] = None) -> ConstellationState:
        """
        Run the constellation workflow.

        Args:
            initial_state: Initial state for the workflow
            config: Optional configuration for the workflow run

        Returns:
            Final state after workflow completion
        """
        if not self.workflow:
            raise RuntimeError("Workflow not initialized. LangGraph may not be available.")

        # Set default config
        if config is None:
            config = {"configurable": {"thread_id": initial_state["session_id"]}}

        # Run the workflow
        final_state = await self.workflow.ainvoke(initial_state, config)
        return final_state


class ConstellationManager:
    """
    Manages the formation and execution of agent constellations.
    
    The ConstellationManager is responsible for:
    1. Selecting the optimal constellation type based on user profile and context
    2. Creating and configuring agent constellations
    3. Managing agent handoffs and communication
    4. Tracking constellation effectiveness
    """
    
    def __init__(self, config_path: Path = None, data_path: Path = None):
        """
        Initialize the ConstellationManager.

        Args:
            config_path: Path to constellation configurations
            data_path: Path to data directory
        """
        self.constellations = {}
        self.constellation_workflows = {}  # Store LangGraph workflows
        self.constellation_configs = self._load_constellation_configs(config_path)
        self.framework_configs = DEFAULT_FRAMEWORK_CONFIGS
        self.data_path = data_path or Path("data")
        self.tool_manager = None
        if data_path:
            try:
                self.tool_manager = get_tool_manager(data_path)
            except Exception as e:
                print(f"Warning: Could not initialize tool manager: {e}")
        
    def _load_constellation_configs(self, config_path: Optional[Path] = None) -> Dict[ConstellationType, ConstellationConfig]:
        """
        Load constellation configurations from file or use defaults.
        
        Args:
            config_path: Path to constellation configurations JSON file
            
        Returns:
            Dictionary mapping constellation types to their configurations
        """
        # Default configurations if no file is provided
        default_configs = {
            ConstellationType.KNOWLEDGE_INTENSIVE: ConstellationConfig(
                primary_agents=["instructor", "documentation_expert", "knowledge_synthesizer"],
                support_agents=["research_assistant", "progress_tracker"],
                description="Focus on theoretical understanding and knowledge acquisition",
                best_for=["Conceptual learning", "Theoretical foundations"],
                workflow_type="sequential",
                max_iterations=4
            ),
            ConstellationType.HANDS_ON_FOCUSED: ConstellationConfig(
                primary_agents=["code_assistant", "practice_facilitator", "project_guide"],
                support_agents=["troubleshooter", "mentor"],
                description="Focus on practical implementation and coding skills",
                best_for=["Learning by doing", "Practical skills"],
                workflow_type="conditional",
                max_iterations=6
            ),
            ConstellationType.THEORY_PRACTICE_BALANCED: ConstellationConfig(
                primary_agents=["instructor", "code_assistant", "practice_facilitator"],
                support_agents=["documentation_expert", "mentor"],
                description="Balanced approach between theory and practice",
                best_for=["Comprehensive understanding", "Balanced learning"],
                workflow_type="conditional",
                max_iterations=5
            ),
            ConstellationType.BASIC_LEARNING: ConstellationConfig(
                primary_agents=["instructor", "code_assistant"],
                support_agents=["mentor", "practice_facilitator"],
                description="Gentle introduction for beginners",
                best_for=["Beginners", "Foundational learning"],
                workflow_type="sequential",
                max_iterations=3
            ),
            ConstellationType.GUIDED_LEARNING: ConstellationConfig(
                primary_agents=["instructor", "mentor"],
                support_agents=["code_assistant", "practice_facilitator"],
                description="Structured guidance with personalized support",
                best_for=["Users needing extra support", "Structured learning paths"],
                workflow_type="sequential",
                max_iterations=4
            ),
            ConstellationType.ASSESSMENT_FOCUSED: ConstellationConfig(
                primary_agents=["assessment", "progress_tracker"],
                support_agents=["instructor", "mentor"],
                description="Focus on evaluation and progress tracking",
                best_for=["Skill assessment", "Progress evaluation"],
                workflow_type="sequential",
                max_iterations=3
            ),
            ConstellationType.PROJECT_ORIENTED: ConstellationConfig(
                primary_agents=["project_guide", "code_assistant", "troubleshooter"],
                support_agents=["mentor", "assessment"],
                description="End-to-end project development support",
                best_for=["Project building", "Real-world applications"],
                workflow_type="conditional",
                max_iterations=8
            ),
            ConstellationType.RESEARCH_INTENSIVE: ConstellationConfig(
                primary_agents=["research_assistant", "knowledge_synthesizer"],
                support_agents=["documentation_expert", "instructor"],
                description="Deep research and information discovery",
                best_for=["Advanced topics", "Research-based learning"],
                workflow_type="conditional",
                max_iterations=6
            )
        }
        
        # If config file exists, load from it (would override defaults)
        if config_path and config_path.exists():
            try:
                with open(config_path, "r") as f:
                    config_data = json.load(f)
                    
                # Process the loaded configurations
                # Implementation would parse the JSON into ConstellationConfig objects
                pass
            except Exception as e:
                print(f"Error loading constellation configs: {e}")
                
        return default_configs
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_id: str,
        llm: BaseLanguageModel
    ) -> Dict[str, BaseGAAPFAgent]:
        """
        Create a new constellation based on type and user profile.
        
        Args:
            constellation_type: Type of constellation to create
            user_profile: User profile information
            framework: Target framework (e.g., "langchain", "langgraph")
            module_id: Current learning module
            session_id: Unique session identifier
            llm: Language model to use for agents
            
        Returns:
            Dictionary of configured agents in the constellation
        """
        config = self.constellation_configs[constellation_type]
        agents = {}
        
        # Create primary agents
        for agent_type in config.primary_agents:
            agent = await self._create_agent(
                agent_type=agent_type,
                user_profile=user_profile,
                framework=framework,
                module_id=module_id,
                session_id=session_id,
                llm=llm,
                is_primary=True
            )
            agents[agent_type] = agent
            
        # Create support agents
        for agent_type in config.support_agents:
            agent = await self._create_agent(
                agent_type=agent_type,
                user_profile=user_profile,
                framework=framework,
                module_id=module_id,
                session_id=session_id,
                llm=llm,
                is_primary=False
            )
            agents[agent_type] = agent
            
        # Create LangGraph workflow if available
        workflow = None
        if LANGGRAPH_AVAILABLE:
            try:
                workflow = ConstellationWorkflow(config, agents)
            except Exception as e:
                print(f"Warning: Could not create LangGraph workflow: {e}")

        # Store the constellation
        self.constellations[session_id] = {
            "type": constellation_type,
            "agents": agents,
            "framework": framework,
            "module_id": module_id,
            "user_profile": user_profile,
            "config": config
        }

        # Store the workflow separately
        if workflow:
            self.constellation_workflows[session_id] = workflow

        return agents
    
    async def _create_agent(
        self,
        agent_type: str,
        user_profile: Dict,
        framework: str,
        module_id: str,
        session_id: str,
        llm: BaseLanguageModel,
        is_primary: bool
    ) -> BaseGAAPFAgent:
        """
        Create and configure an individual agent.
        
        Args:
            agent_type: Type of agent to create
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module
            session_id: Unique session identifier
            llm: Language model to use
            is_primary: Whether this is a primary agent
            
        Returns:
            Configured BaseGAAPFAgent instance
        """
        # Get appropriate tools for this agent type
        tools = []
        if self.tool_manager:
            tools = self.tool_manager.get_recommended_tools_for_agent(agent_type, framework)

        # Create the agent with tools
        from gaapf.agents.base_agent import create_agent

        return create_agent(
            agent_type=agent_type,
            user_profile=user_profile,
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            llm=llm,
            is_primary=is_primary,
            tools=tools,
            data_path=self.data_path
        )
    
    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: Dict,
        framework: str,
        module_id: str
    ) -> Dict:
        """
        Run a learning session with the specified constellation.

        Args:
            session_id: Unique session identifier
            user_message: User's message/question
            user_profile: User profile information
            framework: Target framework
            module_id: Current learning module

        Returns:
            Session result including agent responses
        """
        if session_id not in self.constellations:
            raise ValueError(f"No constellation found for session {session_id}")

        constellation = self.constellations[session_id]

        # Try to use LangGraph workflow if available
        if session_id in self.constellation_workflows:
            return await self._run_langgraph_session(
                session_id, user_message, user_profile, framework, module_id
            )

        # Fall back to traditional approach
        return await self._run_traditional_session(
            session_id, user_message, user_profile, framework, module_id
        )

    async def _run_langgraph_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: Dict,
        framework: str,
        module_id: str
    ) -> Dict:
        """Run session using LangGraph workflow."""
        workflow = self.constellation_workflows[session_id]
        constellation = self.constellations[session_id]

        # Create initial state
        initial_state = ConstellationState(
            messages=[],
            user_query=user_message,
            current_agent="",
            session_id=session_id,
            user_id=user_profile.get("user_id", "unknown_user"),
            framework=framework,
            module_id=module_id,
            user_profile=user_profile,
            learning_context={},
            agent_responses={},
            handoff_history=[],
            confidence_scores={},
            available_tools=[],
            tool_results={},
            learning_progress={},
            assessment_results={},
            next_action="",
            workflow_complete=False,
            error_state=None
        )

        # Run the workflow
        try:
            final_state = await workflow.run(initial_state)

            return {
                "session_id": session_id,
                "response": final_state.get("final_response"),
                "agent_path": final_state.get("handoff_history", []) + [final_state.get("current_agent", "")],
                "workflow_state": final_state,
                "error": final_state.get("error_state")
            }
        except Exception as e:
            return {
                "session_id": session_id,
                "error": f"Workflow execution error: {str(e)}",
                "response": None,
                "agent_path": []
            }

    async def _run_traditional_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: Dict,
        framework: str,
        module_id: str
    ) -> Dict:
        """Run session using traditional approach (fallback)."""
        constellation = self.constellations[session_id]

        # Determine the initial agent based on message content
        initial_agent = await self._select_initial_agent(
            constellation=constellation,
            user_message=user_message
        )

        # Process the message through the selected agent
        response = await initial_agent.ainvoke(
            query=user_message,
            user_id=user_profile.get("user_id", "unknown_user")
        )

        # Check if handoff is needed
        handoff_agent = await self._check_handoff(
            constellation=constellation,
            current_agent=initial_agent,
            response=response,
            user_message=user_message
        )

        # If handoff is needed, process through the next agent
        agent_path = [initial_agent.__class__.__name__]
        if handoff_agent and handoff_agent != initial_agent:
            handoff_response = await handoff_agent.ainvoke(
                query=f"Previous agent response: {response.content}\nUser question: {user_message}",
                user_id=user_profile.get("user_id", "unknown_user")
            )
            response = handoff_response
            agent_path.append(handoff_agent.__class__.__name__)

        return {
            "session_id": session_id,
            "response": response,
            "agent_path": agent_path
        }
    
    async def _select_initial_agent(self, constellation: Dict, user_message: str):
        """
        Select the initial agent to handle the user message.

        Args:
            constellation: Current constellation configuration
            user_message: User's message/question

        Returns:
            Selected agent instance
        """
        agents = constellation["agents"]

        # Calculate confidence scores for all agents
        agent_scores = {}
        for agent_name, agent in agents.items():
            confidence = agent.get_confidence_score(user_message)
            agent_scores[agent_name] = confidence

        # Select the agent with the highest confidence
        best_agent_name = max(agent_scores, key=agent_scores.get)
        return agents[best_agent_name]

    async def _check_handoff(
        self,
        constellation: Dict,
        current_agent,
        response: Any,
        user_message: str
    ):
        """
        Check if a handoff to another agent is needed.

        Args:
            constellation: Current constellation configuration
            current_agent: Current agent handling the request
            response: Current agent's response
            user_message: Original user message

        Returns:
            Next agent if handoff is needed, None otherwise
        """
        # Get handoff analysis from the current agent
        if hasattr(response, 'get') and 'handoff_analysis' in response:
            handoff_analysis = response['handoff_analysis']
        else:
            # If response doesn't have handoff analysis, analyze the content
            content = response.content if hasattr(response, 'content') else str(response)
            handoff_analysis = current_agent._analyze_content_for_handoff(content, user_message)

        # Check if handoff is needed
        if handoff_analysis.get("needs_handoff", False):
            suggested_agent_type = handoff_analysis.get("suggested_agent")
            confidence = handoff_analysis.get("confidence", 0.0)

            # Only handoff if confidence is above threshold
            if confidence >= 0.6 and suggested_agent_type:
                agents = constellation["agents"]
                if suggested_agent_type in agents:
                    return agents[suggested_agent_type]

        return None

    def get_constellation_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a constellation.

        Args:
            session_id: Session identifier

        Returns:
            Constellation information or None if not found
        """
        if session_id not in self.constellations:
            return None

        constellation = self.constellations[session_id]
        config = constellation.get("config")

        return {
            "session_id": session_id,
            "type": constellation["type"].value,
            "framework": constellation["framework"],
            "module_id": constellation["module_id"],
            "agents": list(constellation["agents"].keys()),
            "primary_agents": config.primary_agents if config else [],
            "support_agents": config.support_agents if config else [],
            "workflow_type": config.workflow_type if config else "sequential",
            "has_langgraph_workflow": session_id in self.constellation_workflows
        }

    def list_active_constellations(self) -> List[Dict[str, Any]]:
        """
        List all active constellations.

        Returns:
            List of constellation information
        """
        return [
            self.get_constellation_info(session_id)
            for session_id in self.constellations.keys()
        ]

    def cleanup_constellation(self, session_id: str) -> bool:
        """
        Clean up a constellation and its resources.

        Args:
            session_id: Session identifier

        Returns:
            True if cleanup was successful
        """
        try:
            if session_id in self.constellations:
                del self.constellations[session_id]

            if session_id in self.constellation_workflows:
                del self.constellation_workflows[session_id]

            return True
        except Exception as e:
            print(f"Error cleaning up constellation {session_id}: {e}")
            return False

    def get_constellation_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about constellation usage.

        Returns:
            Dictionary with constellation statistics
        """
        stats = {
            "total_constellations": len(self.constellations),
            "langgraph_workflows": len(self.constellation_workflows),
            "constellation_types": {},
            "framework_distribution": {},
            "workflow_types": {}
        }

        for constellation in self.constellations.values():
            # Count constellation types
            constellation_type = constellation["type"].value
            stats["constellation_types"][constellation_type] = stats["constellation_types"].get(constellation_type, 0) + 1

            # Count frameworks
            framework = constellation["framework"]
            stats["framework_distribution"][framework] = stats["framework_distribution"].get(framework, 0) + 1

            # Count workflow types
            config = constellation.get("config")
            if config:
                workflow_type = config.workflow_type
                stats["workflow_types"][workflow_type] = stats["workflow_types"].get(workflow_type, 0) + 1

        return stats