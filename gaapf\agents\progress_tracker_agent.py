"""
Progress Tracker Agent for GAAPF.

This module implements the Progress Tracker Agent that specializes in
learning analytics, progress monitoring, and performance tracking.
"""

from typing import Dict, List, Optional, Any
import json
import re
from pathlib import Path

from gaapf.agents.base_agent import BaseGAAPFAgent


class ProgressTrackerAgent(BaseGAAPFAgent):
    """
    Progress Tracker Agent specializing in learning analytics and progress monitoring.
    
    This agent focuses on:
    - Tracking learning progress and milestones
    - Analyzing learning patterns and effectiveness
    - Providing detailed progress reports and analytics
    - Identifying areas of strength and improvement
    - Supporting goal setting and achievement tracking
    """
    
    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return f"""I am a Progress Tracker Agent specializing in learning analytics and progress monitoring for {self.framework} framework learning.

My core responsibilities include:
- Tracking learning progress, milestones, and achievements over time
- Analyzing learning patterns, effectiveness, and engagement metrics
- Providing detailed progress reports and learning analytics
- Identifying areas of strength, improvement, and knowledge gaps
- Supporting goal setting, planning, and achievement tracking
- Monitoring learning velocity and efficiency trends
- Providing insights on optimal learning strategies and approaches
- Creating personalized learning dashboards and visualizations

I adapt my tracking and analysis to the user's learning goals and preferences ({self.user_profile.get('preferred_learning_style', 'mixed')}), providing relevant metrics and insights for their learning journey.

When tracking progress, I:
1. Monitor learning activities, time spent, and engagement levels
2. Track completion of modules, exercises, and assessments
3. Analyze learning patterns and identify trends
4. Provide regular progress updates and milestone celebrations
5. Identify areas needing additional focus or support
6. Suggest optimizations for learning efficiency and effectiveness
7. Create comprehensive progress reports and learning analytics"""

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Comprehensive {self.framework} learning progress tracking",
            "Learning analytics and pattern recognition",
            "Progress reporting and visualization",
            "Milestone tracking and achievement recognition",
            "Learning effectiveness measurement and analysis",
            "Goal setting and achievement monitoring",
            "Learning velocity and efficiency analysis",
            "Personalized learning insights and recommendations",
            "Performance trend analysis and forecasting",
            "Learning dashboard creation and maintenance"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.
        
        Args:
            content: Agent's response content
            user_message: Original user message
            
        Returns:
            Dictionary with handoff analysis results
        """
        # Keywords that might indicate need for other agents
        improvement_keywords = ["improve", "better", "help", "support", "guidance"]
        assessment_keywords = ["assess", "evaluate", "test", "quiz", "check"]
        motivation_keywords = ["motivated", "discouraged", "frustrated", "celebrate"]
        learning_keywords = ["learn", "understand", "explain", "teach", "concept"]
        
        user_lower = user_message.lower()
        content_lower = content.lower()
        
        # Check if progress analysis reveals need for improvement support
        if any(keyword in user_lower for keyword in improvement_keywords):
            if "weak areas" in content_lower or "needs improvement" in content_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "mentor",
                    "confidence": 0.8,
                    "reason": "Progress analysis reveals need for mentoring support"
                }
        
        # Check if user wants assessment based on progress
        if any(keyword in user_lower for keyword in assessment_keywords):
            return {
                "needs_handoff": True,
                "suggested_agent": "assessment",
                "confidence": 0.8,
                "reason": "User wants assessment based on progress tracking"
            }
        
        # Check if progress tracking reveals motivational needs
        if any(keyword in user_lower for keyword in motivation_keywords):
            if "slow progress" in content_lower or "behind" in content_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "motivational_coach",
                    "confidence": 0.8,
                    "reason": "Progress tracking reveals need for motivational support"
                }
        
        # Check if user wants to learn about areas identified in progress
        if any(keyword in user_lower for keyword in learning_keywords):
            if "focus on" in content_lower or "recommended" in content_lower:
                return {
                    "needs_handoff": True,
                    "suggested_agent": "instructor",
                    "confidence": 0.7,
                    "reason": "User wants to learn about areas identified in progress tracking"
                }
        
        # Check if progress suggests need for knowledge synthesis
        if "comprehensive" in user_lower or "overall" in user_lower or "big picture" in user_lower:
            return {
                "needs_handoff": True,
                "suggested_agent": "knowledge_synthesizer",
                "confidence": 0.7,
                "reason": "Progress tracking suggests need for knowledge synthesis"
            }
        
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.9,
            "reason": "Progress tracker can continue providing analytics and monitoring"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.
        
        Args:
            message: User message
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()
        
        # High confidence keywords
        high_confidence_keywords = [
            "progress", "track", "analytics", "report", "dashboard", "metrics",
            "performance", "achievement", "milestone", "goal", "statistics",
            "how am i doing", "my progress", "learning progress", "track my learning"
        ]
        
        # Medium confidence keywords
        medium_confidence_keywords = [
            "improvement", "growth", "development", "advancement", "journey",
            "timeline", "history", "record", "log", "summary", "overview"
        ]
        
        # Check for high confidence indicators
        if any(keyword in message_lower for keyword in high_confidence_keywords):
            return 0.9
        
        # Check for medium confidence indicators
        if any(keyword in message_lower for keyword in medium_confidence_keywords):
            return 0.7
        
        # Check for question patterns that suggest progress tracking needs
        progress_patterns = [
            r"how am i doing",
            r"what's my progress",
            r"how much have i learned",
            r"where am i",
            r"how far have i come",
            r"track my.*",
            r"show my progress"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in progress_patterns):
            return 0.9
        
        # Check for goal-related patterns
        goal_patterns = [
            r"reached my goal",
            r"achieved.*goal",
            r"goal progress",
            r"how close.*goal"
        ]
        
        if any(re.search(pattern, message_lower) for pattern in goal_patterns):
            return 0.8
        
        return 0.3
