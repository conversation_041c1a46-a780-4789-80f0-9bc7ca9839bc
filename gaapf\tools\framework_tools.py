"""
Framework context tools for GAAPF agents.

This module provides tools that agents can use to query framework information
from the database during conversations, enabling them to provide accurate
and contextual responses about specific frameworks and modules.
"""

import json
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, ClassVar

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field, ConfigDict

from gaapf.database.models import DatabaseManager


class FrameworkInfoInput(BaseModel):
    """Input schema for framework information tool."""
    framework_id: str = Field(description="Framework identifier (e.g., 'langchain', 'langgraph')")


class ModuleInfoInput(BaseModel):
    """Input schema for module information tool."""
    framework_id: str = Field(description="Framework identifier")
    module_id: str = Field(description="Module identifier")


class FrameworkSearchInput(BaseModel):
    """Input schema for framework search tool."""
    query: str = Field(description="Search query for framework content")
    framework_id: Optional[str] = Field(default=None, description="Optional framework to limit search to")


class FrameworkInfoTool(BaseTool):
    """Tool for getting framework information."""

    name: str = "get_framework_info"
    description: str = """Get detailed information about a specific framework including its description,
    version, learning paths, and available resources. Use this when you need to provide information
    about a framework's capabilities, structure, or general information."""
    args_schema: type = FrameworkInfoInput

    def __init__(self, db_manager: DatabaseManager, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'db_manager', db_manager)
    
    def _run(self, framework_id: str) -> str:
        """Get framework information."""
        try:
            # Get framework info
            framework_info = self.db_manager.get_framework_info(framework_id)
            if not framework_info:
                return f"Framework '{framework_id}' not found in the database."
            
            # Get modules for this framework
            modules = self.db_manager.get_framework_modules(framework_id)
            
            # Format the response
            result = {
                "framework": framework_info,
                "modules_count": len(modules),
                "available_modules": [
                    {
                        "module_id": m["module_id"],
                        "title": m["title"],
                        "difficulty": m["difficulty"],
                        "estimated_minutes": m["estimated_minutes"]
                    }
                    for m in modules
                ]
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error retrieving framework information: {str(e)}"


class ModuleInfoTool(BaseTool):
    """Tool for getting module information."""

    name: str = "get_module_info"
    description: str = """Get detailed information about a specific learning module including its
    description, difficulty level, topics covered, prerequisites, and estimated completion time.
    Use this when you need to provide specific information about a learning module."""
    args_schema: type = ModuleInfoInput

    def __init__(self, db_manager: DatabaseManager, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'db_manager', db_manager)
    
    def _run(self, framework_id: str, module_id: str) -> str:
        """Get module information."""
        try:
            module_info = self.db_manager.get_module_info(framework_id, module_id)
            if not module_info:
                return f"Module '{module_id}' not found for framework '{framework_id}'."
            
            return json.dumps(module_info, indent=2)
            
        except Exception as e:
            return f"Error retrieving module information: {str(e)}"


class FrameworkSearchTool(BaseTool):
    """Tool for searching framework content."""

    name: str = "search_framework_content"
    description: str = """Search for information across frameworks and modules based on a query.
    This tool searches through framework names, descriptions, module titles, descriptions, and topics.
    Use this when you need to find relevant information about specific concepts, features, or topics."""
    args_schema: type = FrameworkSearchInput

    def __init__(self, db_manager: DatabaseManager, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'db_manager', db_manager)
    
    def _run(self, query: str, framework_id: Optional[str] = None) -> str:
        """Search framework content."""
        try:
            results = self.db_manager.search_framework_content(query, framework_id)
            
            if not results:
                search_scope = f"in framework '{framework_id}'" if framework_id else "across all frameworks"
                return f"No results found for query '{query}' {search_scope}."
            
            # Format results
            formatted_results = {
                "query": query,
                "framework_filter": framework_id,
                "results_count": len(results),
                "results": results
            }
            
            return json.dumps(formatted_results, indent=2)
            
        except Exception as e:
            return f"Error searching framework content: {str(e)}"


class ListFrameworksTool(BaseTool):
    """Tool for listing all available frameworks."""

    name: str = "list_frameworks"
    description: str = """Get a list of all available frameworks in the system with their basic information.
    Use this when you need to show what frameworks are available for learning."""

    def __init__(self, db_manager: DatabaseManager, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'db_manager', db_manager)
    
    def _run(self) -> str:
        """List all frameworks."""
        try:
            frameworks = self.db_manager.get_all_frameworks()
            
            if not frameworks:
                return "No frameworks found in the database."
            
            result = {
                "frameworks_count": len(frameworks),
                "frameworks": frameworks
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error listing frameworks: {str(e)}"


class FrameworkModulesTool(BaseTool):
    """Tool for getting all modules of a framework."""

    name: str = "get_framework_modules"
    description: str = """Get all learning modules available for a specific framework with their details.
    Use this when you need to show what modules are available for learning in a particular framework."""
    args_schema: type = FrameworkInfoInput

    def __init__(self, db_manager: DatabaseManager, **kwargs):
        super().__init__(**kwargs)
        object.__setattr__(self, 'db_manager', db_manager)
    
    def _run(self, framework_id: str) -> str:
        """Get all modules for a framework."""
        try:
            modules = self.db_manager.get_framework_modules(framework_id)
            
            if not modules:
                return f"No modules found for framework '{framework_id}'."
            
            result = {
                "framework_id": framework_id,
                "modules_count": len(modules),
                "modules": modules
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error retrieving framework modules: {str(e)}"


class FrameworkToolsFactory:
    """Singleton factory for creating framework tools with database access."""

    _instance: ClassVar[Optional['FrameworkToolsFactory']] = None
    _lock: ClassVar[threading.Lock] = threading.Lock()
    _tools_cache: ClassVar[Optional[List[BaseTool]]] = None

    def __new__(cls, data_path: Path):
        """Ensure singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, data_path: Path):
        """
        Initialize the tools factory.

        Args:
            data_path: Path to the data directory containing the database
        """
        # Only initialize once
        if hasattr(self, '_is_initialized'):
            return

        self.data_path = data_path
        self.db_path = data_path / "frameworks.db"
        self.db_manager = DatabaseManager(self.db_path)
        self._is_initialized = True
    
    def create_framework_tools(self) -> List[BaseTool]:
        """
        Create all framework tools with caching.

        Returns:
            List of framework tools
        """
        # Use cached tools if available
        if self._tools_cache is not None:
            return self._tools_cache

        with self._lock:
            # Double-check pattern
            if self._tools_cache is not None:
                return self._tools_cache

            # Create tools
            tools = [
                FrameworkInfoTool(self.db_manager),
                ModuleInfoTool(self.db_manager),
                FrameworkSearchTool(self.db_manager),
                ListFrameworksTool(self.db_manager),
                FrameworkModulesTool(self.db_manager)
            ]

            # Cache the tools
            self._tools_cache = tools
            return tools
    
    def get_tool_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions of all available tools.

        Returns:
            Dictionary mapping tool names to descriptions
        """
        tools = self.create_framework_tools()
        return {tool.name: tool.description for tool in tools}

    @classmethod
    def get_instance(cls, data_path: Path = None) -> 'FrameworkToolsFactory':
        """
        Get the singleton instance of the factory.

        Args:
            data_path: Path to data directory (only used for first initialization)

        Returns:
            Singleton instance of FrameworkToolsFactory
        """
        if cls._instance is None:
            if data_path is None:
                raise ValueError("data_path is required for first initialization")
            return cls(data_path)
        return cls._instance

    @classmethod
    def reset_instance(cls):
        """Reset the singleton instance (mainly for testing)."""
        with cls._lock:
            cls._instance = None
            cls._tools_cache = None
