"""
Real-Time Analytics System for GAAPF.

This module implements comprehensive learning metrics tracking, effectiveness measurement,
and adaptive learning optimization as specified in the methodology.
"""

import time
import statistics
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import threading
import asyncio


@dataclass
class LearningMetric:
    """Represents a learning metric measurement."""
    metric_id: str
    user_id: str
    session_id: str
    framework: str
    module_id: str
    metric_type: str
    value: float
    timestamp: float
    metadata: Dict[str, Any]


@dataclass
class EngagementMetrics:
    """Engagement-related metrics."""
    session_duration: float
    interaction_count: int
    question_count: int
    response_time_avg: float
    attention_score: float
    completion_rate: float


@dataclass
class LearningEffectivenessMetrics:
    """Learning effectiveness metrics."""
    comprehension_score: float
    retention_score: float
    application_score: float
    progress_velocity: float
    concept_mastery_rate: float
    error_recovery_rate: float


@dataclass
class AdaptiveMetrics:
    """Adaptive learning optimization metrics."""
    difficulty_appropriateness: float
    learning_style_alignment: float
    constellation_effectiveness: float
    tool_usage_efficiency: float
    handoff_success_rate: float
    personalization_score: float


class RealTimeAnalyticsEngine:
    """
    Real-time analytics engine for learning metrics tracking.
    
    This engine provides:
    - Real-time metric collection and processing
    - Learning effectiveness measurement
    - Adaptive optimization recommendations
    - Performance trend analysis
    - Anomaly detection in learning patterns
    """
    
    def __init__(self, data_path: Path = Path("data/analytics")):
        """
        Initialize the analytics engine.
        
        Args:
            data_path: Path to store analytics data
        """
        self.data_path = data_path
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # Real-time metric storage
        self.live_metrics = defaultdict(deque)  # metric_type -> deque of metrics
        self.session_metrics = defaultdict(dict)  # session_id -> metrics
        self.user_metrics = defaultdict(lambda: defaultdict(list))  # user_id -> metric_type -> values
        
        # Analytics state
        self.metric_processors = {}
        self.alert_thresholds = {}
        self.trend_analyzers = {}
        
        # Threading for real-time processing
        self.processing_lock = threading.Lock()
        self.is_running = False
        self.processing_thread = None
        
        # Initialize default processors
        self._initialize_default_processors()
        self._load_analytics_data()
    
    def start_real_time_processing(self):
        """Start real-time analytics processing."""
        if not self.is_running:
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._processing_loop)
            self.processing_thread.daemon = True
            self.processing_thread.start()
    
    def stop_real_time_processing(self):
        """Stop real-time analytics processing."""
        self.is_running = False
        if self.processing_thread:
            self.processing_thread.join()
    
    def record_metric(self, metric: LearningMetric):
        """
        Record a learning metric.
        
        Args:
            metric: LearningMetric to record
        """
        with self.processing_lock:
            # Add to live metrics
            self.live_metrics[metric.metric_type].append(metric)
            
            # Limit live metrics size
            if len(self.live_metrics[metric.metric_type]) > 1000:
                self.live_metrics[metric.metric_type].popleft()
            
            # Update session metrics
            if metric.session_id not in self.session_metrics:
                self.session_metrics[metric.session_id] = {}
            
            if metric.metric_type not in self.session_metrics[metric.session_id]:
                self.session_metrics[metric.session_id][metric.metric_type] = []
            
            self.session_metrics[metric.session_id][metric.metric_type].append(metric)
            
            # Update user metrics
            self.user_metrics[metric.user_id][metric.metric_type].append(metric)
    
    def record_engagement_metrics(
        self, 
        user_id: str, 
        session_id: str, 
        framework: str,
        module_id: str,
        metrics: EngagementMetrics
    ):
        """Record engagement metrics."""
        timestamp = time.time()
        
        for field_name, value in asdict(metrics).items():
            metric = LearningMetric(
                metric_id=f"engagement_{field_name}_{timestamp}",
                user_id=user_id,
                session_id=session_id,
                framework=framework,
                module_id=module_id,
                metric_type=f"engagement.{field_name}",
                value=value,
                timestamp=timestamp,
                metadata={"category": "engagement"}
            )
            self.record_metric(metric)
    
    def record_effectiveness_metrics(
        self,
        user_id: str,
        session_id: str,
        framework: str,
        module_id: str,
        metrics: LearningEffectivenessMetrics
    ):
        """Record learning effectiveness metrics."""
        timestamp = time.time()
        
        for field_name, value in asdict(metrics).items():
            metric = LearningMetric(
                metric_id=f"effectiveness_{field_name}_{timestamp}",
                user_id=user_id,
                session_id=session_id,
                framework=framework,
                module_id=module_id,
                metric_type=f"effectiveness.{field_name}",
                value=value,
                timestamp=timestamp,
                metadata={"category": "effectiveness"}
            )
            self.record_metric(metric)
    
    def record_adaptive_metrics(
        self,
        user_id: str,
        session_id: str,
        framework: str,
        module_id: str,
        metrics: AdaptiveMetrics
    ):
        """Record adaptive learning metrics."""
        timestamp = time.time()
        
        for field_name, value in asdict(metrics).items():
            metric = LearningMetric(
                metric_id=f"adaptive_{field_name}_{timestamp}",
                user_id=user_id,
                session_id=session_id,
                framework=framework,
                module_id=module_id,
                metric_type=f"adaptive.{field_name}",
                value=value,
                timestamp=timestamp,
                metadata={"category": "adaptive"}
            )
            self.record_metric(metric)
    
    def get_real_time_dashboard(self, user_id: str = None) -> Dict[str, Any]:
        """
        Get real-time dashboard data.
        
        Args:
            user_id: Optional user ID to filter metrics
            
        Returns:
            Dashboard data with current metrics and trends
        """
        dashboard = {
            "timestamp": time.time(),
            "active_sessions": len(self.session_metrics),
            "total_metrics": sum(len(metrics) for metrics in self.live_metrics.values()),
            "engagement_summary": {},
            "effectiveness_summary": {},
            "adaptive_summary": {},
            "trends": {},
            "alerts": []
        }
        
        # Calculate engagement summary
        engagement_metrics = self._get_recent_metrics_by_category("engagement", user_id)
        if engagement_metrics:
            dashboard["engagement_summary"] = {
                "avg_session_duration": statistics.mean([m.value for m in engagement_metrics if "session_duration" in m.metric_type]),
                "avg_interaction_count": statistics.mean([m.value for m in engagement_metrics if "interaction_count" in m.metric_type]),
                "avg_attention_score": statistics.mean([m.value for m in engagement_metrics if "attention_score" in m.metric_type])
            }
        
        # Calculate effectiveness summary
        effectiveness_metrics = self._get_recent_metrics_by_category("effectiveness", user_id)
        if effectiveness_metrics:
            dashboard["effectiveness_summary"] = {
                "avg_comprehension": statistics.mean([m.value for m in effectiveness_metrics if "comprehension_score" in m.metric_type]),
                "avg_retention": statistics.mean([m.value for m in effectiveness_metrics if "retention_score" in m.metric_type]),
                "avg_progress_velocity": statistics.mean([m.value for m in effectiveness_metrics if "progress_velocity" in m.metric_type])
            }
        
        # Calculate adaptive summary
        adaptive_metrics = self._get_recent_metrics_by_category("adaptive", user_id)
        if adaptive_metrics:
            dashboard["adaptive_summary"] = {
                "avg_difficulty_appropriateness": statistics.mean([m.value for m in adaptive_metrics if "difficulty_appropriateness" in m.metric_type]),
                "avg_constellation_effectiveness": statistics.mean([m.value for m in adaptive_metrics if "constellation_effectiveness" in m.metric_type]),
                "avg_personalization_score": statistics.mean([m.value for m in adaptive_metrics if "personalization_score" in m.metric_type])
            }
        
        # Calculate trends
        dashboard["trends"] = self._calculate_trends(user_id)
        
        # Check for alerts
        dashboard["alerts"] = self._check_alerts(user_id)
        
        return dashboard
    
    def analyze_learning_effectiveness(self, user_id: str, time_window_hours: int = 24) -> Dict[str, Any]:
        """
        Analyze learning effectiveness for a user.
        
        Args:
            user_id: User identifier
            time_window_hours: Time window for analysis
            
        Returns:
            Effectiveness analysis results
        """
        cutoff_time = time.time() - (time_window_hours * 3600)
        
        # Get user metrics in time window
        user_metrics = []
        for metric_type, metrics in self.user_metrics[user_id].items():
            for metric in metrics:
                if metric.timestamp >= cutoff_time:
                    user_metrics.append(metric)
        
        if not user_metrics:
            return {"error": "No metrics available for analysis"}
        
        # Group metrics by category
        engagement_metrics = [m for m in user_metrics if m.metadata.get("category") == "engagement"]
        effectiveness_metrics = [m for m in user_metrics if m.metadata.get("category") == "effectiveness"]
        adaptive_metrics = [m for m in user_metrics if m.metadata.get("category") == "adaptive"]
        
        analysis = {
            "user_id": user_id,
            "time_window_hours": time_window_hours,
            "total_metrics": len(user_metrics),
            "engagement_analysis": self._analyze_engagement(engagement_metrics),
            "effectiveness_analysis": self._analyze_effectiveness(effectiveness_metrics),
            "adaptive_analysis": self._analyze_adaptive(adaptive_metrics),
            "overall_score": 0.0,
            "recommendations": []
        }
        
        # Calculate overall score
        scores = []
        if analysis["engagement_analysis"]["score"] > 0:
            scores.append(analysis["engagement_analysis"]["score"])
        if analysis["effectiveness_analysis"]["score"] > 0:
            scores.append(analysis["effectiveness_analysis"]["score"])
        if analysis["adaptive_analysis"]["score"] > 0:
            scores.append(analysis["adaptive_analysis"]["score"])
        
        if scores:
            analysis["overall_score"] = statistics.mean(scores)
        
        # Generate recommendations
        analysis["recommendations"] = self._generate_recommendations(analysis)
        
        return analysis
    
    def _get_recent_metrics_by_category(self, category: str, user_id: str = None, hours: int = 1) -> List[LearningMetric]:
        """Get recent metrics by category."""
        cutoff_time = time.time() - (hours * 3600)
        metrics = []
        
        for metric_type, metric_list in self.live_metrics.items():
            for metric in metric_list:
                if (metric.timestamp >= cutoff_time and 
                    metric.metadata.get("category") == category and
                    (user_id is None or metric.user_id == user_id)):
                    metrics.append(metric)
        
        return metrics
    
    def _calculate_trends(self, user_id: str = None) -> Dict[str, Any]:
        """Calculate metric trends."""
        trends = {}
        
        # Calculate trends for key metrics
        key_metrics = [
            "engagement.session_duration",
            "effectiveness.comprehension_score",
            "adaptive.constellation_effectiveness"
        ]
        
        for metric_type in key_metrics:
            if metric_type in self.live_metrics:
                recent_values = []
                for metric in self.live_metrics[metric_type]:
                    if user_id is None or metric.user_id == user_id:
                        recent_values.append(metric.value)
                
                if len(recent_values) >= 2:
                    # Simple trend calculation
                    if len(recent_values) >= 10:
                        first_half = recent_values[:len(recent_values)//2]
                        second_half = recent_values[len(recent_values)//2:]
                        
                        first_avg = statistics.mean(first_half)
                        second_avg = statistics.mean(second_half)
                        
                        trend = "improving" if second_avg > first_avg else "declining"
                        change_percent = ((second_avg - first_avg) / first_avg) * 100 if first_avg > 0 else 0
                        
                        trends[metric_type] = {
                            "trend": trend,
                            "change_percent": change_percent,
                            "current_value": recent_values[-1],
                            "sample_size": len(recent_values)
                        }
        
        return trends
    
    def _check_alerts(self, user_id: str = None) -> List[Dict[str, Any]]:
        """Check for metric alerts."""
        alerts = []
        
        # Define alert conditions
        alert_conditions = {
            "low_engagement": {
                "metric": "engagement.attention_score",
                "threshold": 0.3,
                "condition": "below"
            },
            "poor_comprehension": {
                "metric": "effectiveness.comprehension_score",
                "threshold": 0.5,
                "condition": "below"
            },
            "ineffective_constellation": {
                "metric": "adaptive.constellation_effectiveness",
                "threshold": 0.4,
                "condition": "below"
            }
        }
        
        for alert_name, condition in alert_conditions.items():
            metric_type = condition["metric"]
            if metric_type in self.live_metrics:
                recent_metrics = [
                    m for m in self.live_metrics[metric_type]
                    if (user_id is None or m.user_id == user_id) and
                       m.timestamp >= time.time() - 3600  # Last hour
                ]
                
                if recent_metrics:
                    avg_value = statistics.mean([m.value for m in recent_metrics])
                    
                    if condition["condition"] == "below" and avg_value < condition["threshold"]:
                        alerts.append({
                            "alert_type": alert_name,
                            "metric": metric_type,
                            "current_value": avg_value,
                            "threshold": condition["threshold"],
                            "severity": "high" if avg_value < condition["threshold"] * 0.8 else "medium"
                        })
        
        return alerts

    def _analyze_engagement(self, metrics: List[LearningMetric]) -> Dict[str, Any]:
        """Analyze engagement metrics."""
        if not metrics:
            return {"score": 0.0, "insights": [], "recommendations": []}

        # Calculate engagement score
        attention_scores = [m.value for m in metrics if "attention_score" in m.metric_type]
        interaction_counts = [m.value for m in metrics if "interaction_count" in m.metric_type]
        session_durations = [m.value for m in metrics if "session_duration" in m.metric_type]

        score_components = []
        insights = []
        recommendations = []

        if attention_scores:
            avg_attention = statistics.mean(attention_scores)
            score_components.append(avg_attention)

            if avg_attention < 0.5:
                insights.append("Low attention scores detected")
                recommendations.append("Consider shorter learning sessions or more interactive content")
            elif avg_attention > 0.8:
                insights.append("High attention levels maintained")

        if interaction_counts:
            avg_interactions = statistics.mean(interaction_counts)
            # Normalize interaction count (assuming 10+ interactions per session is good)
            interaction_score = min(avg_interactions / 10, 1.0)
            score_components.append(interaction_score)

            if avg_interactions < 5:
                insights.append("Low interaction levels")
                recommendations.append("Encourage more active participation")

        if session_durations:
            avg_duration = statistics.mean(session_durations)
            # Optimal session duration is around 30-60 minutes
            if 1800 <= avg_duration <= 3600:  # 30-60 minutes
                duration_score = 1.0
            elif avg_duration < 1800:
                duration_score = avg_duration / 1800
            else:
                duration_score = max(0.5, 3600 / avg_duration)

            score_components.append(duration_score)

            if avg_duration < 900:  # Less than 15 minutes
                insights.append("Very short session durations")
                recommendations.append("Encourage longer learning sessions")
            elif avg_duration > 7200:  # More than 2 hours
                insights.append("Very long session durations")
                recommendations.append("Suggest breaks to maintain effectiveness")

        overall_score = statistics.mean(score_components) if score_components else 0.0

        return {
            "score": overall_score,
            "insights": insights,
            "recommendations": recommendations,
            "metrics_analyzed": len(metrics)
        }

    def _analyze_effectiveness(self, metrics: List[LearningMetric]) -> Dict[str, Any]:
        """Analyze learning effectiveness metrics."""
        if not metrics:
            return {"score": 0.0, "insights": [], "recommendations": []}

        comprehension_scores = [m.value for m in metrics if "comprehension_score" in m.metric_type]
        retention_scores = [m.value for m in metrics if "retention_score" in m.metric_type]
        progress_velocities = [m.value for m in metrics if "progress_velocity" in m.metric_type]

        score_components = []
        insights = []
        recommendations = []

        if comprehension_scores:
            avg_comprehension = statistics.mean(comprehension_scores)
            score_components.append(avg_comprehension)

            if avg_comprehension < 0.6:
                insights.append("Low comprehension scores")
                recommendations.append("Review fundamental concepts and adjust difficulty")
            elif avg_comprehension > 0.85:
                insights.append("Excellent comprehension levels")
                recommendations.append("Consider advancing to more challenging topics")

        if retention_scores:
            avg_retention = statistics.mean(retention_scores)
            score_components.append(avg_retention)

            if avg_retention < 0.7:
                insights.append("Knowledge retention could be improved")
                recommendations.append("Implement spaced repetition and review sessions")

        if progress_velocities:
            avg_velocity = statistics.mean(progress_velocities)
            # Normalize velocity (assuming 1.0 is optimal)
            velocity_score = min(avg_velocity, 1.0)
            score_components.append(velocity_score)

            if avg_velocity < 0.3:
                insights.append("Slow learning progress")
                recommendations.append("Adjust learning approach or provide additional support")
            elif avg_velocity > 1.5:
                insights.append("Very fast learning progress")
                recommendations.append("Ensure comprehension depth is maintained")

        overall_score = statistics.mean(score_components) if score_components else 0.0

        return {
            "score": overall_score,
            "insights": insights,
            "recommendations": recommendations,
            "metrics_analyzed": len(metrics)
        }

    def _analyze_adaptive(self, metrics: List[LearningMetric]) -> Dict[str, Any]:
        """Analyze adaptive learning metrics."""
        if not metrics:
            return {"score": 0.0, "insights": [], "recommendations": []}

        difficulty_scores = [m.value for m in metrics if "difficulty_appropriateness" in m.metric_type]
        constellation_scores = [m.value for m in metrics if "constellation_effectiveness" in m.metric_type]
        personalization_scores = [m.value for m in metrics if "personalization_score" in m.metric_type]

        score_components = []
        insights = []
        recommendations = []

        if difficulty_scores:
            avg_difficulty = statistics.mean(difficulty_scores)
            score_components.append(avg_difficulty)

            if avg_difficulty < 0.5:
                insights.append("Difficulty level may not be appropriate")
                recommendations.append("Adjust content difficulty based on user performance")

        if constellation_scores:
            avg_constellation = statistics.mean(constellation_scores)
            score_components.append(avg_constellation)

            if avg_constellation < 0.6:
                insights.append("Current constellation may not be optimal")
                recommendations.append("Consider switching to a different agent constellation")

        if personalization_scores:
            avg_personalization = statistics.mean(personalization_scores)
            score_components.append(avg_personalization)

            if avg_personalization < 0.7:
                insights.append("Learning experience could be more personalized")
                recommendations.append("Enhance personalization based on user preferences")

        overall_score = statistics.mean(score_components) if score_components else 0.0

        return {
            "score": overall_score,
            "insights": insights,
            "recommendations": recommendations,
            "metrics_analyzed": len(metrics)
        }

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate overall recommendations based on analysis."""
        recommendations = []

        # Collect recommendations from all analyses
        for category in ["engagement_analysis", "effectiveness_analysis", "adaptive_analysis"]:
            if category in analysis:
                recommendations.extend(analysis[category].get("recommendations", []))

        # Add overall recommendations based on scores
        overall_score = analysis.get("overall_score", 0.0)

        if overall_score < 0.5:
            recommendations.append("Overall learning effectiveness is low - consider comprehensive review of approach")
        elif overall_score > 0.8:
            recommendations.append("Excellent learning performance - maintain current approach")

        # Remove duplicates while preserving order
        unique_recommendations = []
        for rec in recommendations:
            if rec not in unique_recommendations:
                unique_recommendations.append(rec)

        return unique_recommendations

    def _initialize_default_processors(self):
        """Initialize default metric processors."""
        # This could be expanded with more sophisticated processors
        self.metric_processors = {
            "engagement": self._process_engagement_metric,
            "effectiveness": self._process_effectiveness_metric,
            "adaptive": self._process_adaptive_metric
        }

    def _process_engagement_metric(self, metric: LearningMetric):
        """Process engagement metrics."""
        # Could implement real-time engagement analysis
        pass

    def _process_effectiveness_metric(self, metric: LearningMetric):
        """Process effectiveness metrics."""
        # Could implement real-time effectiveness analysis
        pass

    def _process_adaptive_metric(self, metric: LearningMetric):
        """Process adaptive metrics."""
        # Could implement real-time adaptive analysis
        pass

    def _processing_loop(self):
        """Main processing loop for real-time analytics."""
        while self.is_running:
            try:
                # Process any pending metrics
                with self.processing_lock:
                    # Could implement more sophisticated real-time processing
                    pass

                # Sleep for a short interval
                time.sleep(1)

            except Exception as e:
                print(f"Error in analytics processing loop: {e}")
                time.sleep(5)

    def _load_analytics_data(self):
        """Load analytics data from disk."""
        try:
            # Load historical metrics if needed
            pass
        except Exception as e:
            print(f"Error loading analytics data: {e}")

    def save_analytics_data(self):
        """Save analytics data to disk."""
        try:
            # Save current metrics to disk for persistence
            metrics_file = self.data_path / "metrics.json"

            # Convert metrics to serializable format
            all_metrics = []
            for metric_type, metrics in self.live_metrics.items():
                for metric in metrics:
                    all_metrics.append({
                        "metric_id": metric.metric_id,
                        "user_id": metric.user_id,
                        "session_id": metric.session_id,
                        "framework": metric.framework,
                        "module_id": metric.module_id,
                        "metric_type": metric.metric_type,
                        "value": metric.value,
                        "timestamp": metric.timestamp,
                        "metadata": metric.metadata
                    })

            with open(metrics_file, 'w') as f:
                json.dump(all_metrics, f, indent=2)

        except Exception as e:
            print(f"Error saving analytics data: {e}")

    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get summary of analytics system status."""
        return {
            "is_running": self.is_running,
            "total_metrics": sum(len(metrics) for metrics in self.live_metrics.values()),
            "active_sessions": len(self.session_metrics),
            "tracked_users": len(self.user_metrics),
            "metric_types": list(self.live_metrics.keys()),
            "processing_thread_active": self.processing_thread is not None and self.processing_thread.is_alive()
        }


# Global analytics engine instance
_analytics_engine_instance = None

def get_analytics_engine(data_path: Path = None) -> RealTimeAnalyticsEngine:
    """
    Get the global analytics engine instance.

    Args:
        data_path: Path to analytics data (required for first call)

    Returns:
        RealTimeAnalyticsEngine instance
    """
    global _analytics_engine_instance

    if _analytics_engine_instance is None:
        if data_path is None:
            data_path = Path("data/analytics")
        _analytics_engine_instance = RealTimeAnalyticsEngine(data_path)

    return _analytics_engine_instance
