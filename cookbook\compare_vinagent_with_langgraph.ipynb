{"cells": [{"cell_type": "markdown", "id": "e9e0c8e1", "metadata": {}, "source": ["# Test with <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 3, "id": "edfd65f3", "metadata": {}, "outputs": [], "source": ["from langchain_together import ChatTogether\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "llm = ChatTogether(\n", "    model=\"meta-llama/Llama-3.3-70B-Instruct-Turbo-Free\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "011bf06e", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.checkpoint.memory import InMemorySaver\n", "\n", "checkpointer = InMemorySaver()\n", "\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "agent = create_react_agent(\n", "    model=llm,\n", "    tools=[],\n", "    checkpointer=checkpointer\n", ")\n", "\n", "text_input = \"\"\"<PERSON>, my name is <PERSON><PERSON>. I was born in Thanh Hoa Province, Vietnam, in 1993.\n", "My motto is: \"Make the world better with data and models\". That’s why I work as an AI Solution Architect at FPT Software and as an AI lecturer at NEU.\n", "I began my journey as a gifted student in Mathematics at the High School for Gifted Students, VNU University, where I developed a deep passion for Math and Science.\n", "Later, I earned an Excellent Bachelor's Degree in Applied Mathematical Economics from NEU University in 2015. During my time there, I became the first student from the Math Department to win a bronze medal at the National Math Olympiad.\n", "I have been working as an AI Solution Architect at FPT Software since 2021.\n", "I have been teaching AI and ML courses at NEU university since 2022.\n", "I have conducted extensive research on Reliable AI, Generative AI, and Knowledge Graphs at FPT AIC.\n", "I was one of the first individuals in Vietnam to win a paper award on the topic of Generative AI and LLMs at the Nvidia GTC Global Conference 2025 in San Jose, USA.\n", "I am the founder of DataScienceWorld.Kan, an AI learning hub offering high-standard AI/ML courses such as Build Generative AI Applications and MLOps – Machine Learning in Production, designed for anyone pursuing a career as an AI/ML engineer.\n", "Since 2024, I have participated in Google GDSC and Google I/O as a guest speaker and AI/ML coach for dedicated AI startups.\n", "\"\"\"\n", "\n", "agent.invoke(\n", "    {\"messages\": [{\"role\": \"user\", \"content\": text_input}]},\n", "    config\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "afdd10db", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------\n", "Question: What is organization I founded?\n", "I don't have enough information to determine which organization you founded. Could you provide more context or details about the organization? This would help me better understand and provide a more accurate response.\n", "-----------------------------------------------\n", "Question: What are my works?\n", "I don't have enough information to determine your works. You could be a person from various fields such as art, literature, science, or entertainment, and without more context, it's difficult for me to provide a specific answer. Could you please provide more details or clarify what type of works you are referring to? This would help me better understand and provide a more accurate response.\n", "-----------------------------------------------\n", "Question: What is my Company?\n", "I don't have enough information to determine what your company is. You haven't provided any details about yourself or your company, such as its name, industry, or location. If you could provide more context or information, I'd be happy to try and help you.\n", "-----------------------------------------------\n", "Question: What is my Research Topics?What is my Awards?\n", "I don't have enough information to determine your research topics or awards. You haven't provided any details about yourself, your field of study, or your accomplishments. If you could provide more context or information, I'd be happy to try and help you.\n", "\n", "To better assist you, could you please provide some details such as:\n", "\n", "* Your name or field of study\n", "* Your current or past institutions or organizations\n", "* Any notable publications or presentations you've made\n", "* Any awards or recognition you've received\n", "\n", "This would help me narrow down the search and provide a more accurate response.\n", "-----------------------------------------------\n", "Question: What is my High School?\n", "I don't have enough information to determine which high school you attended. You haven't provided any details about yourself, such as your name, location, or any other identifying information. If you could provide more context or information, I'd be happy to try and help you.\n", "-----------------------------------------------\n", "Question: What is my University?\n", "I don't have enough information to determine which university you attended. You haven't provided any details about yourself, such as your name, location, or any other identifying information. If you could provide more context or information, I'd be happy to try and help you.\n", "-----------------------------------------------\n", "Question: What is my Home Town?\n", "I don't have enough information to determine your hometown. You haven't provided any details about yourself, such as your name, location, or any other identifying information. If you could provide more context or information, I'd be happy to try and help you.\n", "-----------------------------------------------\n", "Question: When I was born?\n", "I don't have enough information to determine your birthdate. You haven't provided any details about yourself, such as your name, location, or any other identifying information. If you could provide more context or information, I'd be happy to try and help you.\n"]}], "source": ["test_questions = [\n", "    \"What is organization I founded?\",\n", "    \"What are my works?\",\n", "    \"What is my Company?\",\n", "    \"What is my Research Topics?\"\n", "    \"What is my Awards?\",\n", "    \"What is my High School?\",\n", "    \"What is my University?\",\n", "    \"What is my Home Town?\",\n", "    \"When I was born?\"\n", "]\n", "\n", "for question in test_questions:\n", "    print(\"-----------------------------------------------\")\n", "    print(f\"Question: {question}\")\n", "    \n", "    response = agent.invoke(  \n", "        {\"messages\": [{\"role\": \"user\", \"content\": question}]},\n", "        config\n", "    )\n", "\n", "    print(response[\"messages\"][-1].content)"]}, {"cell_type": "code", "execution_count": 4, "id": "a53fead5", "metadata": {}, "outputs": [], "source": ["message = \"Hi my name is <PERSON>. I am a singer and my hobbies are singing and dancing\"\n", "\n", "response = agent.invoke(  \n", "    {\"messages\": [{\"role\": \"user\", \"content\": message}]},\n", "    config\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "b03faf90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------\n", "Question: What is my name?\n", "Your name is <PERSON>.\n", "-----------------------------------------------\n", "Question: What are my jobs?\n", "You are a singer.\n"]}], "source": ["test_questions = [\n", "    \"What is my name?\",\n", "    \"What are my jobs?\"\n", "]\n", "\n", "for question in test_questions:\n", "    print(\"-----------------------------------------------\")\n", "    print(f\"Question: {question}\")\n", "    \n", "    response = agent.invoke(  \n", "        {\"messages\": [{\"role\": \"user\", \"content\": question}]},\n", "        config\n", "    )\n", "\n", "    print(response[\"messages\"][-1].content)"]}, {"cell_type": "markdown", "id": "d31815e1", "metadata": {}, "source": ["# Test with vinagent"]}, {"cell_type": "code", "execution_count": null, "id": "8a1f6450", "metadata": {}, "outputs": [], "source": ["from langchain_together import ChatTogether \n", "from vinagent.agent import Agent\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "llm = ChatTogether(\n", "    model=\"meta-llama/Llama-3.3-70B-Instruct-Turbo-Free\"\n", ")\n", "\n", "# Step 1: Create Agent with tools\n", "agent2 = Agent(\n", "    description=\"You are my close friend\",\n", "    llm = llm,\n", "    memory_path = \"templates/memory.json\"\n", ")\n", "\n", "text_input = \"\"\"<PERSON>, my name is <PERSON><PERSON>. I was born in Thanh Hoa Province, Vietnam, in 1993.\n", "My motto is: \"Make the world better with data and models\". That’s why I work as an AI Solution Architect at FPT Software and as an AI lecturer at NEU.\n", "I began my journey as a gifted student in Mathematics at the High School for Gifted Students, VNU University, where I developed a deep passion for Math and Science.\n", "Later, I earned an Excellent Bachelor's Degree in Applied Mathematical Economics from NEU University in 2015. During my time there, I became the first student from the Math Department to win a bronze medal at the National Math Olympiad.\n", "I have been working as an AI Solution Architect at FPT Software since 2021.\n", "I have been teaching AI and ML courses at NEU university since 2022.\n", "I have conducted extensive research on Reliable AI, Generative AI, and Knowledge Graphs at FPT AIC.\n", "I was one of the first individuals in Vietnam to win a paper award on the topic of Generative AI and LLMs at the Nvidia GTC Global Conference 2025 in San Jose, USA.\n", "I am the founder of DataScienceWorld.Kan, an AI learning hub offering high-standard AI/ML courses such as Build Generative AI Applications and MLOps – Machine Learning in Production, designed for anyone pursuing a career as an AI/ML engineer.\n", "Since 2024, I have participated in Google GDSC and Google I/O as a guest speaker and AI/ML coach for dedicated AI startups.\n", "\"\"\"\n", "\n", "message = agent2.invoke(text_input, is_save_memory=True, user_id=\"Kan\")\n", "message.content"]}, {"cell_type": "code", "execution_count": 7, "id": "134f71a2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------\n", "Question: What is organization I founded?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The organization you founded is DataScienceWorld.Kan.\n", "-----------------------------------------------\n", "Question: What are my works?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 3.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["You work at FPT Software since 2021 and NEU since 2022.\n", "-----------------------------------------------\n", "Question: What is my Company?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your company is FPT Software, you have been working there since 2021, and also NEU since 2022.\n", "-----------------------------------------------\n", "Question: What is my Research Topics?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 7.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your research topics include Reliable AI, Generative AI, and Knowledge Graphs.\n", "-----------------------------------------------\n", "Question: What is my Awards?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 8.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON>n, your award is the Nvidia GTC Global Conference award, which you received in 2025.\n", "-----------------------------------------------\n", "Question: What is my High School?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 5.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your high school is High School for Gifted Students, VNU University.\n", "-----------------------------------------------\n", "Question: What is my University?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your university is NEU University, where you graduated in 2015, and you also worked there since 2022. Additionally, you studied at the High School for Gifted Students, VNU University.\n", "-----------------------------------------------\n", "Question: What is my Home Town?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 8.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your home town is Thanh Hoa Province, Vietnam.\n", "-----------------------------------------------\n", "Question: When I was born?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["You were born in 1993.\n"]}], "source": ["test_questions = [\n", "    \"What is organization I founded?\",\n", "    \"What are my works?\",\n", "    \"What is my Company?\",\n", "    \"What is my Research Topics?\",\n", "    \"What is my Awards?\",\n", "    \"What is my High School?\",\n", "    \"What is my University?\",\n", "    \"What is my Home Town?\",\n", "    \"When I was born?\"\n", "]\n", "\n", "for question in test_questions:\n", "    print(\"-----------------------------------------------\")\n", "    print(f\"Question: {question}\")\n", "    \n", "    response = agent2.invoke(\n", "        query=question\n", "    )\n", "\n", "    print(response.content)"]}, {"cell_type": "code", "execution_count": 8, "id": "d523aef2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["AIMessage(content=\"<PERSON> <PERSON>, it's nice to meet you. I'm <PERSON><PERSON>, nice to know that you're a singer and you enjoy singing and dancing. What kind of music do you like to sing?\", additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 40, 'prompt_tokens': 523, 'total_tokens': 563, 'completion_tokens_details': None, 'prompt_tokens_details': None, 'cached_tokens': 0}, 'model_name': 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-0455a970-b322-4bfb-ae16-531ee872b841-0', usage_metadata={'input_tokens': 523, 'output_tokens': 40, 'total_tokens': 563, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Hi my name is <PERSON>. I am a singer and my hobbies are singing and dancing\"\n", "\n", "agent2.invoke(message)"]}, {"cell_type": "code", "execution_count": 9, "id": "ba36ad94", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------\n", "Question: What is my name?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:agentools.agent.agent:I'am chatting with <PERSON><PERSON>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Your name is <PERSON><PERSON>.\n", "-----------------------------------------------\n", "Question: What are my jobs?\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 429 Too Many Requests\"\n", "INFO:openai._base_client:Retrying request to /chat/completions in 9.000000 seconds\n", "INFO:httpx:HTTP Request: POST https://api.together.xyz/v1/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>, your jobs include working at FPT Software since 2021 and NEU since 2022.\n"]}], "source": ["test_questions = [\n", "    \"What is my name?\",\n", "    \"What are my jobs?\"\n", "]\n", "\n", "for question in test_questions:\n", "    print(\"-----------------------------------------------\")\n", "    print(f\"Question: {question}\")\n", "    \n", "    response = agent2.invoke(\n", "        query=question\n", "    )\n", "\n", "    print(response.content)"]}, {"cell_type": "code", "execution_count": null, "id": "45f877fb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 5}