[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "gaapf"
version = "0.1.0"
description = "Guidance AI Agent for Python Framework"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "GAAPF Team"}
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Education",
    "Topic :: Education :: Computer Aided Instruction (CAI)"
]
dependencies = [
    "langchain>=0.1.0",
    "langchain-openai>=0.0.1",
    "langchain-core>=0.1.0",
    "langgraph>=0.0.20",
    "questionary>=2.0.0",
    "rich>=13.0.0",
    "numpy>=1.24.0",
    "pydantic>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "mypy>=1.0.0"
]

[project.scripts]
gaapf = "gaapf.main:main_cli"

[tool.setuptools]
packages = ["gaapf"]

[tool.black]
line-length = 88
target-version = ["py39"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
