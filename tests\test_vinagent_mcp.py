"""
Comprehensive tests for vinagent MCP (Model Context Protocol) functionality.

This module tests:
- DistributedMCPClient initialization and configuration
- MCP server connections and management
- Tool calling through MCP
- Resource management
- Session handling
- Error handling and edge cases
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch, MagicMock

# Import vinagent MCP components
from vinagent.mcp.client import DistributedMCPClient
from vinagent.mcp.connections import MCPConnection
from vinagent.mcp.error_handler import MCPErrorHandler
from vinagent.mcp.sessions import MCPSession
from vinagent.mcp.resources import MCPResource
from vinagent.mcp.tools import MCPTool
from vinagent.mcp.prompts import MCPPrompt


class TestDistributedMCPClient:
    """Test DistributedMCPClient functionality."""
    
    @pytest.fixture
    def sample_server_config(self):
        """Sample MCP server configuration."""
        return {
            "math_server": {
                "command": "python",
                "args": ["math_server.py"],
                "transport": "stdio"
            },
            "file_server": {
                "command": "python",
                "args": ["file_server.py"],
                "transport": "stdio"
            }
        }
    
    def test_mcp_client_initialization(self, sample_server_config):
        """Test DistributedMCPClient initialization."""
        client = DistributedMCPClient(sample_server_config)
        
        assert client is not None
        assert hasattr(client, 'servers')
        assert len(client.servers) == 2
        assert "math_server" in client.servers
        assert "file_server" in client.servers
    
    def test_mcp_client_empty_config(self):
        """Test DistributedMCPClient with empty configuration."""
        client = DistributedMCPClient({})
        
        assert client is not None
        assert len(client.servers) == 0
    
    def test_mcp_client_invalid_config(self):
        """Test DistributedMCPClient with invalid configuration."""
        invalid_config = {
            "invalid_server": {
                "missing_command": True
            }
        }
        
        # Should handle invalid configuration gracefully
        try:
            client = DistributedMCPClient(invalid_config)
            assert client is not None
        except Exception as e:
            assert isinstance(e, (ValueError, KeyError))
    
    @pytest.mark.asyncio
    async def test_mcp_client_server_connection(self, sample_server_config):
        """Test MCP client server connection."""
        client = DistributedMCPClient(sample_server_config)
        
        # Mock the connection process
        with patch.object(client, '_connect_to_server', new_callable=AsyncMock) as mock_connect:
            mock_connect.return_value = True
            
            result = await client.connect_to_server("math_server")
            
            mock_connect.assert_called_once_with("math_server")
            assert result == True
    
    @pytest.mark.asyncio
    async def test_mcp_client_connect_all_servers(self, sample_server_config):
        """Test connecting to all configured servers."""
        client = DistributedMCPClient(sample_server_config)
        
        with patch.object(client, '_connect_to_server', new_callable=AsyncMock) as mock_connect:
            mock_connect.return_value = True
            
            await client.connect_all_servers()
            
            # Should attempt to connect to all servers
            assert mock_connect.call_count == 2
    
    @pytest.mark.asyncio
    async def test_mcp_client_tool_calling(self, sample_server_config):
        """Test calling tools through MCP client."""
        client = DistributedMCPClient(sample_server_config)
        
        # Mock tool calling
        with patch.object(client, 'call_tool', new_callable=AsyncMock) as mock_call_tool:
            mock_call_tool.return_value = {"result": "42"}
            
            result = await client.call_tool("math_server", "add", {"a": 20, "b": 22})
            
            mock_call_tool.assert_called_once_with("math_server", "add", {"a": 20, "b": 22})
            assert result["result"] == "42"
    
    @pytest.mark.asyncio
    async def test_mcp_client_list_tools(self, sample_server_config):
        """Test listing available tools from MCP servers."""
        client = DistributedMCPClient(sample_server_config)
        
        with patch.object(client, 'list_tools', new_callable=AsyncMock) as mock_list_tools:
            mock_tools = [
                {"name": "add", "description": "Add two numbers"},
                {"name": "multiply", "description": "Multiply two numbers"}
            ]
            mock_list_tools.return_value = mock_tools
            
            tools = await client.list_tools("math_server")
            
            mock_list_tools.assert_called_once_with("math_server")
            assert len(tools) == 2
            assert tools[0]["name"] == "add"


class TestMCPConnection:
    """Test MCPConnection functionality."""
    
    def test_mcp_connection_initialization(self):
        """Test MCPConnection initialization."""
        try:
            connection = MCPConnection("test_server", {
                "command": "python",
                "args": ["test_server.py"],
                "transport": "stdio"
            })
            
            assert connection is not None
            assert connection.server_name == "test_server"
        except (ImportError, AttributeError):
            pytest.skip("MCPConnection not available")
    
    @pytest.mark.asyncio
    async def test_mcp_connection_connect(self):
        """Test MCP connection establishment."""
        try:
            connection = MCPConnection("test_server", {
                "command": "python",
                "args": ["test_server.py"],
                "transport": "stdio"
            })
            
            with patch.object(connection, 'connect', new_callable=AsyncMock) as mock_connect:
                mock_connect.return_value = True
                
                result = await connection.connect()
                
                mock_connect.assert_called_once()
                assert result == True
        except (ImportError, AttributeError):
            pytest.skip("MCPConnection not available")
    
    @pytest.mark.asyncio
    async def test_mcp_connection_disconnect(self):
        """Test MCP connection disconnection."""
        try:
            connection = MCPConnection("test_server", {
                "command": "python",
                "args": ["test_server.py"],
                "transport": "stdio"
            })
            
            with patch.object(connection, 'disconnect', new_callable=AsyncMock) as mock_disconnect:
                mock_disconnect.return_value = True
                
                result = await connection.disconnect()
                
                mock_disconnect.assert_called_once()
                assert result == True
        except (ImportError, AttributeError):
            pytest.skip("MCPConnection not available")


class TestMCPErrorHandler:
    """Test MCPErrorHandler functionality."""
    
    def test_mcp_error_handler_initialization(self):
        """Test MCPErrorHandler initialization."""
        try:
            error_handler = MCPErrorHandler()
            assert error_handler is not None
        except (ImportError, AttributeError):
            pytest.skip("MCPErrorHandler not available")
    
    def test_mcp_error_handling(self):
        """Test MCP error handling."""
        try:
            error_handler = MCPErrorHandler()
            
            # Test handling different types of errors
            connection_error = ConnectionError("Failed to connect to server")
            timeout_error = TimeoutError("Server response timeout")
            
            handled_connection = error_handler.handle_error(connection_error)
            handled_timeout = error_handler.handle_error(timeout_error)
            
            assert handled_connection is not None
            assert handled_timeout is not None
        except (ImportError, AttributeError):
            pytest.skip("MCPErrorHandler not available")
    
    def test_mcp_error_retry_logic(self):
        """Test MCP error retry logic."""
        try:
            error_handler = MCPErrorHandler()
            
            # Mock retry logic
            with patch.object(error_handler, 'should_retry') as mock_should_retry:
                mock_should_retry.return_value = True
                
                should_retry = error_handler.should_retry(ConnectionError("Test error"))
                
                assert should_retry == True
        except (ImportError, AttributeError):
            pytest.skip("MCPErrorHandler not available")


class TestMCPSession:
    """Test MCPSession functionality."""
    
    def test_mcp_session_initialization(self):
        """Test MCPSession initialization."""
        try:
            session = MCPSession("test_session", "test_server")
            
            assert session is not None
            assert session.session_id == "test_session"
            assert session.server_name == "test_server"
        except (ImportError, AttributeError):
            pytest.skip("MCPSession not available")
    
    @pytest.mark.asyncio
    async def test_mcp_session_start(self):
        """Test MCP session start."""
        try:
            session = MCPSession("test_session", "test_server")
            
            with patch.object(session, 'start', new_callable=AsyncMock) as mock_start:
                mock_start.return_value = True
                
                result = await session.start()
                
                mock_start.assert_called_once()
                assert result == True
        except (ImportError, AttributeError):
            pytest.skip("MCPSession not available")
    
    @pytest.mark.asyncio
    async def test_mcp_session_end(self):
        """Test MCP session end."""
        try:
            session = MCPSession("test_session", "test_server")
            
            with patch.object(session, 'end', new_callable=AsyncMock) as mock_end:
                mock_end.return_value = True
                
                result = await session.end()
                
                mock_end.assert_called_once()
                assert result == True
        except (ImportError, AttributeError):
            pytest.skip("MCPSession not available")


class TestMCPResource:
    """Test MCPResource functionality."""
    
    def test_mcp_resource_initialization(self):
        """Test MCPResource initialization."""
        try:
            resource = MCPResource("test_resource", "file", "/path/to/resource")
            
            assert resource is not None
            assert resource.name == "test_resource"
            assert resource.type == "file"
            assert resource.uri == "/path/to/resource"
        except (ImportError, AttributeError):
            pytest.skip("MCPResource not available")
    
    @pytest.mark.asyncio
    async def test_mcp_resource_read(self):
        """Test MCP resource reading."""
        try:
            resource = MCPResource("test_resource", "file", "/path/to/resource")
            
            with patch.object(resource, 'read', new_callable=AsyncMock) as mock_read:
                mock_read.return_value = "Resource content"
                
                content = await resource.read()
                
                mock_read.assert_called_once()
                assert content == "Resource content"
        except (ImportError, AttributeError):
            pytest.skip("MCPResource not available")
    
    @pytest.mark.asyncio
    async def test_mcp_resource_write(self):
        """Test MCP resource writing."""
        try:
            resource = MCPResource("test_resource", "file", "/path/to/resource")
            
            with patch.object(resource, 'write', new_callable=AsyncMock) as mock_write:
                mock_write.return_value = True
                
                result = await resource.write("New content")
                
                mock_write.assert_called_once_with("New content")
                assert result == True
        except (ImportError, AttributeError):
            pytest.skip("MCPResource not available")


class TestMCPTool:
    """Test MCPTool functionality."""
    
    def test_mcp_tool_initialization(self):
        """Test MCPTool initialization."""
        try:
            tool = MCPTool("test_tool", "A test tool", {"param1": "string"})
            
            assert tool is not None
            assert tool.name == "test_tool"
            assert tool.description == "A test tool"
            assert tool.parameters == {"param1": "string"}
        except (ImportError, AttributeError):
            pytest.skip("MCPTool not available")
    
    @pytest.mark.asyncio
    async def test_mcp_tool_execution(self):
        """Test MCP tool execution."""
        try:
            tool = MCPTool("test_tool", "A test tool", {"param1": "string"})
            
            with patch.object(tool, 'execute', new_callable=AsyncMock) as mock_execute:
                mock_execute.return_value = {"result": "success"}
                
                result = await tool.execute({"param1": "test_value"})
                
                mock_execute.assert_called_once_with({"param1": "test_value"})
                assert result["result"] == "success"
        except (ImportError, AttributeError):
            pytest.skip("MCPTool not available")


class TestMCPPrompt:
    """Test MCPPrompt functionality."""
    
    def test_mcp_prompt_initialization(self):
        """Test MCPPrompt initialization."""
        try:
            prompt = MCPPrompt("test_prompt", "Test prompt description", "This is a test prompt")
            
            assert prompt is not None
            assert prompt.name == "test_prompt"
            assert prompt.description == "Test prompt description"
            assert prompt.template == "This is a test prompt"
        except (ImportError, AttributeError):
            pytest.skip("MCPPrompt not available")
    
    def test_mcp_prompt_formatting(self):
        """Test MCP prompt formatting."""
        try:
            prompt = MCPPrompt("test_prompt", "Test prompt", "Hello {name}, welcome to {place}")
            
            with patch.object(prompt, 'format') as mock_format:
                mock_format.return_value = "Hello Alice, welcome to Wonderland"
                
                formatted = prompt.format(name="Alice", place="Wonderland")
                
                mock_format.assert_called_once_with(name="Alice", place="Wonderland")
                assert formatted == "Hello Alice, welcome to Wonderland"
        except (ImportError, AttributeError):
            pytest.skip("MCPPrompt not available")


class TestMCPIntegration:
    """Test MCP integration scenarios."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_mcp_workflow(self):
        """Test end-to-end MCP workflow."""
        server_config = {
            "test_server": {
                "command": "python",
                "args": ["test_server.py"],
                "transport": "stdio"
            }
        }
        
        client = DistributedMCPClient(server_config)
        
        # Mock the entire workflow
        with patch.object(client, 'connect_to_server', new_callable=AsyncMock) as mock_connect, \
             patch.object(client, 'list_tools', new_callable=AsyncMock) as mock_list_tools, \
             patch.object(client, 'call_tool', new_callable=AsyncMock) as mock_call_tool:
            
            mock_connect.return_value = True
            mock_list_tools.return_value = [{"name": "test_tool", "description": "Test tool"}]
            mock_call_tool.return_value = {"result": "workflow_success"}
            
            # Connect to server
            connected = await client.connect_to_server("test_server")
            assert connected == True
            
            # List available tools
            tools = await client.list_tools("test_server")
            assert len(tools) == 1
            assert tools[0]["name"] == "test_tool"
            
            # Call a tool
            result = await client.call_tool("test_server", "test_tool", {"param": "value"})
            assert result["result"] == "workflow_success"
    
    @pytest.mark.asyncio
    async def test_mcp_error_scenarios(self):
        """Test MCP error scenarios."""
        server_config = {
            "failing_server": {
                "command": "python",
                "args": ["nonexistent_server.py"],
                "transport": "stdio"
            }
        }
        
        client = DistributedMCPClient(server_config)
        
        # Test connection failure
        with patch.object(client, 'connect_to_server', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = ConnectionError("Failed to connect")
            
            with pytest.raises(ConnectionError):
                await client.connect_to_server("failing_server")
        
        # Test tool call failure
        with patch.object(client, 'call_tool', new_callable=AsyncMock) as mock_call_tool:
            mock_call_tool.side_effect = TimeoutError("Tool call timeout")
            
            with pytest.raises(TimeoutError):
                await client.call_tool("failing_server", "test_tool", {})
