"""
Comprehensive tests for vinagent Tools functionality.

This module tests:
- Tool registration and management
- Tool execution and integration
- Individual tool implementations
- Error handling and edge cases
- Tool discovery and loading
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock, AsyncMock

from langchain_core.tools import BaseTool

# Import vinagent tool components
from vinagent.register.tool import ToolManager
from vinagent.register.initialize import initialize_tools


class MockTool(BaseTool):
    """Mock tool for testing."""
    
    name: str = "mock_tool"
    description: str = "A mock tool for testing"
    
    def _run(self, query: str) -> str:
        return f"Mock tool executed with: {query}"
    
    async def _arun(self, query: str) -> str:
        return f"Mock tool executed async with: {query}"


class TestToolManager:
    """Test ToolManager functionality."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def tools_config(self, temp_data_path):
        """Create a test tools configuration file."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "mock_tool": {
                "name": "mock_tool",
                "description": "A mock tool for testing",
                "module_path": "tests.test_vinagent_tools",
                "class_name": "MockTool",
                "tool_type": "function"
            },
            "another_tool": {
                "name": "another_tool",
                "description": "Another mock tool",
                "module_path": "tests.test_vinagent_tools",
                "class_name": "MockTool",
                "tool_type": "function"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return tools_file
    
    def test_tool_manager_initialization(self, tools_config):
        """Test ToolManager initialization."""
        tool_manager = ToolManager(tools_path=tools_config)
        
        assert tool_manager is not None
        assert tool_manager.tools_path == tools_config
        assert hasattr(tool_manager, 'tools')
    
    def test_tool_manager_with_tools_list(self, tools_config):
        """Test ToolManager initialization with tools list."""
        mock_tool = MockTool()
        tools_list = [mock_tool]
        
        tool_manager = ToolManager(
            tools=tools_list,
            tools_path=tools_config
        )
        
        assert tool_manager is not None
        assert len(tool_manager.tools) >= 1
    
    def test_tool_manager_with_string_tools(self, tools_config):
        """Test ToolManager initialization with string tool references."""
        tools_list = ["tests.test_vinagent_tools.MockTool"]
        
        tool_manager = ToolManager(
            tools=tools_list,
            tools_path=tools_config
        )
        
        assert tool_manager is not None
    
    def test_tool_registration(self, tools_config):
        """Test tool registration functionality."""
        tool_manager = ToolManager(tools_path=tools_config)
        mock_tool = MockTool()
        
        # Register tool
        tool_manager.register_tool(mock_tool)
        
        # Verify tool is registered
        assert "mock_tool" in [tool.name for tool in tool_manager.tools]
    
    def test_tool_extraction_from_text(self, tools_config):
        """Test extracting tool calls from text."""
        tool_manager = ToolManager(tools_path=tools_config)
        
        # Text with tool call
        text_with_tool = '''Here's the response.
        
        {"tool_name": "mock_tool", "tool_type": "function", "arguments": {"query": "test"}, "module_path": "test_module"}
        '''
        
        extracted_tool = tool_manager.extract_tool(text_with_tool)
        
        assert extracted_tool is not None
        assert "mock_tool" in extracted_tool
    
    def test_tool_extraction_without_tool(self, tools_config):
        """Test extracting tool calls from text without tools."""
        tool_manager = ToolManager(tools_path=tools_config)
        
        # Text without tool call
        text_without_tool = "This is just a regular response without any tool calls."
        
        extracted_tool = tool_manager.extract_tool(text_without_tool)
        
        assert extracted_tool is None or extracted_tool == "{}"
    
    @pytest.mark.asyncio
    async def test_tool_execution(self, tools_config):
        """Test tool execution."""
        tool_manager = ToolManager(tools_path=tools_config)
        
        # Execute tool
        result = await tool_manager._execute_tool(
            tool_name="mock_tool",
            tool_type="function",
            arguments={"query": "test execution"},
            module_path="tests.test_vinagent_tools"
        )
        
        assert result is not None
        assert "test execution" in str(result)
    
    def test_tool_manager_with_reset_flag(self, tools_config):
        """Test ToolManager with reset flag."""
        tool_manager = ToolManager(
            tools_path=tools_config,
            is_reset_tools=True
        )
        
        assert tool_manager is not None
    
    def test_tool_manager_with_invalid_config(self, temp_data_path):
        """Test ToolManager with invalid configuration file."""
        invalid_config = temp_data_path / "invalid_tools.json"
        invalid_config.write_text("invalid json content")
        
        # Should handle invalid JSON gracefully
        tool_manager = ToolManager(tools_path=invalid_config)
        assert tool_manager is not None


class TestToolExecution:
    """Test tool execution and integration."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    @pytest.fixture
    def tool_manager(self, temp_data_path):
        """Create a tool manager for testing."""
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "test_tool": {
                "name": "test_tool",
                "description": "Test tool",
                "module_path": "tests.test_vinagent_tools",
                "class_name": "MockTool"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        return ToolManager(tools_path=tools_file)
    
    @pytest.mark.asyncio
    async def test_successful_tool_execution(self, tool_manager):
        """Test successful tool execution."""
        result = await tool_manager._execute_tool(
            tool_name="test_tool",
            tool_type="function",
            arguments={"query": "successful test"},
            module_path="tests.test_vinagent_tools"
        )
        
        assert result is not None
        assert "successful test" in str(result)
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_invalid_arguments(self, tool_manager):
        """Test tool execution with invalid arguments."""
        # Should handle invalid arguments gracefully
        try:
            result = await tool_manager._execute_tool(
                tool_name="test_tool",
                tool_type="function",
                arguments={"invalid_arg": "value"},
                module_path="tests.test_vinagent_tools"
            )
            # If it doesn't raise an error, result should be handled gracefully
            assert result is not None
        except Exception as e:
            # If it raises an error, it should be a meaningful one
            assert isinstance(e, (ValueError, TypeError, KeyError))
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_nonexistent_tool(self, tool_manager):
        """Test tool execution with non-existent tool."""
        with pytest.raises(Exception):
            await tool_manager._execute_tool(
                tool_name="nonexistent_tool",
                tool_type="function",
                arguments={"query": "test"},
                module_path="nonexistent.module"
            )
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_mcp_client(self, tool_manager):
        """Test tool execution with MCP client."""
        mock_mcp_client = Mock()
        mock_mcp_client.call_tool = AsyncMock(return_value="MCP tool result")
        
        result = await tool_manager._execute_tool(
            tool_name="mcp_tool",
            tool_type="mcp",
            arguments={"query": "mcp test"},
            module_path="mcp_module",
            mcp_client=mock_mcp_client,
            mcp_server_name="test_server"
        )
        
        # Should call MCP client
        mock_mcp_client.call_tool.assert_called_once()


class TestSpecificTools:
    """Test specific tool implementations."""
    
    def test_mock_tool_sync_execution(self):
        """Test MockTool synchronous execution."""
        tool = MockTool()
        result = tool._run("sync test")
        
        assert "sync test" in result
        assert "Mock tool executed" in result
    
    @pytest.mark.asyncio
    async def test_mock_tool_async_execution(self):
        """Test MockTool asynchronous execution."""
        tool = MockTool()
        result = await tool._arun("async test")
        
        assert "async test" in result
        assert "async" in result
    
    def test_tool_properties(self):
        """Test tool properties and metadata."""
        tool = MockTool()
        
        assert tool.name == "mock_tool"
        assert tool.description == "A mock tool for testing"
        assert hasattr(tool, '_run')
        assert hasattr(tool, '_arun')


class TestToolDiscovery:
    """Test tool discovery and loading."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_tool_loading_from_config(self, temp_data_path):
        """Test loading tools from configuration."""
        tools_file = temp_data_path / "discovery_tools.json"
        tools_data = {
            "discovered_tool": {
                "name": "discovered_tool",
                "description": "A discovered tool",
                "module_path": "tests.test_vinagent_tools",
                "class_name": "MockTool"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        
        tool_manager = ToolManager(tools_path=tools_file)
        
        # Should discover and load the tool
        assert tool_manager is not None
    
    def test_tool_loading_from_module_string(self, temp_data_path):
        """Test loading tools from module strings."""
        tools_file = temp_data_path / "module_tools.json"
        tools_file.write_text(json.dumps({}))
        
        tool_manager = ToolManager(
            tools=["tests.test_vinagent_tools"],
            tools_path=tools_file
        )
        
        assert tool_manager is not None
    
    @patch('vinagent.register.initialize.initialize_tools')
    def test_tool_initialization(self, mock_initialize):
        """Test tool initialization process."""
        mock_initialize.return_value = []
        
        # Call initialization
        result = initialize_tools()
        
        # Should call the initialization function
        mock_initialize.assert_called_once()
        assert result == []


class TestToolErrorHandling:
    """Test tool error handling and edge cases."""
    
    @pytest.fixture
    def temp_data_path(self):
        """Temporary data path for testing."""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)
    
    def test_tool_manager_with_missing_config_file(self, temp_data_path):
        """Test ToolManager with missing configuration file."""
        missing_file = temp_data_path / "missing_tools.json"
        
        # Should handle missing file gracefully
        tool_manager = ToolManager(tools_path=missing_file)
        assert tool_manager is not None
    
    def test_tool_manager_with_empty_config(self, temp_data_path):
        """Test ToolManager with empty configuration."""
        empty_config = temp_data_path / "empty_tools.json"
        empty_config.write_text(json.dumps({}))
        
        tool_manager = ToolManager(tools_path=empty_config)
        assert tool_manager is not None
    
    def test_tool_extraction_with_malformed_json(self, temp_data_path):
        """Test tool extraction with malformed JSON."""
        tools_file = temp_data_path / "tools.json"
        tools_file.write_text(json.dumps({}))
        
        tool_manager = ToolManager(tools_path=tools_file)
        
        # Text with malformed JSON
        malformed_text = '''Response with malformed tool call:
        {"tool_name": "test", "invalid_json": }
        '''
        
        extracted_tool = tool_manager.extract_tool(malformed_text)
        
        # Should handle malformed JSON gracefully
        assert extracted_tool is None or extracted_tool == "{}"
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_exception(self, temp_data_path):
        """Test tool execution when tool raises exception."""
        
        class FailingTool(BaseTool):
            name: str = "failing_tool"
            description: str = "A tool that always fails"
            
            def _run(self, query: str) -> str:
                raise RuntimeError("Tool execution failed")
            
            async def _arun(self, query: str) -> str:
                raise RuntimeError("Async tool execution failed")
        
        tools_file = temp_data_path / "tools.json"
        tools_data = {
            "failing_tool": {
                "name": "failing_tool",
                "description": "Failing tool",
                "module_path": "tests.test_vinagent_tools",
                "class_name": "FailingTool"
            }
        }
        tools_file.write_text(json.dumps(tools_data, indent=2))
        
        tool_manager = ToolManager(tools_path=tools_file)
        
        # Should handle tool execution errors
        with pytest.raises(Exception):
            await tool_manager._execute_tool(
                tool_name="failing_tool",
                tool_type="function",
                arguments={"query": "test"},
                module_path="tests.test_vinagent_tools"
            )
