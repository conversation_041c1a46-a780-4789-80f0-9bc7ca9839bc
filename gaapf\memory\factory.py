"""
Memory factory for GAAPF.

This module provides a factory for creating memory instances.
"""

from typing import Dict, Optional
from pathlib import Path

from vinagent.memory import BaseMemory, ConversationMemory
from gaapf.memory.learning_memory import LearningMemory


def create_memory(
    memory_type: str,
    user_id: str = None,
    framework: str = None,
    module_id: str = None,
    session_id: str = None,
    storage_path: Path = None
) -> BaseMemory:
    """
    Create a memory instance.
    
    Args:
        memory_type: Type of memory to create
        user_id: User identifier
        framework: Target framework
        module_id: Current learning module
        session_id: Session identifier
        storage_path: Path to store memory data
        
    Returns:
        Memory instance
    """
    if memory_type == "learning":
        return LearningMemory(
            user_id=user_id,
            framework=framework,
            module_id=module_id,
            session_id=session_id,
            storage_path=storage_path
        )
    else:
        return ConversationMemory() 