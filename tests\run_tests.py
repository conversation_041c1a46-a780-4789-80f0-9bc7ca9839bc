#!/usr/bin/env python3
"""
Test runner for GAAPF system.

This script provides comprehensive testing capabilities including:
- Unit tests
- Integration tests
- Performance tests
- Coverage reporting
- Test result analysis
"""

import sys
import subprocess
import argparse
import json
from pathlib import Path
from typing import List, Dict, Any
import time


class GAAPFTestRunner:
    """Test runner for GAAPF system."""
    
    def __init__(self, project_root: Path = None):
        """Initialize test runner."""
        self.project_root = project_root or Path(__file__).parent.parent
        self.test_dir = self.project_root / "tests"
        self.results = {}
    
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run unit tests."""
        print("🧪 Running unit tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-m", "unit",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        self.results["unit_tests"] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Unit tests passed in {duration:.2f}s")
        else:
            print(f"❌ Unit tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results["unit_tests"]
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run integration tests."""
        print("🔗 Running integration tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-m", "integration",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        self.results["integration_tests"] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Integration tests passed in {duration:.2f}s")
        else:
            print(f"❌ Integration tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results["integration_tests"]
    
    def run_all_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run all tests."""
        print("🚀 Running all tests...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        self.results["all_tests"] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ All tests passed in {duration:.2f}s")
        else:
            print(f"❌ Some tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results["all_tests"]
    
    def run_with_coverage(self, verbose: bool = False) -> Dict[str, Any]:
        """Run tests with coverage reporting."""
        print("📊 Running tests with coverage...")
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "--cov=gaapf",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        self.results["coverage_tests"] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Coverage tests completed in {duration:.2f}s")
            print("📈 Coverage report generated in htmlcov/index.html")
        else:
            print(f"❌ Coverage tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results["coverage_tests"]
    
    def run_specific_test(self, test_path: str, verbose: bool = False) -> Dict[str, Any]:
        """Run a specific test file or test function."""
        print(f"🎯 Running specific test: {test_path}")
        
        cmd = [
            sys.executable, "-m", "pytest",
            test_path,
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        test_key = f"specific_test_{test_path.replace('/', '_').replace('.py', '')}"
        self.results[test_key] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Specific test passed in {duration:.2f}s")
        else:
            print(f"❌ Specific test failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results[test_key]
    
    def check_test_dependencies(self) -> bool:
        """Check if test dependencies are installed."""
        print("🔍 Checking test dependencies...")
        
        required_packages = [
            "pytest",
            "pytest-asyncio",
            "pytest-cov",
            "pytest-mock"
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing test dependencies: {', '.join(missing_packages)}")
            print("Install with: pip install " + " ".join(missing_packages))
            return False
        
        print("✅ All test dependencies are installed")
        return True
    
    def generate_test_report(self, output_file: str = None) -> str:
        """Generate a test report."""
        if not self.results:
            return "No test results available"
        
        report = "# GAAPF Test Report\n\n"
        report += f"Generated at: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        total_duration = sum(result.get("duration", 0) for result in self.results.values())
        passed_tests = sum(1 for result in self.results.values() if result.get("return_code") == 0)
        total_tests = len(self.results)
        
        report += f"## Summary\n"
        report += f"- Total test suites: {total_tests}\n"
        report += f"- Passed: {passed_tests}\n"
        report += f"- Failed: {total_tests - passed_tests}\n"
        report += f"- Total duration: {total_duration:.2f}s\n\n"
        
        report += "## Test Results\n\n"
        
        for test_name, result in self.results.items():
            status = "✅ PASSED" if result["return_code"] == 0 else "❌ FAILED"
            duration = result.get("duration", 0)
            
            report += f"### {test_name}\n"
            report += f"- Status: {status}\n"
            report += f"- Duration: {duration:.2f}s\n"
            
            if result["return_code"] != 0 and result.get("stderr"):
                report += f"- Error output:\n```\n{result['stderr']}\n```\n"
            
            report += "\n"
        
        if output_file:
            output_path = Path(output_file)
            output_path.write_text(report)
            print(f"📄 Test report saved to {output_path}")
        
        return report
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """Run performance tests."""
        print("⚡ Running performance tests...")
        
        # This would run specific performance tests
        # For now, we'll simulate with a placeholder
        
        cmd = [
            sys.executable, "-m", "pytest",
            str(self.test_dir),
            "-m", "slow",
            "--tb=short"
        ]
        
        if verbose:
            cmd.append("-v")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True)
        duration = time.time() - start_time
        
        self.results["performance_tests"] = {
            "return_code": result.returncode,
            "duration": duration,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
        
        if result.returncode == 0:
            print(f"✅ Performance tests passed in {duration:.2f}s")
        else:
            print(f"❌ Performance tests failed in {duration:.2f}s")
            if verbose:
                print(result.stdout)
                print(result.stderr)
        
        return self.results["performance_tests"]


def main():
    """Main entry point for test runner."""
    parser = argparse.ArgumentParser(description="GAAPF Test Runner")
    parser.add_argument("--unit", action="store_true", help="Run unit tests only")
    parser.add_argument("--integration", action="store_true", help="Run integration tests only")
    parser.add_argument("--coverage", action="store_true", help="Run tests with coverage")
    parser.add_argument("--performance", action="store_true", help="Run performance tests")
    parser.add_argument("--specific", type=str, help="Run specific test file or function")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--report", type=str, help="Generate test report to file")
    parser.add_argument("--check-deps", action="store_true", help="Check test dependencies")
    
    args = parser.parse_args()
    
    runner = GAAPFTestRunner()
    
    if args.check_deps:
        if not runner.check_test_dependencies():
            sys.exit(1)
        return
    
    # Check dependencies before running tests
    if not runner.check_test_dependencies():
        sys.exit(1)
    
    success = True
    
    if args.unit:
        result = runner.run_unit_tests(args.verbose)
        success = success and result["return_code"] == 0
    elif args.integration:
        result = runner.run_integration_tests(args.verbose)
        success = success and result["return_code"] == 0
    elif args.coverage:
        result = runner.run_with_coverage(args.verbose)
        success = success and result["return_code"] == 0
    elif args.performance:
        result = runner.run_performance_tests(args.verbose)
        success = success and result["return_code"] == 0
    elif args.specific:
        result = runner.run_specific_test(args.specific, args.verbose)
        success = success and result["return_code"] == 0
    else:
        # Run all tests by default
        result = runner.run_all_tests(args.verbose)
        success = success and result["return_code"] == 0
    
    # Generate report if requested
    if args.report:
        runner.generate_test_report(args.report)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
