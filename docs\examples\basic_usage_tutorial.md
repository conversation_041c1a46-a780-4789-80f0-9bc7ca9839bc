# GAAPF Basic Usage Tutorial

This tutorial will guide you through the basic usage of GAAPF, from initial setup to conducting your first learning session.

## 🚀 Getting Started

### Prerequisites

Before starting, ensure you have:
- Python 3.8 or higher
- An API key from Together AI or OpenAI
- At least 4GB of available RAM
- Basic familiarity with command-line interfaces

### Step 1: Installation

```bash
# Clone the repository
git clone <repository-url>
cd gaapf

# Create a virtual environment (recommended)
python -m venv gaapf-env
source gaapf-env/bin/activate  # On Windows: gaapf-env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Environment Setup

Create a `.env` file in the project root:

```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file with your configuration:

```bash
# LLM Provider Configuration
TOGETHER_API_KEY=your_together_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
DEFAULT_LLM_PROVIDER=together

# System Configuration
GAAPF_DATA_PATH=./data
GAAPF_LOG_LEVEL=INFO

# Analytics Configuration
ANALYTICS_ENABLED=true

# Memory Configuration
MEMORY_TTL_HOURS=24
CACHE_SIZE_LIMIT=1000
```

### Step 3: First Launch

```bash
# Launch GAAPF CLI
python -m gaapf.interfaces.cli.app
```

## 👤 User Registration and Profile Setup

### Creating Your User Profile

When you first launch GAAPF, you'll be prompted to create a user profile:

```
Welcome to GAAPF - Guidance AI Agent for Python Framework!

Please create your user profile:

1. User ID: Enter a unique identifier (e.g., "john_doe_2024")
2. Programming Experience: Select your experience level
   - 🟢 Beginner (0-1 years)
   - 🟡 Intermediate (2-5 years)
   - 🟠 Advanced (5+ years)
3. Python Skill Level:
   - 🟢 Beginner
   - 🟡 Intermediate
   - 🟠 Advanced
4. Learning Pace:
   - 🐌 Slow and thorough
   - 🚶 Moderate pace
   - 🏃 Fast-paced
5. Preferred Learning Style:
   - 👁️ Visual (diagrams, examples)
   - 👂 Auditory (explanations, discussions)
   - ✋ Kinesthetic (hands-on practice)
   - 📖 Reading/Writing (documentation, notes)
   - 🔄 Mixed approach
```

**Example Profile Setup:**
```
User ID: alice_learner
Programming Experience: Intermediate (3 years)
Python Skill Level: Intermediate
Learning Pace: Moderate pace
Preferred Learning Style: Hands-on practice
```

## 🎯 Selecting Your First Framework

After profile creation, choose a framework to start learning:

```
Available Frameworks:

🟢 Beginner-Friendly:
1. LangChain - Building applications with language models
2. OpenAI API - Direct OpenAI API integration

🟡 Intermediate:
3. LangGraph - Stateful, multi-agent applications
4. CrewAI - Collaborative AI agent frameworks
5. LlamaIndex - Data framework for LLM applications

🟠 Advanced:
6. AutoGen - Multi-agent conversation frameworks
7. Haystack - End-to-end NLP framework
8. Semantic Kernel - Microsoft's AI orchestration

Which framework would you like to learn? (1-8):
```

**Recommendation for beginners:** Start with LangChain (option 1) as it provides the best foundation for understanding AI application development.

## 📚 Choosing a Learning Module

Once you've selected a framework, choose an appropriate module:

```
LangChain Learning Modules:

🟢 Beginner Level:
1. LangChain Basics - Introduction to core concepts (30 min)
2. Simple Chains - Creating your first chains (45 min)
3. Prompts and Templates - Effective prompt engineering (30 min)

🟡 Intermediate Level:
4. Memory Systems - Adding memory to applications (60 min)
5. Agents and Tools - Building autonomous agents (90 min)
6. Vector Stores - Working with embeddings (75 min)

🟠 Advanced Level:
7. Custom Components - Building custom LangChain components (120 min)
8. Production Deployment - Deploying LangChain applications (90 min)

Which module would you like to start with? (1-8):
```

**For your first session:** Choose "LangChain Basics" to build a solid foundation.

## 🤖 Your First Learning Session

### Starting the Session

```
Starting learning session for: LangChain Basics
Estimated duration: 30 minutes
Difficulty: Beginner

Your learning constellation has been assembled:
- 👨‍🏫 Instructor Agent (Primary)
- 💻 Code Assistant Agent
- 📚 Documentation Expert Agent
- 🎯 Practice Facilitator Agent

Type 'help' for available commands or start by asking a question!

You: 
```

### Example Learning Conversation

Here's what a typical learning conversation might look like:

```
You: Hi! I'm new to LangChain. Can you help me understand what it is?

🤖 Instructor Agent: Hello! I'd be happy to introduce you to LangChain. 

LangChain is a powerful framework designed to help developers build applications with Large Language Models (LLMs). Think of it as a toolkit that makes it easier to create AI-powered applications.

Here are the core concepts:

🔗 **Chains**: Sequences of calls that process information step by step
🧠 **LLMs**: The language models that power your application
📝 **Prompts**: Templates for communicating with the LLM
💾 **Memory**: Systems to remember conversation history
🛠️ **Tools**: External capabilities your AI can use

Would you like me to explain any of these concepts in more detail, or would you prefer to see a practical example?

You: Can you show me a simple example of how to create a basic chain?

🤖 Code Assistant Agent: Absolutely! Let me show you how to create a simple LangChain chain. Here's a basic example:

```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Step 1: Initialize the LLM
llm = OpenAI(temperature=0.7)

# Step 2: Create a prompt template
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Write a brief explanation about {topic} in simple terms."
)

# Step 3: Create the chain
chain = LLMChain(llm=llm, prompt=prompt)

# Step 4: Run the chain
result = chain.run(topic="artificial intelligence")
print(result)
```

This example creates a simple chain that:
1. Takes a topic as input
2. Uses a prompt template to format the request
3. Sends it to the LLM
4. Returns the response

Would you like to try modifying this example or learn about more complex chains?

You: This is helpful! But I'm getting confused about the difference between chains and agents. Can you clarify?

🤖 Instructor Agent: Great question! This is a common point of confusion. Let me clarify the difference:

**Chains** 🔗:
- Follow a predetermined sequence of steps
- Predictable flow: Input → Processing → Output
- Good for structured tasks with known steps
- Example: Summarize text → Translate → Format output

**Agents** 🤖:
- Can make decisions about what to do next
- Have access to tools and can choose which ones to use
- More autonomous and flexible
- Example: "Research this topic" → Agent decides to search web, read documents, synthesize information

Think of chains as following a recipe step-by-step, while agents are like having an assistant who can figure out what needs to be done and use available tools to accomplish it.

Would you like to see examples of both, or dive deeper into one of them?

You: I'd like to try creating something hands-on. Can you give me a practice exercise?

🤖 Practice Facilitator Agent: Perfect! Let's get you hands-on experience. Here's a beginner-friendly exercise:

**Exercise: Build a Personal Assistant Chain**

**Goal**: Create a chain that acts as a personal writing assistant

**Your Task**:
1. Create a prompt template that takes a writing task and tone
2. Build a chain that helps with different writing tasks
3. Test it with different inputs

**Starter Code**:
```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import LLMChain

# Your code here - fill in the missing parts
llm = OpenAI(temperature=0.7)

prompt = PromptTemplate(
    input_variables=["task", "tone"],
    template="Help me with this writing task: {task}. Please write in a {tone} tone."
)

# Complete the chain and test it
```

**Test Cases**:
- Task: "Write an email to my boss", Tone: "professional"
- Task: "Explain quantum physics", Tone: "simple and friendly"

Try implementing this and let me know if you need help or want to see the solution!

You: I tried the exercise but I'm getting an error. Here's my code: [code with error]

🤖 Troubleshooter Agent: I can help you fix that error! Looking at your code, I see the issue...

[Troubleshooter provides specific debugging help]

You: Thanks! That worked. What should I learn next?

🤖 Progress Tracker Agent: Excellent progress! You've successfully:
✅ Understood LangChain basics
✅ Created your first chain
✅ Completed a hands-on exercise
✅ Debugged and fixed an issue

**Your Learning Progress**: 75% complete with LangChain Basics

**Recommended Next Steps**:
1. **Continue Current Module**: Learn about prompt engineering techniques (15 min remaining)
2. **Next Module**: Move to "Simple Chains" to build more complex workflows
3. **Practice More**: Try the additional exercises in the practice section

**Personalized Recommendation**: Based on your hands-on learning style, I suggest completing the current module with the prompt engineering section, then moving to "Simple Chains" where you'll build more complex examples.

Would you like to continue with prompt engineering or move to the next module?
```

## 📊 Viewing Your Progress

### Accessing the Analytics Dashboard

```
Main Menu Options:
1. Continue learning session
2. View learning progress
3. Get learning recommendations
4. View analytics dashboard
5. Manage learning goals

Select option: 4
```

### Sample Analytics Dashboard

```
📊 Learning Analytics Dashboard for alice_learner

┌─────────────────────────────────────────────────────────────┐
│                    Learning Progress                         │
├─────────────────────────────────────────────────────────────┤
│ Total Sessions: 3                                           │
│ Total Time: 2.5 hours                                       │
│ Frameworks Studied: 1 (LangChain)                          │
│ Concepts Mastered: 8                                        │
│ Average Score: 78.5%                                        │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                    Recent Activity                           │
├─────────────────────────────────────────────────────────────┤
│ 📈 LangChain Basics - Today - Score: 85%                   │
│ 💻 Hands-on Exercise - Today - Completed                   │
│ 🔧 Debugging Practice - Today - Score: 90%                 │
└─────────────────────────────────────────────────────────────┘

💡 Quick Tips: Keep up the great work! | Try studying during your peak hours | Review previous concepts regularly
```

## 🎯 Setting Learning Goals

### Creating Your First Goal

```
🎯 Learning Goals Management

Current Goals: None

What would you like to do?
1. Add new goal
2. Back to main menu

Select option: 1

Goal Setup:
- Goal Description: Master LangChain fundamentals
- Target Framework: LangChain
- Target Date: 2024-02-15
- Priority: High

Goal created successfully! 🎉

Your goal "Master LangChain fundamentals" has been added with target date 2024-02-15.
```

## 🔄 Session Continuation

### Resuming Previous Sessions

```
Continue a Previous Session:

Available Sessions:
1. LangChain Basics - 75% complete (Last: 2 hours ago)
2. Simple Chains - 30% complete (Last: 1 day ago)

Which session would you like to continue? (1-2): 1

Resuming session: LangChain Basics
Progress: 75% complete
Last topic: Prompt Engineering
Next: Advanced prompt techniques

Your constellation is ready! How can I help you continue learning?
```

## 💡 Getting Recommendations

### Personalized Learning Recommendations

```
🎯 Learning Recommendations for alice_learner

Based on your progress and learning style, here are personalized recommendations:

#1 Advanced LangChain Chains
Framework: LangChain
Difficulty: Intermediate
Estimated Time: 45 minutes
Reason: Natural progression from your current knowledge
Description: Learn to build complex multi-step chains for advanced applications.

#2 LangGraph State Management
Framework: LangGraph
Difficulty: Advanced
Estimated Time: 60 minutes
Reason: Builds on your LangChain foundation
Description: Master stateful workflows and complex agent coordination.

#3 Hands-on Project: Chatbot
Framework: LangChain
Difficulty: Intermediate
Estimated Time: 90 minutes
Reason: Perfect for your hands-on learning style
Description: Build a complete chatbot application using LangChain.

Would you like to start one of these recommendations? (1-3):
```

## 🛠️ Troubleshooting Common Issues

### Issue 1: API Key Problems

```
Error: No valid LLM provider found

Solution:
1. Check your .env file has the correct API key
2. Verify the API key is valid and has credits
3. Ensure DEFAULT_LLM_PROVIDER matches your available key
```

### Issue 2: Memory Issues

```
Error: Insufficient memory for analytics processing

Solution:
1. Reduce CACHE_SIZE_LIMIT in .env file
2. Close other applications to free RAM
3. Restart GAAPF to clear memory
```

### Issue 3: Module Not Found

```
Error: Module 'lc_advanced' not found

Solution:
1. Check if you've completed prerequisite modules
2. Verify framework configuration is up to date
3. Try refreshing the module database
```

## 📈 Next Steps

After completing this tutorial, you should:

1. **Explore More Frameworks**: Try LangGraph or CrewAI
2. **Build Projects**: Use the Project Guide agent for complete applications
3. **Join Advanced Modules**: Move to intermediate and advanced content
4. **Customize Your Experience**: Adjust settings and preferences
5. **Track Your Progress**: Regularly check analytics and set new goals

## 🎓 Graduation Criteria

You've mastered the basics when you can:
- ✅ Navigate the GAAPF interface confidently
- ✅ Complete learning modules independently
- ✅ Use different agents effectively
- ✅ Debug common issues
- ✅ Set and track learning goals
- ✅ Build simple applications in your chosen framework

**Congratulations!** You're ready to dive deeper into AI framework mastery with GAAPF! 🎉

## 📞 Getting Help

If you need assistance:
- Use the **Motivational Coach** agent when feeling stuck
- Ask the **Mentor** agent for learning strategy advice
- Consult the **Documentation Expert** for technical references
- Try the **Troubleshooter** agent for error resolution

Remember: GAAPF is designed to adapt to your learning style and pace. Don't hesitate to ask questions and explore different approaches!
