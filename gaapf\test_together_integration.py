#!/usr/bin/env python3
"""
Test script for Together AI integration.

This script tests the Together AI provider integration with the GAAPF system.
"""

import sys
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_together_config_properties():
    """Test Together AI configuration properties."""
    print("🧪 Testing Together AI Configuration Properties")
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Test with mock environment variables
        with patch.dict(os.environ, {
            'TOGETHER_API_KEY': 'test_together_key',
            'TOGETHER_MODEL': 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
        }):
            config = EnvironmentConfig()
            
            # Test API key property
            assert config.together_api_key == 'test_together_key'
            print("✅ Together API key property works")
            
            # Test model property
            assert config.together_model == 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
            print("✅ Together model property works")
            
            # Test default model when not set
            with patch.dict(os.environ, {}, clear=True):
                config_default = EnvironmentConfig()
                assert config_default.together_model == 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'
                print("✅ Together model default value works")
        
        return True
        
    except Exception as e:
        print(f"❌ Together AI configuration properties test failed: {e}")
        return False

def test_together_provider_availability():
    """Test Together AI provider availability detection."""
    print("\n🧪 Testing Together AI Provider Availability")
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Test with Together API key present
        with patch.dict(os.environ, {'TOGETHER_API_KEY': 'test_key'}):
            config = EnvironmentConfig()
            providers = config.get_available_llm_providers()
            
            assert 'together' in providers
            assert providers['together'] is True
            print("✅ Together AI detected as available when API key is present")
        
        # Test with Together API key absent
        with patch.dict(os.environ, {}, clear=True):
            config = EnvironmentConfig()
            providers = config.get_available_llm_providers()
            
            assert 'together' in providers
            assert providers['together'] is False
            print("✅ Together AI detected as unavailable when API key is missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Together AI provider availability test failed: {e}")
        return False

def test_together_llm_creation():
    """Test Together AI LLM creation."""
    print("\n🧪 Testing Together AI LLM Creation")
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Mock the ChatTogether class
        with patch('langchain_together.ChatTogether') as mock_chat_together:
            mock_llm = MagicMock()
            mock_chat_together.return_value = mock_llm
            
            # Test LLM creation with Together provider
            with patch.dict(os.environ, {
                'TOGETHER_API_KEY': 'test_together_key',
                'TOGETHER_MODEL': 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
                'LLM_TEMPERATURE': '0.7',
                'MAX_TOKENS': '2000',
                'LLM_TIMEOUT': '30'
            }):
                config = EnvironmentConfig()
                llm = config.create_llm(provider='together')
                
                # Verify ChatTogether was called with correct parameters
                mock_chat_together.assert_called_once_with(
                    model='meta-llama/Llama-3.3-70B-Instruct-Turbo-Free',
                    temperature=0.7,
                    max_tokens=2000,
                    timeout=30,
                    together_api_key='test_together_key'
                )
                
                assert llm == mock_llm
                print("✅ Together AI LLM creation works with correct parameters")
        
        return True
        
    except Exception as e:
        print(f"❌ Together AI LLM creation test failed: {e}")
        return False

def test_together_error_handling():
    """Test Together AI error handling."""
    print("\n🧪 Testing Together AI Error Handling")
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Test error when API key is missing
        with patch.dict(os.environ, {}, clear=True):
            config = EnvironmentConfig()
            
            try:
                config.create_llm(provider='together')
                print("❌ Should have raised ValueError for missing API key")
                return False
            except ValueError as e:
                assert "API key not configured for provider: together" in str(e)
                print("✅ Correct error raised for missing Together API key")
        
        return True
        
    except Exception as e:
        print(f"❌ Together AI error handling test failed: {e}")
        return False

def test_together_validation_integration():
    """Test Together AI integration with validation system."""
    print("\n🧪 Testing Together AI Validation Integration")
    
    try:
        from gaapf.config.env_config import EnvironmentConfig
        
        # Test validation with Together AI as only provider
        with patch.dict(os.environ, {'TOGETHER_API_KEY': 'test_key'}, clear=True):
            config = EnvironmentConfig()
            validation = config.validate_configuration()
            
            assert validation['valid'] is True
            assert validation['available_providers']['together'] is True
            print("✅ Validation works with Together AI as only provider")
        
        # Test validation with Together AI as default provider
        with patch.dict(os.environ, {
            'TOGETHER_API_KEY': 'test_key',
            'DEFAULT_LLM_PROVIDER': 'together'
        }, clear=True):
            config = EnvironmentConfig()
            validation = config.validate_configuration()
            
            assert validation['valid'] is True
            assert validation['default_provider_available'] is True
            print("✅ Validation works with Together AI as default provider")
        
        return True
        
    except Exception as e:
        print(f"❌ Together AI validation integration test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Together AI Integration Test")
    print("=" * 50)
    
    # Run tests
    tests = [
        ("Together AI Configuration Properties", test_together_config_properties),
        ("Together AI Provider Availability", test_together_provider_availability),
        ("Together AI LLM Creation", test_together_llm_creation),
        ("Together AI Error Handling", test_together_error_handling),
        ("Together AI Validation Integration", test_together_validation_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All Together AI integration tests passed!")
        print("\n📝 Together AI is successfully integrated into GAAPF!")
        print("💡 To use Together AI:")
        print("1. Get your API key from: https://api.together.xyz/settings/api-keys")
        print("2. Add TOGETHER_API_KEY=your_key to your .env file")
        print("3. Set DEFAULT_LLM_PROVIDER=together in .env (optional)")
        return 0
    else:
        print("\n❌ Some Together AI integration tests failed.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
