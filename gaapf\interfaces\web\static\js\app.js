// GAAPF Web Interface JavaScript

// API URL - change this to your actual API URL in production
const API_URL = 'http://localhost:8000/api';

// DOM Elements
const setupPanel = document.getElementById('setup-panel');
const chatPanel = document.getElementById('chat-panel');
const sessionForm = document.getElementById('session-form');
const messageForm = document.getElementById('message-form');
const messagesContainer = document.getElementById('messages');
const agentsList = document.getElementById('agents-list');
const frameworkSelect = document.getElementById('framework');
const learningGoalsContainer = document.getElementById('learning-goals');
const endSessionButton = document.getElementById('end-session');

// State
let currentSession = null;
let websocket = null;
let selectedAgent = null;

// Initialize the application
async function init() {
    try {
        // Fetch available frameworks
        const response = await fetch(`${API_URL}/frameworks`);
        const data = await response.json();
        
        // Populate frameworks dropdown
        data.frameworks.forEach(framework => {
            const option = document.createElement('option');
            option.value = framework;
            option.textContent = framework;
            frameworkSelect.appendChild(option);
        });
        
        // Add event listeners
        sessionForm.addEventListener('submit', handleSessionFormSubmit);
        messageForm.addEventListener('submit', handleMessageSubmit);
        frameworkSelect.addEventListener('change', handleFrameworkChange);
        endSessionButton.addEventListener('click', handleEndSession);
        
    } catch (error) {
        console.error('Failed to initialize application:', error);
        showError('Failed to connect to the server. Please try again later.');
    }
}

// Handle framework selection change
async function handleFrameworkChange() {
    const framework = frameworkSelect.value;
    if (!framework) return;
    
    // Clear existing learning goals
    learningGoalsContainer.innerHTML = '';
    
    // These are example learning goals - in a real app, you'd fetch these from the server
    const learningGoals = {
        'Django': ['Web Development', 'ORM', 'Templates', 'Authentication', 'REST API'],
        'Flask': ['Routing', 'Templates', 'Extensions', 'RESTful API', 'Database Integration'],
        'FastAPI': ['API Development', 'Data Validation', 'Async', 'Documentation', 'Security'],
        'Pyramid': ['Configuration', 'Views', 'Templates', 'Security', 'Extending'],
        'SQLAlchemy': ['ORM Basics', 'Relationships', 'Queries', 'Migrations', 'Performance'],
        'Pytest': ['Test Writing', 'Fixtures', 'Mocking', 'Parametrization', 'Plugins']
    };
    
    // Add learning goals checkboxes
    const goals = learningGoals[framework] || [];
    goals.forEach(goal => {
        const label = document.createElement('label');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.value = goal;
        checkbox.name = 'learning-goal';
        
        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(goal));
        
        learningGoalsContainer.appendChild(label);
    });
}

// Handle session form submission
async function handleSessionFormSubmit(event) {
    event.preventDefault();
    
    const userId = document.getElementById('user-id').value;
    const framework = frameworkSelect.value;
    const skillLevel = document.getElementById('skill-level').value;
    
    // Get selected learning goals
    const learningGoals = [];
    document.querySelectorAll('input[name="learning-goal"]:checked').forEach(checkbox => {
        learningGoals.push(checkbox.value);
    });
    
    if (learningGoals.length === 0) {
        showError('Please select at least one learning goal.');
        return;
    }
    
    try {
        // Create session
        const response = await fetch(`${API_URL}/sessions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                user_id: userId,
                framework: framework,
                skill_level: skillLevel,
                learning_goals: learningGoals
            })
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || 'Failed to create session');
        }
        
        const data = await response.json();
        currentSession = data.session_id;
        
        // Populate agents list
        agentsList.innerHTML = '';
        data.agents.forEach(agent => {
            const li = document.createElement('li');
            li.textContent = formatAgentName(agent);
            li.dataset.agent = agent;
            li.addEventListener('click', () => selectAgent(agent, li));
            agentsList.appendChild(li);
        });
        
        // Connect WebSocket
        connectWebSocket(currentSession);
        
        // Show welcome message
        addMessage(`Welcome to your ${framework} learning session! How can I help you today?`, 'system');
        
        // Switch to chat panel
        setupPanel.classList.add('hidden');
        chatPanel.classList.remove('hidden');
        
    } catch (error) {
        console.error('Failed to create session:', error);
        showError(error.message || 'Failed to create session. Please try again.');
    }
}

// Format agent type for display
function formatAgentName(agentType) {
    return agentType
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase());
}

// Select an agent to direct messages to
function selectAgent(agent, element) {
    // Remove active class from all agents
    document.querySelectorAll('#agents-list li').forEach(li => {
        li.classList.remove('active');
    });
    
    // Add active class to selected agent
    element.classList.add('active');
    selectedAgent = agent;
    
    addMessage(`You are now talking to the ${formatAgentName(agent)}. How can I help you?`, agent);
}

// Handle message form submission
async function handleMessageSubmit(event) {
    event.preventDefault();
    
    const messageInput = document.getElementById('message-input');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Add message to chat
    addMessage(message, 'user');
    
    // Clear input
    messageInput.value = '';
    
    try {
        // Send message via WebSocket if connected
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send(JSON.stringify({
                content: message,
                agent_type: selectedAgent
            }));
        } else {
            // Fallback to HTTP if WebSocket is not connected
            const response = await fetch(`${API_URL}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: currentSession,
                    content: message,
                    agent_type: selectedAgent
                })
            });
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.detail || 'Failed to send message');
            }
            
            const data = await response.json();
            addMessage(data.response, selectedAgent || 'system');
        }
    } catch (error) {
        console.error('Failed to send message:', error);
        showError('Failed to send message. Please try again.');
    }
}

// Connect to WebSocket
function connectWebSocket(sessionId) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    
    websocket = new WebSocket(`${protocol}//${host}/api/ws/${sessionId}`);
    
    websocket.onopen = () => {
        console.log('WebSocket connection established');
    };
    
    websocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'message') {
            addMessage(data.content, selectedAgent || 'system');
        } else if (data.error) {
            showError(data.error);
        }
    };
    
    websocket.onerror = (error) => {
        console.error('WebSocket error:', error);
    };
    
    websocket.onclose = () => {
        console.log('WebSocket connection closed');
        // Try to reconnect after a delay
        setTimeout(() => {
            if (currentSession) {
                connectWebSocket(currentSession);
            }
        }, 3000);
    };
}

// Handle ending the session
async function handleEndSession() {
    if (!currentSession) return;
    
    try {
        // Close WebSocket
        if (websocket) {
            websocket.close();
            websocket = null;
        }
        
        // End session on server
        await fetch(`${API_URL}/sessions/${currentSession}`, {
            method: 'DELETE'
        });
        
        // Reset state
        currentSession = null;
        selectedAgent = null;
        
        // Switch back to setup panel
        chatPanel.classList.add('hidden');
        setupPanel.classList.remove('hidden');
        
        // Clear messages
        messagesContainer.innerHTML = '';
        
    } catch (error) {
        console.error('Failed to end session:', error);
        showError('Failed to end session. Please try again.');
    }
}

// Add a message to the chat
function addMessage(content, sender) {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${sender === 'user' ? 'user' : 'agent'}`;
    
    const senderElement = document.createElement('div');
    senderElement.className = 'sender';
    senderElement.textContent = sender === 'user' ? 'You' : formatAgentName(sender);
    
    const contentElement = document.createElement('div');
    contentElement.className = 'content';
    contentElement.textContent = content;
    
    messageElement.appendChild(senderElement);
    messageElement.appendChild(contentElement);
    
    messagesContainer.appendChild(messageElement);
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Show error message
function showError(message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    
    document.body.appendChild(errorElement);
    
    setTimeout(() => {
        errorElement.classList.add('show');
        
        setTimeout(() => {
            errorElement.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(errorElement);
            }, 300);
        }, 3000);
    }, 10);
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', init); 