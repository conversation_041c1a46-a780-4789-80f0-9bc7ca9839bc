"""
Tavily-powered search tools for GAAPF.

This module implements comprehensive search capabilities through Tavily API integration,
enabling real-time framework discovery and documentation access as specified in the methodology.
"""

import json
import os
from typing import Dict, List, Optional, Any
from pathlib import Path

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

try:
    from tavily import TavilyClient
    TAVILY_AVAILABLE = True
except ImportError:
    TAVILY_AVAILABLE = False


class TavilySearchInput(BaseModel):
    """Input schema for Tavily search tool."""
    query: str = Field(description="Search query for framework documentation and information")
    max_results: int = Field(default=5, description="Maximum number of results to return")
    include_domains: Optional[List[str]] = Field(default=None, description="Specific domains to include in search")


class TavilyExtractInput(BaseModel):
    """Input schema for Tavily content extraction tool."""
    url: str = Field(description="URL to extract detailed content from")


class TavilyCrawlInput(BaseModel):
    """Input schema for Tavily site crawling tool."""
    domain: str = Field(description="Domain to crawl for comprehensive information")
    max_pages: int = Field(default=10, description="Maximum number of pages to crawl")


class TavilySearchTool(BaseTool):
    """Tool for AI-powered search using Tavily API."""

    name: str = "tavily_search"
    description: str = """Search for framework documentation, tutorials, and information using AI-powered search.
    This tool provides real-time access to the latest documentation, tutorials, and community resources
    for Python frameworks. Use this when you need current information that might not be in the local database."""
    args_schema: type = TavilySearchInput

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = None
        if TAVILY_AVAILABLE:
            api_key = os.getenv("TAVILY_API_KEY")
            if api_key:
                self.client = TavilyClient(api_key=api_key)

    def _run(self, query: str, max_results: int = 5, include_domains: Optional[List[str]] = None) -> str:
        """Search using Tavily API."""
        if not self.client:
            return "Tavily search is not available. Please check your TAVILY_API_KEY configuration."

        try:
            # Enhance query for framework-specific searches
            enhanced_query = f"{query} Python framework documentation tutorial"
            
            # Configure search parameters
            search_params = {
                "query": enhanced_query,
                "max_results": max_results,
                "search_depth": "advanced",
                "include_answer": True,
                "include_raw_content": False
            }
            
            if include_domains:
                search_params["include_domains"] = include_domains

            # Perform search
            response = self.client.search(**search_params)
            
            # Format results
            results = {
                "query": query,
                "enhanced_query": enhanced_query,
                "answer": response.get("answer", ""),
                "results": []
            }
            
            for result in response.get("results", []):
                formatted_result = {
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0.0)
                }
                results["results"].append(formatted_result)
            
            return json.dumps(results, indent=2)
            
        except Exception as e:
            return f"Error performing Tavily search: {str(e)}"


class TavilyExtractTool(BaseTool):
    """Tool for extracting detailed content from URLs using Tavily."""

    name: str = "tavily_extract"
    description: str = """Extract detailed content from a specific URL using Tavily's content extraction.
    Use this when you need to get comprehensive information from a specific documentation page or tutorial."""
    args_schema: type = TavilyExtractInput

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = None
        if TAVILY_AVAILABLE:
            api_key = os.getenv("TAVILY_API_KEY")
            if api_key:
                self.client = TavilyClient(api_key=api_key)

    def _run(self, url: str) -> str:
        """Extract content from URL using Tavily."""
        if not self.client:
            return "Tavily extract is not available. Please check your TAVILY_API_KEY configuration."

        try:
            # Use Tavily's extract method
            response = self.client.extract(url)
            
            result = {
                "url": url,
                "title": response.get("title", ""),
                "content": response.get("content", ""),
                "raw_content": response.get("raw_content", "")
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error extracting content from {url}: {str(e)}"


class TavilyCrawlTool(BaseTool):
    """Tool for crawling websites to discover comprehensive information."""

    name: str = "tavily_crawl"
    description: str = """Crawl a website domain to discover comprehensive information and documentation.
    Use this when you need to explore a framework's official documentation site systematically."""
    args_schema: type = TavilyCrawlInput

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = None
        if TAVILY_AVAILABLE:
            api_key = os.getenv("TAVILY_API_KEY")
            if api_key:
                self.client = TavilyClient(api_key=api_key)

    def _run(self, domain: str, max_pages: int = 10) -> str:
        """Crawl domain for comprehensive information."""
        if not self.client:
            return "Tavily crawl is not available. Please check your TAVILY_API_KEY configuration."

        try:
            # Perform multiple targeted searches for the domain
            search_queries = [
                f"site:{domain} getting started tutorial",
                f"site:{domain} API documentation",
                f"site:{domain} examples",
                f"site:{domain} advanced features"
            ]
            
            all_results = []
            for query in search_queries:
                try:
                    response = self.client.search(
                        query=query,
                        max_results=max_pages // len(search_queries),
                        search_depth="basic"
                    )
                    
                    for result in response.get("results", []):
                        if domain in result.get("url", ""):
                            all_results.append({
                                "query": query,
                                "title": result.get("title", ""),
                                "url": result.get("url", ""),
                                "content": result.get("content", "")[:500] + "..." if len(result.get("content", "")) > 500 else result.get("content", ""),
                                "score": result.get("score", 0.0)
                            })
                except Exception as e:
                    continue
            
            # Remove duplicates based on URL
            unique_results = []
            seen_urls = set()
            for result in all_results:
                if result["url"] not in seen_urls:
                    unique_results.append(result)
                    seen_urls.add(result["url"])
            
            # Sort by score
            unique_results.sort(key=lambda x: x["score"], reverse=True)
            
            result = {
                "domain": domain,
                "pages_found": len(unique_results),
                "pages": unique_results[:max_pages]
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error crawling domain {domain}: {str(e)}"


class FrameworkSpecificSearchTool(BaseTool):
    """Tool for framework-specific searches with enhanced context."""

    name: str = "framework_specific_search"
    description: str = """Search for information specific to a particular Python framework with enhanced context.
    This tool automatically adds framework-specific context to searches for more relevant results."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.client = None
        if TAVILY_AVAILABLE:
            api_key = os.getenv("TAVILY_API_KEY")
            if api_key:
                self.client = TavilyClient(api_key=api_key)

    def _run(self, query: str, framework: str, search_type: str = "general") -> str:
        """Search with framework-specific context."""
        if not self.client:
            return "Framework-specific search is not available. Please check your TAVILY_API_KEY configuration."

        try:
            # Framework-specific search enhancements
            framework_contexts = {
                "langchain": "LangChain Python framework LLM applications",
                "langgraph": "LangGraph stateful multi-agent applications",
                "crewai": "CrewAI multi-agent systems",
                "autogen": "AutoGen conversational AI agents"
            }
            
            search_type_contexts = {
                "tutorial": "tutorial guide how-to step-by-step",
                "documentation": "official documentation API reference",
                "examples": "code examples sample implementation",
                "troubleshooting": "error debugging troubleshooting fix"
            }
            
            # Build enhanced query
            framework_context = framework_contexts.get(framework.lower(), f"{framework} Python framework")
            type_context = search_type_contexts.get(search_type, "")
            
            enhanced_query = f"{query} {framework_context} {type_context}".strip()
            
            # Perform search
            response = self.client.search(
                query=enhanced_query,
                max_results=5,
                search_depth="advanced",
                include_answer=True
            )
            
            result = {
                "original_query": query,
                "framework": framework,
                "search_type": search_type,
                "enhanced_query": enhanced_query,
                "answer": response.get("answer", ""),
                "results": response.get("results", [])
            }
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Error performing framework-specific search: {str(e)}"


def create_tavily_tools() -> List[BaseTool]:
    """Create all Tavily-powered search tools."""
    if not TAVILY_AVAILABLE:
        return []
    
    return [
        TavilySearchTool(),
        TavilyExtractTool(),
        TavilyCrawlTool(),
        FrameworkSpecificSearchTool()
    ]
