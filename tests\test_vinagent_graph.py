"""
Comprehensive tests for vinagent Graph functionality.

This module tests:
- StateGraph initialization and configuration
- FunctionStateGraph functionality
- Graph compilation and execution
- Node and edge management
- Flow definition and execution
- Error handling and edge cases
"""

import pytest
import asyncio
from typing import Dict, Any, TypedDict, Annotated
from unittest.mock import Mock, AsyncMock, patch

from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables import RunnableConfig

# Import vinagent graph components
from vinagent.graph.state_graph import StateGraph
from vinagent.graph.function_graph import FunctionStateGraph
from vinagent.graph.graph import Graph, CompiledGraph
from vinagent.graph.node import NodeSpec
from vinagent.graph.branch import Branch
from vinagent.graph.operator import FlowStateGraph


# Test state schemas
class SimpleState(TypedDict):
    """Simple state for testing."""
    message: str
    count: int


class AdvancedState(TypedDict):
    """Advanced state with annotations for testing."""
    messages: Annotated[list, lambda x, y: x + y]  # Reducer function
    user_id: str
    session_data: dict


class ConfigSchema(TypedDict):
    """Configuration schema for testing."""
    temperature: float
    max_tokens: int


class TestStateGraph:
    """Test StateGraph functionality."""
    
    def test_state_graph_initialization(self):
        """Test basic StateGraph initialization."""
        graph = StateGraph(SimpleState)
        
        assert graph is not None
        assert graph.state_schema == SimpleState
        assert hasattr(graph, 'nodes')
        assert hasattr(graph, 'edges')
    
    def test_state_graph_with_config_schema(self):
        """Test StateGraph initialization with config schema."""
        graph = StateGraph(SimpleState, config_schema=ConfigSchema)
        
        assert graph is not None
        assert graph.state_schema == SimpleState
        assert graph.config_schema == ConfigSchema
    
    def test_add_node_to_state_graph(self):
        """Test adding nodes to StateGraph."""
        graph = StateGraph(SimpleState)
        
        def test_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": f"Processed: {state['message']}", "count": state["count"] + 1}
        
        graph.add_node("test_node", test_node)
        
        assert "test_node" in graph.nodes
        assert graph.nodes["test_node"].runnable is not None
    
    def test_add_edge_to_state_graph(self):
        """Test adding edges to StateGraph."""
        graph = StateGraph(SimpleState)
        
        def node1(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Node 1", "count": 1}
        
        def node2(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Node 2", "count": 2}
        
        graph.add_node("node1", node1)
        graph.add_node("node2", node2)
        graph.add_edge("node1", "node2")
        
        assert ("node1", "node2") in graph.edges
    
    def test_state_graph_compilation(self):
        """Test StateGraph compilation."""
        graph = StateGraph(SimpleState)
        
        def start_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Started", "count": 1}
        
        graph.add_node("start", start_node)
        graph.set_entry_point("start")
        graph.set_finish_point("start")
        
        compiled = graph.compile()
        
        assert compiled is not None
        assert isinstance(compiled, CompiledGraph)


class TestFunctionStateGraph:
    """Test FunctionStateGraph functionality."""
    
    def test_function_state_graph_initialization(self):
        """Test FunctionStateGraph initialization."""
        graph = FunctionStateGraph(SimpleState)
        
        assert graph is not None
        assert graph.state_schema == SimpleState
    
    def test_function_state_graph_with_config(self):
        """Test FunctionStateGraph with configuration."""
        graph = FunctionStateGraph(SimpleState, config_schema=ConfigSchema)
        
        assert graph is not None
        assert graph.state_schema == SimpleState
        assert graph.config_schema == ConfigSchema
    
    def test_function_graph_node_addition(self):
        """Test adding function nodes to FunctionStateGraph."""
        graph = FunctionStateGraph(SimpleState)
        
        async def async_node(state: SimpleState, config: RunnableConfig) -> Dict[str, Any]:
            return {"message": f"Async: {state['message']}", "count": state["count"] + 1}
        
        graph.add_node("async_node", async_node)
        
        assert "async_node" in graph.nodes
    
    def test_function_graph_with_checkpointer(self):
        """Test FunctionStateGraph with checkpointer."""
        graph = FunctionStateGraph(SimpleState)
        checkpointer = MemorySaver()
        
        def simple_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Checkpointed", "count": 1}
        
        graph.add_node("checkpoint_node", simple_node)
        graph.set_entry_point("checkpoint_node")
        graph.set_finish_point("checkpoint_node")
        
        compiled = graph.compile(checkpointer=checkpointer)
        
        assert compiled is not None
        assert compiled.checkpointer == checkpointer


class TestGraphExecution:
    """Test Graph execution and flow."""
    
    @pytest.fixture
    def simple_graph(self):
        """Create a simple graph for testing."""
        graph = StateGraph(SimpleState)
        
        def increment_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": f"Count: {state['count'] + 1}", "count": state["count"] + 1}
        
        def double_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": f"Doubled: {state['count'] * 2}", "count": state["count"] * 2}
        
        graph.add_node("increment", increment_node)
        graph.add_node("double", double_node)
        graph.add_edge("increment", "double")
        graph.set_entry_point("increment")
        graph.set_finish_point("double")
        
        return graph.compile()
    
    def test_graph_invoke(self, simple_graph):
        """Test synchronous graph invocation."""
        initial_state = {"message": "Start", "count": 1}
        
        result = simple_graph.invoke(initial_state)
        
        assert result is not None
        assert "count" in result
        assert result["count"] == 4  # (1 + 1) * 2 = 4
    
    @pytest.mark.asyncio
    async def test_graph_ainvoke(self, simple_graph):
        """Test asynchronous graph invocation."""
        initial_state = {"message": "Start", "count": 2}
        
        result = await simple_graph.ainvoke(initial_state)
        
        assert result is not None
        assert "count" in result
        assert result["count"] == 6  # (2 + 1) * 2 = 6
    
    def test_graph_with_config(self, simple_graph):
        """Test graph execution with configuration."""
        initial_state = {"message": "Start", "count": 1}
        config = {"configurable": {"temperature": 0.5, "max_tokens": 100}}
        
        result = simple_graph.invoke(initial_state, config=config)
        
        assert result is not None
        assert "count" in result


class TestGraphBranching:
    """Test Graph branching and conditional flows."""
    
    def test_conditional_branching(self):
        """Test conditional branching in graphs."""
        graph = StateGraph(SimpleState)
        
        def decision_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Decision made", "count": state["count"]}
        
        def path_a(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Path A", "count": state["count"] + 10}
        
        def path_b(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Path B", "count": state["count"] + 20}
        
        def route_function(state: SimpleState) -> str:
            return "path_a" if state["count"] < 5 else "path_b"
        
        graph.add_node("decision", decision_node)
        graph.add_node("path_a", path_a)
        graph.add_node("path_b", path_b)
        
        graph.add_conditional_edges("decision", route_function, {
            "path_a": "path_a",
            "path_b": "path_b"
        })
        
        graph.set_entry_point("decision")
        graph.set_finish_point("path_a")
        graph.set_finish_point("path_b")
        
        compiled = graph.compile()
        
        # Test path A (count < 5)
        result_a = compiled.invoke({"message": "Start", "count": 3})
        assert result_a["count"] == 13  # 3 + 10
        
        # Test path B (count >= 5)
        result_b = compiled.invoke({"message": "Start", "count": 7})
        assert result_b["count"] == 27  # 7 + 20


class TestFlowStateGraph:
    """Test FlowStateGraph functionality."""
    
    def test_flow_state_graph_initialization(self):
        """Test FlowStateGraph initialization."""
        try:
            graph = FlowStateGraph(SimpleState)
            assert graph is not None
        except (ImportError, AttributeError):
            # FlowStateGraph might not be available in all versions
            pytest.skip("FlowStateGraph not available")
    
    def test_flow_definition(self):
        """Test flow definition in FlowStateGraph."""
        try:
            graph = FlowStateGraph(SimpleState)
            
            def node1(state: SimpleState) -> Dict[str, Any]:
                return {"message": "Node 1", "count": 1}
            
            def node2(state: SimpleState) -> Dict[str, Any]:
                return {"message": "Node 2", "count": 2}
            
            graph.add_node("node1", node1)
            graph.add_node("node2", node2)
            
            # Define flow
            flow = ["node1", "node2"]
            compiled = graph.compile(flow=flow)
            
            assert compiled is not None
        except (ImportError, AttributeError):
            pytest.skip("FlowStateGraph not available")


class TestGraphErrorHandling:
    """Test Graph error handling and edge cases."""
    
    def test_graph_with_invalid_node(self):
        """Test graph behavior with invalid node functions."""
        graph = StateGraph(SimpleState)
        
        def invalid_node(state: SimpleState) -> Dict[str, Any]:
            raise ValueError("Node error")
        
        graph.add_node("invalid", invalid_node)
        graph.set_entry_point("invalid")
        graph.set_finish_point("invalid")
        
        compiled = graph.compile()
        
        with pytest.raises(ValueError):
            compiled.invoke({"message": "Test", "count": 1})
    
    def test_graph_with_missing_entry_point(self):
        """Test graph compilation without entry point."""
        graph = StateGraph(SimpleState)
        
        def test_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Test", "count": 1}
        
        graph.add_node("test", test_node)
        
        # Should handle missing entry point gracefully or raise appropriate error
        try:
            compiled = graph.compile()
            # If compilation succeeds, test should pass
            assert compiled is not None
        except Exception as e:
            # If compilation fails, it should be a meaningful error
            assert isinstance(e, (ValueError, RuntimeError))
    
    def test_graph_with_circular_dependencies(self):
        """Test graph with circular dependencies."""
        graph = StateGraph(SimpleState)
        
        def node1(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Node 1", "count": state["count"] + 1}
        
        def node2(state: SimpleState) -> Dict[str, Any]:
            return {"message": "Node 2", "count": state["count"] + 1}
        
        graph.add_node("node1", node1)
        graph.add_node("node2", node2)
        graph.add_edge("node1", "node2")
        graph.add_edge("node2", "node1")  # Circular dependency
        
        # Should handle circular dependencies appropriately
        try:
            compiled = graph.compile()
            assert compiled is not None
        except Exception as e:
            # If it raises an error, it should be meaningful
            assert isinstance(e, (ValueError, RuntimeError))
    
    @pytest.mark.asyncio
    async def test_async_node_error_handling(self):
        """Test error handling in async nodes."""
        graph = StateGraph(SimpleState)
        
        async def failing_async_node(state: SimpleState) -> Dict[str, Any]:
            raise RuntimeError("Async node failed")
        
        graph.add_node("failing", failing_async_node)
        graph.set_entry_point("failing")
        graph.set_finish_point("failing")
        
        compiled = graph.compile()
        
        with pytest.raises(RuntimeError):
            await compiled.ainvoke({"message": "Test", "count": 1})


class TestGraphStreaming:
    """Test Graph streaming functionality."""
    
    @pytest.mark.asyncio
    async def test_graph_streaming(self):
        """Test graph streaming capabilities."""
        graph = StateGraph(SimpleState)
        
        def streaming_node(state: SimpleState) -> Dict[str, Any]:
            return {"message": f"Streamed: {state['message']}", "count": state["count"] + 1}
        
        graph.add_node("stream", streaming_node)
        graph.set_entry_point("stream")
        graph.set_finish_point("stream")
        
        compiled = graph.compile()
        
        # Test streaming if available
        if hasattr(compiled, 'astream'):
            chunks = []
            async for chunk in compiled.astream({"message": "Test", "count": 1}):
                chunks.append(chunk)
            
            assert len(chunks) > 0
        else:
            # If streaming not available, just test regular invocation
            result = await compiled.ainvoke({"message": "Test", "count": 1})
            assert result is not None
