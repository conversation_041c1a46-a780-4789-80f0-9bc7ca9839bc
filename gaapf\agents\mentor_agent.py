"""
Mentor agent implementation for GAAPF.

This agent specializes in providing personalized guidance, support,
and encouragement throughout the learning journey.
"""

from typing import Dict, List, Optional, Any
import re

from gaapf.agents.base_agent import BaseGAAPFAgent


class MentorAgent(BaseGAAPFAgent):
    """
    Mentor agent that provides personalized guidance and support.

    This agent specializes in:
    - Providing personalized learning guidance
    - Offering encouragement and motivation
    - Helping with learning strategy and planning
    - Supporting through challenges and difficulties
    - Adapting to individual learning needs
    """

    def _get_agent_description(self) -> str:
        """Get the agent's description for the system prompt."""
        return (
            f"You are a supportive {self.framework} learning mentor and guide. Your role is to provide "
            "personalized guidance, encouragement, and strategic support throughout the learning journey. "
            "You excel at understanding individual learning needs, providing motivation during challenges, "
            "and helping learners develop effective study strategies. You're empathetic, patient, and "
            f"adapt your guidance to the user's skill level ({self.user_profile.get('skill_level', 'intermediate')}) "
            f"and learning style ({self.user_profile.get('learning_style', 'mixed')}). You focus on building "
            "confidence and maintaining motivation while providing practical learning advice."
        )

    def _get_agent_skills(self) -> List[str]:
        """Get the agent's skills list."""
        return [
            f"Personalized {self.framework} learning guidance",
            "Providing encouragement and motivation",
            "Developing effective learning strategies",
            "Supporting through learning challenges",
            "Adapting to individual learning needs",
            "Building learner confidence",
            "Creating personalized learning plans",
            "Offering emotional support and patience"
        ]

    def _analyze_content_for_handoff(self, content: str, user_message: str) -> Dict[str, Any]:
        """
        Analyze content to determine if handoff to another agent is needed.

        Args:
            content: Agent's response content
            user_message: Original user message

        Returns:
            Dictionary with handoff analysis results
        """
        # Mentor agent typically provides guidance and support
        # Handoff to other agents when specific technical help is needed

        handoff_patterns = {
            "code_assistant": [
                r"code\s+example", r"implementation", r"syntax", r"debug",
                r"write\s+code", r"programming", r"script", r"function"
            ],
            "documentation_expert": [
                r"documentation", r"official\s+docs", r"api\s+reference",
                r"specification", r"manual"
            ],
            "practice_facilitator": [
                r"practice", r"exercise", r"hands.?on", r"tutorial",
                r"step.?by.?step", r"walkthrough"
            ],
            "instructor": [
                r"explain", r"concept", r"theory", r"understand",
                r"how\s+does", r"what\s+is", r"definition"
            ]
        }

        # Check if user message indicates need for specific expertise
        user_lower = user_message.lower()
        for agent_type, patterns in handoff_patterns.items():
            for pattern in patterns:
                if re.search(pattern, user_lower):
                    return {
                        "needs_handoff": True,
                        "suggested_agent": agent_type,
                        "confidence": 0.7,
                        "reason": f"User request matches {agent_type} expertise pattern: {pattern}"
                    }

        # Check if the mentor's response suggests another agent would be better
        content_lower = content.lower()
        if any(phrase in content_lower for phrase in ["let me get", "i'll connect you", "specialist", "expert"]):
            return {
                "needs_handoff": True,
                "suggested_agent": "instructor",  # Default fallback
                "confidence": 0.6,
                "reason": "Mentor response suggests specialist help needed"
            }

        # No handoff needed - mentor can handle this
        return {
            "needs_handoff": False,
            "suggested_agent": None,
            "confidence": 0.8,
            "reason": "Mentor can provide appropriate guidance and support"
        }

    def get_confidence_score(self, message: str) -> float:
        """
        Get confidence score for handling a specific message.

        Args:
            message: User message

        Returns:
            Confidence score between 0.0 and 1.0
        """
        message_lower = message.lower()

        # High confidence for mentoring and guidance requests
        high_confidence_patterns = [
            r"help\s+me", r"guidance", r"advice", r"support", r"stuck",
            r"struggling", r"motivation", r"encourage", r"strategy",
            r"plan", r"approach", r"overwhelmed", r"confused"
        ]

        for pattern in high_confidence_patterns:
            if re.search(pattern, message_lower):
                return 0.9

        # Medium confidence for general learning questions
        medium_confidence_patterns = [
            r"learn", r"study", r"practice", r"improve", r"better",
            r"recommend", r"suggest", r"should\s+i", r"how\s+to"
        ]

        for pattern in medium_confidence_patterns:
            if re.search(pattern, message_lower):
                return 0.7

        # Lower confidence for technical specifics
        low_confidence_patterns = [
            r"code", r"syntax", r"implementation", r"debug", r"error",
            r"api", r"documentation", r"reference"
        ]

        for pattern in low_confidence_patterns:
            if re.search(pattern, message_lower):
                return 0.3

        # Default confidence for general questions
        return 0.6