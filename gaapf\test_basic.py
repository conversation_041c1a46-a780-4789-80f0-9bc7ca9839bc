#!/usr/bin/env python3
"""
Basic test script for GAAPF system.

This script tests the core functionality of the GAAPF system
to ensure all components are working together properly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import gaapf
sys.path.insert(0, str(Path(__file__).parent.parent))

from gaapf.core.learning_hub import LearningHubCore
from gaapf.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from gaapf.config.framework_configs import SupportedFrameworks

# Mock LLM for testing
class MockLLM:
    """Mock language model for testing."""
    
    def __init__(self):
        self.model_name = "mock-llm"
        
    async def ainvoke(self, messages):
        """Mock async invoke method."""
        class MockResponse:
            def __init__(self, content):
                self.content = content
                
        return MockResponse("This is a mock response from the LLM.")
    
    def invoke(self, messages):
        """Mock sync invoke method."""
        class MockResponse:
            def __init__(self, content):
                self.content = content
                
        return MockResponse("This is a mock response from the LLM.")


async def test_basic_functionality():
    """Test basic GAAPF functionality."""
    print("🚀 Starting GAAPF Basic Test")
    
    # Create test data directory
    test_data_path = Path("test_data")
    test_data_path.mkdir(exist_ok=True)
    
    try:
        # Initialize Learning Hub
        print("📚 Initializing Learning Hub...")
        hub = LearningHubCore(data_path=test_data_path)
        hub.llm = MockLLM()
        await hub.initialize()
        print("✅ Learning Hub initialized successfully")
        
        # Create test user profile
        print("👤 Creating test user profile...")
        test_profile = UserProfile(
            user_id="test_user",
            programming_experience_years=3,
            python_skill_level=SkillLevel.INTERMEDIATE,
            learning_pace=LearningPace.MODERATE,
            preferred_learning_style=LearningStyle.HANDS_ON,
            learning_goals=["Learn LangChain", "Build AI applications"]
        )
        
        await hub.save_user_profile(test_profile)
        print("✅ Test user profile created successfully")
        
        # Test profile retrieval
        print("🔍 Testing profile retrieval...")
        retrieved_profile = await hub.get_user_profile("test_user")
        if retrieved_profile and retrieved_profile.user_id == "test_user":
            print("✅ Profile retrieval successful")
        else:
            print("❌ Profile retrieval failed")
            return False
        
        # Test session creation
        print("🎯 Testing session creation...")
        try:
            session_id = await hub.create_session(
                user_id="test_user",
                framework=SupportedFrameworks.LANGCHAIN.value,
                module_id="lc_basics",
                llm=MockLLM()
            )
            print(f"✅ Session created successfully: {session_id}")
        except Exception as e:
            print(f"❌ Session creation failed: {e}")
            return False
        
        # Test message processing
        print("💬 Testing message processing...")
        try:
            result = await hub.process_message(
                user_id="test_user",
                message="What is LangChain?",
                session_id=session_id
            )
            
            if result and "response" in result:
                print("✅ Message processing successful")
                print(f"📝 Response: {result['response'][:100]}...")
            else:
                print("❌ Message processing failed - no response")
                return False
        except Exception as e:
            print(f"❌ Message processing failed: {e}")
            return False
        
        # Test session ending
        print("🏁 Testing session ending...")
        try:
            summary = await hub.end_session(session_id)
            if summary and "session_id" in summary:
                print("✅ Session ended successfully")
            else:
                print("❌ Session ending failed")
                return False
        except Exception as e:
            print(f"❌ Session ending failed: {e}")
            return False
        
        # Test progress retrieval
        print("📊 Testing progress retrieval...")
        try:
            progress = await hub.get_learning_progress("test_user")
            if progress and "total_sessions" in progress:
                print("✅ Progress retrieval successful")
                print(f"📈 Total sessions: {progress['total_sessions']}")
            else:
                print("❌ Progress retrieval failed")
                return False
        except Exception as e:
            print(f"❌ Progress retrieval failed: {e}")
            return False
        
        print("\n🎉 All tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    
    finally:
        # Cleanup test data
        import shutil
        if test_data_path.exists():
            shutil.rmtree(test_data_path)
            print("🧹 Test data cleaned up")


async def test_agent_creation():
    """Test agent creation and basic functionality."""
    print("\n🤖 Testing Agent Creation")
    
    try:
        from gaapf.agents.base_agent import create_agent
        
        # Test creating different agent types
        agent_types = ["instructor", "code_assistant", "documentation_expert", "practice_facilitator", "mentor"]
        
        for agent_type in agent_types:
            print(f"🔧 Creating {agent_type} agent...")
            try:
                agent = await create_agent(
                    agent_type=agent_type,
                    user_profile={"user_id": "test_user", "skill_level": "intermediate"},
                    framework="langchain",
                    module_id="test_module",
                    session_id="test_session",
                    llm=MockLLM(),
                    is_primary=True
                )
                
                if agent:
                    print(f"✅ {agent_type} agent created successfully")
                    
                    # Test confidence scoring
                    confidence = agent.get_confidence_score("What is LangChain?")
                    print(f"📊 Confidence score: {confidence}")
                else:
                    print(f"❌ {agent_type} agent creation failed")
                    return False
                    
            except Exception as e:
                print(f"❌ {agent_type} agent creation failed: {e}")
                return False
        
        print("✅ All agent types created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Agent creation test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🧪 GAAPF System Test Suite")
    print("=" * 50)
    
    # Run basic functionality tests
    basic_test_passed = await test_basic_functionality()
    
    # Run agent creation tests
    agent_test_passed = await test_agent_creation()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"Basic Functionality: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"Agent Creation: {'✅ PASSED' if agent_test_passed else '❌ FAILED'}")
    
    if basic_test_passed and agent_test_passed:
        print("\n🎉 All tests passed! GAAPF system is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
