"""
Framework initialization service for GAAPF.

This module handles loading framework configurations from DEFAULT_FRAMEWORK_CONFIGS
and storing them in the database during application startup.
"""

import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, ClassVar

from gaapf.config.framework_configs import DEFAULT_FRAMEWORK_CONFIGS, SupportedFrameworks
from gaapf.database.models import DatabaseManager

logger = logging.getLogger(__name__)


class FrameworkInitializationService:
    """
    Singleton service responsible for initializing framework data in the database.

    This service:
    1. Loads framework configurations from DEFAULT_FRAMEWORK_CONFIGS
    2. Stores them in the database for agent access
    3. Handles updates and synchronization
    4. Provides initialization status reporting
    5. Implements singleton pattern to prevent duplicate initialization
    """

    _instance: ClassVar[Optional['FrameworkInitializationService']] = None
    _lock: ClassVar[threading.Lock] = threading.Lock()
    _initialized_frameworks: ClassVar[Dict[str, bool]] = {}

    def __new__(cls, data_path: Path):
        """Ensure singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, data_path: Path):
        """
        Initialize the framework initialization service.

        Args:
            data_path: Path to the data directory
        """
        # Only initialize once
        if hasattr(self, '_is_initialized'):
            return

        self.data_path = data_path
        self.db_path = data_path / "frameworks.db"
        self.db_manager = DatabaseManager(self.db_path)
        self._is_initialized = True
        
    async def initialize_frameworks(self, force_reinit: bool = False) -> Dict[str, bool]:
        """
        Initialize all framework configurations in the database.

        Args:
            force_reinit: If True, force re-initialization even if already done

        Returns:
            Dictionary mapping framework IDs to initialization success status
        """
        with self._lock:
            # Check if already initialized and not forcing re-init
            if not force_reinit and self._initialized_frameworks:
                logger.info("Frameworks already initialized, skipping...")
                return self._initialized_frameworks.copy()

        logger.info("Starting framework initialization...")

        results = {}

        for framework_enum, config in DEFAULT_FRAMEWORK_CONFIGS.items():
            framework_id = framework_enum.value

            # Check if this specific framework is already initialized
            if not force_reinit and framework_id in self._initialized_frameworks:
                results[framework_id] = self._initialized_frameworks[framework_id]
                logger.info(f"Framework {config.name} already initialized, skipping...")
                continue

            try:
                logger.info(f"Initializing framework: {config.name}")

                # Store the framework configuration
                success = self.db_manager.store_framework_config(config)
                results[framework_id] = success

                if success:
                    logger.info(f"✅ Successfully initialized {config.name}")
                    logger.info(f"   - {len(config.modules)} modules loaded")
                    logger.info(f"   - {len(config.learning_paths)} learning paths configured")

                    # Cache the successful initialization
                    self._initialized_frameworks[framework_id] = True
                else:
                    logger.error(f"❌ Failed to initialize {config.name}")
                    self._initialized_frameworks[framework_id] = False

            except Exception as e:
                logger.error(f"❌ Error initializing {config.name}: {e}")
                results[framework_id] = False
                self._initialized_frameworks[framework_id] = False

        # Log summary
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"Framework initialization complete: {successful}/{total} frameworks initialized")

        return results
    
    def get_initialization_status(self) -> Dict[str, any]:
        """
        Get the current initialization status of frameworks.
        
        Returns:
            Dictionary with initialization status information
        """
        try:
            # Check if database exists and has data
            if not self.db_path.exists():
                return {
                    "initialized": False,
                    "database_exists": False,
                    "frameworks_count": 0,
                    "modules_count": 0,
                    "message": "Database not found - initialization required"
                }
            
            # Get framework and module counts
            frameworks = self.db_manager.get_all_frameworks()
            frameworks_count = len(frameworks)
            
            total_modules = 0
            for framework in frameworks:
                modules = self.db_manager.get_framework_modules(framework['framework_id'])
                total_modules += len(modules)
            
            return {
                "initialized": frameworks_count > 0,
                "database_exists": True,
                "frameworks_count": frameworks_count,
                "modules_count": total_modules,
                "frameworks": [f['name'] for f in frameworks],
                "message": f"Initialized with {frameworks_count} frameworks and {total_modules} modules"
            }
            
        except Exception as e:
            logger.error(f"Error checking initialization status: {e}")
            return {
                "initialized": False,
                "database_exists": self.db_path.exists(),
                "frameworks_count": 0,
                "modules_count": 0,
                "error": str(e),
                "message": "Error checking initialization status"
            }
    
    async def reinitialize_framework(self, framework_id: str) -> bool:
        """
        Reinitialize a specific framework.
        
        Args:
            framework_id: Framework identifier to reinitialize
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Find the framework configuration
            framework_enum = SupportedFrameworks(framework_id)
            if framework_enum not in DEFAULT_FRAMEWORK_CONFIGS:
                logger.error(f"Framework {framework_id} not found in DEFAULT_FRAMEWORK_CONFIGS")
                return False
            
            config = DEFAULT_FRAMEWORK_CONFIGS[framework_enum]
            logger.info(f"Reinitializing framework: {config.name}")
            
            # Store the framework configuration
            success = self.db_manager.store_framework_config(config)
            
            if success:
                logger.info(f"✅ Successfully reinitialized {config.name}")
            else:
                logger.error(f"❌ Failed to reinitialize {config.name}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error reinitializing framework {framework_id}: {e}")
            return False
    
    def validate_framework_data(self) -> Dict[str, any]:
        """
        Validate the integrity of framework data in the database.
        
        Returns:
            Dictionary with validation results
        """
        try:
            validation_results = {
                "valid": True,
                "issues": [],
                "frameworks_validated": 0,
                "modules_validated": 0
            }
            
            # Get all frameworks from database
            frameworks = self.db_manager.get_all_frameworks()
            
            for framework in frameworks:
                framework_id = framework['framework_id']
                validation_results["frameworks_validated"] += 1
                
                # Check if framework exists in DEFAULT_FRAMEWORK_CONFIGS
                try:
                    framework_enum = SupportedFrameworks(framework_id)
                    if framework_enum not in DEFAULT_FRAMEWORK_CONFIGS:
                        validation_results["issues"].append(
                            f"Framework {framework_id} in database but not in DEFAULT_FRAMEWORK_CONFIGS"
                        )
                        validation_results["valid"] = False
                except ValueError:
                    validation_results["issues"].append(
                        f"Invalid framework ID in database: {framework_id}"
                    )
                    validation_results["valid"] = False
                    continue
                
                # Validate modules
                modules = self.db_manager.get_framework_modules(framework_id)
                validation_results["modules_validated"] += len(modules)
                
                # Check if modules match configuration
                config = DEFAULT_FRAMEWORK_CONFIGS[framework_enum]
                config_module_ids = {m.module_id for m in config.modules}
                db_module_ids = {m['module_id'] for m in modules}
                
                if config_module_ids != db_module_ids:
                    validation_results["issues"].append(
                        f"Module mismatch for {framework_id}: "
                        f"Config has {config_module_ids}, DB has {db_module_ids}"
                    )
                    validation_results["valid"] = False
            
            # Check for missing frameworks
            for framework_enum in DEFAULT_FRAMEWORK_CONFIGS:
                framework_id = framework_enum.value
                if not any(f['framework_id'] == framework_id for f in frameworks):
                    validation_results["issues"].append(
                        f"Framework {framework_id} in DEFAULT_FRAMEWORK_CONFIGS but not in database"
                    )
                    validation_results["valid"] = False
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating framework data: {e}")
            return {
                "valid": False,
                "issues": [f"Validation error: {str(e)}"],
                "frameworks_validated": 0,
                "modules_validated": 0
            }
    
    def is_framework_initialized(self, framework_id: str) -> bool:
        """
        Check if a specific framework is already initialized.

        Args:
            framework_id: Framework identifier to check

        Returns:
            True if framework is initialized, False otherwise
        """
        # Check in-memory cache first
        if framework_id in self._initialized_frameworks:
            return self._initialized_frameworks[framework_id]

        # Check database if not in cache
        framework_info = self.db_manager.get_framework_info(framework_id)
        is_initialized = framework_info is not None

        # Cache the result
        self._initialized_frameworks[framework_id] = is_initialized
        return is_initialized

    async def ensure_framework_initialized(self, framework_id: str) -> bool:
        """
        Ensure a specific framework is initialized.

        Args:
            framework_id: Framework identifier to ensure is initialized

        Returns:
            True if framework is initialized, False otherwise
        """
        if self.is_framework_initialized(framework_id):
            logger.debug(f"Framework {framework_id} already initialized")
            return True

        logger.info(f"Framework {framework_id} not initialized, initializing...")
        return await self.reinitialize_framework(framework_id)

    async def ensure_initialized(self) -> bool:
        """
        Ensure frameworks are initialized, initializing if necessary.

        Returns:
            True if frameworks are initialized, False otherwise
        """
        status = self.get_initialization_status()

        if status["initialized"]:
            logger.info(f"Frameworks already initialized: {status['message']}")
            # Update in-memory cache with database state
            frameworks = self.db_manager.get_all_frameworks()
            for framework in frameworks:
                self._initialized_frameworks[framework['framework_id']] = True
            return True

        logger.info("Frameworks not initialized, starting initialization...")
        results = await self.initialize_frameworks()

        # Check if initialization was successful
        successful_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        if successful_count == total_count:
            logger.info("✅ All frameworks initialized successfully")
            return True
        else:
            logger.warning(f"⚠️ Only {successful_count}/{total_count} frameworks initialized successfully")
            return successful_count > 0  # Return True if at least some frameworks were initialized

    @classmethod
    def get_instance(cls, data_path: Path = None) -> 'FrameworkInitializationService':
        """
        Get the singleton instance of the service.

        Args:
            data_path: Path to data directory (only used for first initialization)

        Returns:
            Singleton instance of FrameworkInitializationService
        """
        if cls._instance is None:
            if data_path is None:
                raise ValueError("data_path is required for first initialization")
            return cls(data_path)
        return cls._instance

    @classmethod
    def reset_instance(cls):
        """Reset the singleton instance (mainly for testing)."""
        with cls._lock:
            cls._instance = None
            cls._initialized_frameworks.clear()
