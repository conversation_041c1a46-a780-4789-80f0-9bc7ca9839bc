<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GAAPF - Guidance AI Agent for Python Framework</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>GAAPF</h1>
            <h2>Guidance AI Agent for Python Framework</h2>
        </header>
        
        <div id="setup-panel" class="panel">
            <h3>Start a New Learning Session</h3>
            <form id="session-form">
                <div class="form-group">
                    <label for="user-id">Your Name or ID:</label>
                    <input type="text" id="user-id" required>
                </div>
                
                <div class="form-group">
                    <label for="framework">Choose a Framework:</label>
                    <select id="framework" required>
                        <option value="" disabled selected>Select a framework</option>
                        <!-- Will be populated dynamically -->
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="skill-level">Your Skill Level:</label>
                    <select id="skill-level" required>
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Learning Goals:</label>
                    <div class="checkbox-group" id="learning-goals">
                        <!-- Will be populated dynamically based on framework -->
                    </div>
                </div>
                
                <button type="submit" class="btn primary">Start Learning</button>
            </form>
        </div>
        
        <div id="chat-panel" class="panel hidden">
            <div class="sidebar">
                <h3>Agents</h3>
                <ul id="agents-list">
                    <!-- Will be populated dynamically -->
                </ul>
                <button id="end-session" class="btn danger">End Session</button>
            </div>
            
            <div class="chat-container">
                <div id="messages" class="messages"></div>
                
                <form id="message-form" class="message-input">
                    <input type="text" id="message-input" placeholder="Type your message here..." required>
                    <button type="submit" class="btn primary">Send</button>
                </form>
            </div>
        </div>
    </div>
    
    <script src="js/app.js"></script>
</body>
</html> 